#!/bin/bash

# Pod库优化工具 - 测试运行器
# 功能: 运行所有单元测试，验证脚本功能的正确性
# 版本: v1.0
# 用法: ./test_runner.sh [选项]

set -e

# 测试目录配置
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$TEST_DIR")"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"
LIB_DIR="$SCRIPTS_DIR/lib"
MODULES_DIR="$SCRIPTS_DIR/modules"

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[SKIP]${NC} $1"
}

# 显示测试横幅
show_test_banner() {
    echo "=================================="
    echo "  Pod库优化工具 - 单元测试套件"
    echo "=================================="
    echo "测试目录: $TEST_DIR"
    echo "项目根目录: $PROJECT_ROOT"
    echo ""
}

# 运行单个测试文件
run_test_file() {
    local test_file="$1"
    local test_name=$(basename "$test_file" .sh)
    
    log_info "运行测试: $test_name"
    
    if [ ! -f "$test_file" ]; then
        log_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    if [ ! -x "$test_file" ]; then
        chmod +x "$test_file"
    fi
    
    # 创建临时测试环境
    local temp_test_dir=$(mktemp -d)
    export TEST_TEMP_DIR="$temp_test_dir"
    export TEST_PROJECT_ROOT="$PROJECT_ROOT"
    
    # 运行测试
    local test_output=$(mktemp)
    local test_result=0
    
    if "$test_file" > "$test_output" 2>&1; then
        log_success "$test_name 测试通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        test_result=$?
        log_error "$test_name 测试失败 (退出码: $test_result)"
        echo "测试输出:"
        cat "$test_output" | sed 's/^/  /'
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    # 清理
    rm -rf "$temp_test_dir" "$test_output"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    return $test_result
}

# 运行所有测试
run_all_tests() {
    log_info "开始运行所有测试..."
    echo ""
    
    # 按顺序运行测试
    local test_files=(
        "$TEST_DIR/test_common.sh"
        "$TEST_DIR/test_database.sh"
        "$TEST_DIR/test_image_processor.sh"
        "$TEST_DIR/test_unused_detector.sh"
        "$TEST_DIR/test_git_manager.sh"
        "$TEST_DIR/test_batch_optimizer.sh"
        "$TEST_DIR/test_selective_optimizer.sh"
        "$TEST_DIR/test_main_menu.sh"
        "$TEST_DIR/test_integration.sh"
    )
    
    for test_file in "${test_files[@]}"; do
        if [ -f "$test_file" ]; then
            run_test_file "$test_file"
            echo ""
        else
            log_warning "跳过不存在的测试: $(basename "$test_file")"
            SKIPPED_TESTS=$((SKIPPED_TESTS + 1))
            TOTAL_TESTS=$((TOTAL_TESTS + 1))
        fi
    done
}

# 显示测试结果摘要
show_test_summary() {
    echo "=================================="
    echo "           测试结果摘要"
    echo "=================================="
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    echo -e "跳过: ${YELLOW}$SKIPPED_TESTS${NC}"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}✓ 所有测试通过！${NC}"
        return 0
    else
        echo -e "${RED}✗ 有 $FAILED_TESTS 个测试失败${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Pod库优化工具测试运行器"
    echo ""
    echo "用法:"
    echo "  $0 [选项] [测试文件]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -v, --verbose  详细输出模式"
    echo "  -q, --quiet    静默模式"
    echo ""
    echo "示例:"
    echo "  $0                           # 运行所有测试"
    echo "  $0 test_common.sh           # 运行特定测试"
    echo "  $0 -v                       # 详细模式运行所有测试"
}

# 主函数
main() {
    show_test_banner
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -v|--verbose)
            export TEST_VERBOSE=1
            run_all_tests
            show_test_summary
            ;;
        -q|--quiet)
            export TEST_QUIET=1
            run_all_tests > /dev/null 2>&1
            show_test_summary
            ;;
        "")
            run_all_tests
            show_test_summary
            ;;
        *)
            # 运行特定测试文件
            local test_file="$TEST_DIR/$1"
            if [[ "$1" != *.sh ]]; then
                test_file="$TEST_DIR/$1.sh"
            fi
            run_test_file "$test_file"
            show_test_summary
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
