#!/bin/bash

# Pod库优化工具 - 选择性优化器测试
# 功能: 测试 selective_optimizer.sh 中的选择性优化功能
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 加载被测试的模块
PROJECT_ROOT="$TEST_PROJECT_ROOT"
source "$PROJECT_ROOT/scripts/lib/common.sh"
source "$PROJECT_ROOT/scripts/lib/database.sh"

# 检查选择性优化器模块是否存在
if [ -f "$PROJECT_ROOT/scripts/modules/selective_optimizer.sh" ]; then
    source "$PROJECT_ROOT/scripts/modules/selective_optimizer.sh"
else
    echo "警告: selective_optimizer.sh 模块不存在，将创建模拟测试"
fi

# 创建模拟Pod目录结构
create_mock_pod() {
    local pod_name="$1"
    local pod_dir="$2"
    local size_category="${3:-medium}"  # small, medium, large
    
    mkdir -p "$pod_dir/$pod_name/Classes"
    mkdir -p "$pod_dir/$pod_name/Resources"
    mkdir -p "$pod_dir/$pod_name/Assets.xcassets"
    
    # 创建Pod规范文件
    cat > "$pod_dir/$pod_name/$pod_name.podspec" << EOF
Pod::Spec.new do |s|
  s.name         = "$pod_name"
  s.version      = "1.0.0"
  s.summary      = "Test Pod - $size_category size"
  s.homepage     = "https://example.com"
  s.license      = "MIT"
  s.author       = "Test Author"
  s.source       = { :git => "https://github.com/test/$pod_name.git", :tag => "1.0.0" }
  s.source_files = "Classes/**/*.{h,m}"
  s.resources    = "Resources/**/*"
end
EOF
    
    # 创建不同大小的资源文件
    case "$size_category" in
        "small")
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/icon.png" bs=512 count=1 2>/dev/null
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/bg.jpg" bs=1024 count=1 2>/dev/null
            ;;
        "medium")
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/icon.png" bs=2048 count=1 2>/dev/null
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/bg.jpg" bs=4096 count=1 2>/dev/null
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/banner.png" bs=3072 count=1 2>/dev/null
            ;;
        "large")
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/icon.png" bs=8192 count=1 2>/dev/null
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/bg.jpg" bs=16384 count=1 2>/dev/null
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/banner.png" bs=12288 count=1 2>/dev/null
            dd if=/dev/zero of="$pod_dir/$pod_name/Resources/splash.png" bs=20480 count=1 2>/dev/null
            ;;
    esac
    
    # 创建代码文件
    echo "#import <Foundation/Foundation.h>" > "$pod_dir/$pod_name/Classes/$pod_name.h"
    echo "#import \"$pod_name.h\"" > "$pod_dir/$pod_name/Classes/$pod_name.m"
}

# 测试Pod列表生成
test_pod_list_generation() {
    test_case "测试Pod列表生成"
    
    setup_test_env "pod_list_test"
    
    # 创建不同大小的Pod
    create_mock_pod "SmallPod" "." "small"
    create_mock_pod "MediumPod" "." "medium"
    create_mock_pod "LargePod" "." "large"
    
    # 测试Pod发现
    local pod_list=""
    if command -v list_available_pods >/dev/null 2>&1; then
        pod_list=$(list_available_pods "." 2>/dev/null || echo "")
    else
        # 模拟Pod列表生成
        pod_list=$(find . -name "*.podspec" -exec dirname {} \; | sort)
    fi
    
    assert_contains "$pod_list" "SmallPod" "应该找到SmallPod"
    assert_contains "$pod_list" "MediumPod" "应该找到MediumPod"
    assert_contains "$pod_list" "LargePod" "应该找到LargePod"
    
    cleanup_test_env
}

# 测试Pod信息显示
test_pod_info_display() {
    test_case "测试Pod信息显示"
    
    setup_test_env "pod_info_test"
    
    # 创建测试Pod
    create_mock_pod "TestPod" "." "medium"
    
    # 测试Pod信息获取
    if command -v get_pod_info >/dev/null 2>&1; then
        local pod_info=$(get_pod_info "./TestPod" 2>/dev/null || echo "")
        assert_contains "$pod_info" "TestPod" "Pod信息应该包含Pod名称"
    else
        # 模拟Pod信息显示
        assert_file_exists "./TestPod/TestPod.podspec" "Podspec文件应该存在"
        assert_dir_exists "./TestPod/Resources" "Resources目录应该存在"
        
        # 计算资源大小
        local resource_size=$(find "./TestPod/Resources" -type f -exec stat -f%z {} \; 2>/dev/null | awk '{sum+=$1} END {print sum}' || echo "0")
        assert_true "$([ "$resource_size" -gt 0 ])" "应该有资源文件"
    fi
    
    cleanup_test_env
}

# 测试Pod选择逻辑
test_pod_selection_logic() {
    test_case "测试Pod选择逻辑"
    
    setup_test_env "pod_selection_test"
    
    # 创建多个Pod
    create_mock_pod "Pod1" "." "small"
    create_mock_pod "Pod2" "." "medium"
    create_mock_pod "Pod3" "." "large"
    
    # 模拟用户选择验证
    local valid_selections=("1" "2" "3" "1,2" "1,3" "2,3" "1,2,3" "all")
    local invalid_selections=("0" "4" "abc" "1,4" "")
    
    # 测试有效选择
    for selection in "${valid_selections[@]}"; do
        # 这里我们主要测试选择格式的有效性
        if [[ "$selection" =~ ^[0-9,]+$|^all$ ]]; then
            assert_true "true" "选择 '$selection' 应该是有效格式"
        else
            assert_true "false" "选择 '$selection' 应该是有效格式"
        fi
    done
    
    # 测试无效选择
    for selection in "${invalid_selections[@]}"; do
        if [[ "$selection" =~ ^[0-9,]+$|^all$ ]] && [ -n "$selection" ]; then
            assert_true "false" "选择 '$selection' 应该是无效的"
        else
            assert_true "true" "选择 '$selection' 应该是无效的"
        fi
    done
    
    cleanup_test_env
}

# 测试选择性优化执行
test_selective_optimization_execution() {
    test_case "测试选择性优化执行"
    
    setup_test_env "selective_exec_test"
    
    # 设置数据库
    export DB_PATH="$TEST_ENV_DIR/test.db"
    init_database
    
    # 创建测试Pod
    create_mock_pod "SelectedPod" "." "medium"
    
    # 设置备份目录
    local backup_dir="$TEST_ENV_DIR/backup"
    mkdir -p "$backup_dir"
    
    # 测试选择性优化（如果函数存在）
    if command -v selective_optimize_pods >/dev/null 2>&1; then
        # 由于选择性优化需要用户交互，我们主要测试函数存在性
        assert_true "true" "选择性优化函数应该存在"
    else
        # 模拟选择性优化逻辑
        local pod_dir="./SelectedPod"
        assert_dir_exists "$pod_dir" "选择的Pod目录应该存在"
        
        # 模拟优化过程
        local resource_files=$(find "$pod_dir/Resources" -type f 2>/dev/null | wc -l)
        assert_true "$([ "$resource_files" -gt 0 ])" "应该有资源文件可以优化"
    fi
    
    cleanup_test_env
}

# 测试优化预览功能
test_optimization_preview() {
    test_case "测试优化预览功能"
    
    setup_test_env "preview_test"
    
    # 创建测试Pod
    create_mock_pod "PreviewPod" "." "large"
    
    # 测试预览功能（如果存在）
    if command -v preview_optimization >/dev/null 2>&1; then
        local preview_output=$(preview_optimization "./PreviewPod" "compress" 2>/dev/null || echo "")
        assert_true "true" "预览功能应该正常执行"
    else
        # 模拟预览逻辑
        local pod_dir="./PreviewPod"
        local image_files=$(find "$pod_dir" -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | wc -l)
        assert_true "$([ "$image_files" -gt 0 ])" "应该找到可优化的图片文件"
        
        # 计算潜在节省空间
        local total_size=0
        while IFS= read -r -d '' file; do
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
            total_size=$((total_size + file_size))
        done < <(find "$pod_dir" -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -print0 2>/dev/null)
        
        assert_true "$([ "$total_size" -gt 0 ])" "应该有可优化的文件大小"
    fi
    
    cleanup_test_env
}

# 测试交互式菜单
test_interactive_menu() {
    test_case "测试交互式菜单"
    
    setup_test_env "menu_test"
    
    # 创建测试Pod
    create_mock_pod "MenuPod1" "." "small"
    create_mock_pod "MenuPod2" "." "medium"
    
    # 测试菜单选项验证
    local menu_options=("1" "2" "all" "q" "quit" "exit")
    
    for option in "${menu_options[@]}"; do
        case "$option" in
            [0-9]*|"all"|"q"|"quit"|"exit")
                assert_true "true" "菜单选项 '$option' 应该是有效的"
                ;;
            *)
                assert_true "false" "菜单选项 '$option' 应该是有效的"
                ;;
        esac
    done
    
    # 测试无效菜单选项
    local invalid_options=("abc" "-1" "100" "")
    
    for option in "${invalid_options[@]}"; do
        case "$option" in
            [0-9]*|"all"|"q"|"quit"|"exit")
                if [ -n "$option" ]; then
                    assert_true "false" "菜单选项 '$option' 应该是无效的"
                else
                    assert_true "true" "空选项应该是无效的"
                fi
            ;;
            *)
                assert_true "true" "菜单选项 '$option' 应该是无效的"
                ;;
        esac
    done
    
    cleanup_test_env
}

# 测试优化历史记录
test_optimization_history() {
    test_case "测试优化历史记录"
    
    setup_test_env "history_test"
    
    # 设置数据库
    export DB_PATH="$TEST_ENV_DIR/test.db"
    init_database
    
    # 记录一些选择性优化历史
    record_optimization "SelectedPod1" "compress" 2048 3
    record_optimization "SelectedPod2" "delete" 4096 5
    record_optimization "SelectedPod3" "mixed" 1024 2
    
    # 测试历史查询
    local history_output=$(show_optimization_history 2>&1)
    assert_contains "$history_output" "SelectedPod1" "历史应该包含SelectedPod1"
    assert_contains "$history_output" "SelectedPod2" "历史应该包含SelectedPod2"
    assert_contains "$history_output" "SelectedPod3" "历史应该包含SelectedPod3"
    
    # 验证优化类型记录
    assert_contains "$history_output" "compress" "历史应该包含compress类型"
    assert_contains "$history_output" "delete" "历史应该包含delete类型"
    assert_contains "$history_output" "mixed" "历史应该包含mixed类型"
    
    cleanup_test_env
}

# 测试错误恢复
test_error_recovery() {
    test_case "测试错误恢复"
    
    setup_test_env "error_recovery_test"
    
    # 创建测试Pod
    create_mock_pod "ErrorPod" "." "medium"
    
    # 模拟优化过程中的错误
    local backup_dir="$TEST_ENV_DIR/backup"
    mkdir -p "$backup_dir"
    
    # 备份原始文件
    backup_file_preserve_structure "./ErrorPod/Resources/icon.png" "$backup_dir"
    
    # 验证备份存在
    assert_file_exists "$backup_dir/ErrorPod/Resources/icon.png" "备份文件应该存在"
    
    # 模拟文件损坏
    echo "corrupted" > "./ErrorPod/Resources/icon.png"
    
    # 模拟恢复过程
    if [ -f "$backup_dir/ErrorPod/Resources/icon.png" ]; then
        cp "$backup_dir/ErrorPod/Resources/icon.png" "./ErrorPod/Resources/icon.png"
        assert_file_exists "./ErrorPod/Resources/icon.png" "文件应该被恢复"
        
        # 验证文件内容不是损坏的
        local file_content=$(head -c 10 "./ErrorPod/Resources/icon.png")
        assert_not_equals "corrupted" "$file_content" "文件内容应该被恢复"
    fi
    
    cleanup_test_env
}

# 主测试函数
main() {
    test_suite "选择性优化器测试"
    
    test_pod_list_generation
    test_pod_info_display
    test_pod_selection_logic
    test_selective_optimization_execution
    test_optimization_preview
    test_interactive_menu
    test_optimization_history
    test_error_recovery
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
