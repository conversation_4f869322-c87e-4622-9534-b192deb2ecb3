#!/bin/bash

# Pod库优化工具 - 测试框架
# 功能: 提供测试断言和工具函数
# 版本: v1.0

# 测试框架变量
TEST_CURRENT_SUITE=""
TEST_CURRENT_CASE=""
TEST_ASSERTIONS=0
TEST_FAILURES=0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试套件开始
test_suite() {
    TEST_CURRENT_SUITE="$1"
    TEST_ASSERTIONS=0
    TEST_FAILURES=0
    echo -e "${BLUE}=== 测试套件: $TEST_CURRENT_SUITE ===${NC}"
}

# 测试用例开始
test_case() {
    TEST_CURRENT_CASE="$1"
    echo -e "${YELLOW}--- 测试用例: $TEST_CURRENT_CASE ---${NC}"
}

# 断言函数
assert_equals() {
    local expected="$1"
    local actual="$2"
    local message="${3:-断言失败}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ "$expected" = "$actual" ]; then
        echo -e "  ${GREEN}✓${NC} $message"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message"
        echo -e "    期望: '$expected'"
        echo -e "    实际: '$actual'"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_not_equals() {
    local not_expected="$1"
    local actual="$2"
    local message="${3:-断言失败}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ "$not_expected" != "$actual" ]; then
        echo -e "  ${GREEN}✓${NC} $message"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message"
        echo -e "    不应该等于: '$not_expected'"
        echo -e "    实际: '$actual'"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_true() {
    local condition="$1"
    local message="${2:-断言失败}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ "$condition" = "true" ] || [ "$condition" = "0" ]; then
        echo -e "  ${GREEN}✓${NC} $message"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message"
        echo -e "    期望: true"
        echo -e "    实际: $condition"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_false() {
    local condition="$1"
    local message="${2:-断言失败}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ "$condition" = "false" ] || [ "$condition" != "0" ]; then
        echo -e "  ${GREEN}✓${NC} $message"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message"
        echo -e "    期望: false"
        echo -e "    实际: $condition"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_file_exists() {
    local file_path="$1"
    local message="${2:-文件应该存在}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ -f "$file_path" ]; then
        echo -e "  ${GREEN}✓${NC} $message: $file_path"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message: $file_path"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_file_not_exists() {
    local file_path="$1"
    local message="${2:-文件不应该存在}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ ! -f "$file_path" ]; then
        echo -e "  ${GREEN}✓${NC} $message: $file_path"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message: $file_path"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_dir_exists() {
    local dir_path="$1"
    local message="${2:-目录应该存在}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ -d "$dir_path" ]; then
        echo -e "  ${GREEN}✓${NC} $message: $dir_path"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message: $dir_path"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_dir_not_exists() {
    local dir_path="$1"
    local message="${2:-目录不应该存在}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [ ! -d "$dir_path" ]; then
        echo -e "  ${GREEN}✓${NC} $message: $dir_path"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message: $dir_path"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_contains() {
    local haystack="$1"
    local needle="$2"
    local message="${3:-字符串应该包含}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [[ "$haystack" == *"$needle"* ]]; then
        echo -e "  ${GREEN}✓${NC} $message: '$needle'"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message"
        echo -e "    在字符串中查找: '$needle'"
        echo -e "    字符串内容: '$haystack'"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

assert_not_contains() {
    local haystack="$1"
    local needle="$2"
    local message="${3:-字符串不应该包含}"
    
    TEST_ASSERTIONS=$((TEST_ASSERTIONS + 1))
    
    if [[ "$haystack" != *"$needle"* ]]; then
        echo -e "  ${GREEN}✓${NC} $message: '$needle'"
        return 0
    else
        echo -e "  ${RED}✗${NC} $message"
        echo -e "    不应该包含: '$needle'"
        echo -e "    字符串内容: '$haystack'"
        TEST_FAILURES=$((TEST_FAILURES + 1))
        return 1
    fi
}

# 创建测试环境
setup_test_env() {
    local test_name="$1"
    TEST_ENV_DIR="$TEST_TEMP_DIR/$test_name"
    mkdir -p "$TEST_ENV_DIR"
    cd "$TEST_ENV_DIR"
    
    # 创建模拟的Pod目录结构
    mkdir -p "TestPod/Resources"
    mkdir -p "TestPod/Classes"
    mkdir -p "TestPod.xcassets/test.imageset"
    
    # 创建测试文件
    echo "test content" > "TestPod/Resources/test.txt"
    echo "UIImage *img = [UIImage imageNamed:@\"test\"];" > "TestPod/Classes/TestClass.m"
    
    # 创建测试图片文件（模拟）
    echo "PNG_DATA" > "TestPod/Resources/test.png"
    echo "PNG_DATA" > "TestPod.xcassets/test.imageset/test.png"
    echo '{"images":[{"filename":"test.png"}]}' > "TestPod.xcassets/test.imageset/Contents.json"
}

# 清理测试环境
cleanup_test_env() {
    if [ -n "$TEST_ENV_DIR" ] && [ -d "$TEST_ENV_DIR" ]; then
        rm -rf "$TEST_ENV_DIR"
    fi
}

# 测试结果摘要
test_summary() {
    echo ""
    echo -e "${BLUE}=== 测试套件结果: $TEST_CURRENT_SUITE ===${NC}"
    echo "断言总数: $TEST_ASSERTIONS"
    
    if [ $TEST_FAILURES -eq 0 ]; then
        echo -e "结果: ${GREEN}通过${NC}"
        return 0
    else
        echo -e "失败数: ${RED}$TEST_FAILURES${NC}"
        echo -e "结果: ${RED}失败${NC}"
        return 1
    fi
}

# 模拟用户输入
mock_user_input() {
    local input="$1"
    echo "$input"
}

# 捕获命令输出
capture_output() {
    local cmd="$1"
    local output_file=$(mktemp)
    eval "$cmd" > "$output_file" 2>&1
    cat "$output_file"
    rm -f "$output_file"
}

# 检查命令是否成功
command_succeeds() {
    local cmd="$1"
    eval "$cmd" >/dev/null 2>&1
    return $?
}

# 检查命令是否失败
command_fails() {
    local cmd="$1"
    eval "$cmd" >/dev/null 2>&1
    [ $? -ne 0 ]
}
