#!/bin/bash

# Pod库优化工具 - 无用图片检测器测试
# 功能: 测试 unused_detector.sh 中的无引用图片检测和删除功能
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 加载被测试的模块
PROJECT_ROOT="$TEST_PROJECT_ROOT"
source "$PROJECT_ROOT/scripts/lib/common.sh"
source "$PROJECT_ROOT/scripts/lib/database.sh"
source "$PROJECT_ROOT/scripts/lib/unused_detector.sh"

# 创建模拟图片文件
create_mock_image() {
    local file_path="$1"
    local size="${2:-1024}"
    
    mkdir -p "$(dirname "$file_path")"
    dd if=/dev/zero of="$file_path" bs="$size" count=1 2>/dev/null
}

# 创建模拟代码文件
create_mock_code() {
    local file_path="$1"
    local content="$2"
    
    mkdir -p "$(dirname "$file_path")"
    echo "$content" > "$file_path"
}

# 测试动画序列帧检测
test_animation_sequence_detection() {
    test_case "测试动画序列帧检测"
    
    setup_test_env "animation_test"
    
    # 创建动画序列帧文件
    create_mock_image "Resources/ani_loading1.png" 512
    create_mock_image "Resources/ani_loading2.png" 512
    create_mock_image "Resources/ani_loading3.png" 512
    create_mock_image "Resources/ani_loading4.png" 512
    
    # 创建引用动画的代码
    create_mock_code "Classes/LoadingView.m" 'for (int i = 1; i <= 4; i++) {
        NSString *imageName = [NSString stringWithFormat:@"ani_loading%d", i];
        UIImage *image = [UIImage imageNamed:imageName];
    }'
    
    # 测试动画序列帧检测
    if is_animation_sequence_frame "Resources/ani_loading2.png" "."; then
        assert_true "true" "应该检测到动画序列帧"
    else
        assert_true "false" "应该检测到动画序列帧"
    fi
    
    # 测试非动画文件
    create_mock_image "Resources/single_icon.png" 512
    if is_animation_sequence_frame "Resources/single_icon.png" "."; then
        assert_true "false" "单独文件不应该被识别为动画序列帧"
    else
        assert_true "true" "单独文件不应该被识别为动画序列帧"
    fi
    
    cleanup_test_env
}

# 测试单个图片引用检测
test_single_image_reference() {
    test_case "测试单个图片引用检测"
    
    setup_test_env "reference_test"
    
    # 创建图片文件
    create_mock_image "Resources/referenced_icon.png" 1024
    create_mock_image "Resources/unused_icon.png" 1024
    
    # 创建引用图片的代码
    create_mock_code "Classes/ViewController.m" 'UIImage *icon = [UIImage imageNamed:@"referenced_icon"];'
    
    # 创建结果文件
    local result_file="$TEST_ENV_DIR/result.txt"
    
    # 测试有引用的图片
    check_single_image_reference "Resources/referenced_icon.png" "." "$result_file"
    
    # 检查结果
    local result_content=$(cat "$result_file" 2>/dev/null || echo "")
    assert_contains "$result_content" "REFERENCED" "应该检测到图片被引用"
    assert_contains "$result_content" "referenced_icon.png" "结果应该包含文件名"
    
    # 清空结果文件
    > "$result_file"
    
    # 测试无引用的图片
    check_single_image_reference "Resources/unused_icon.png" "." "$result_file"
    
    local unused_result=$(cat "$result_file" 2>/dev/null || echo "")
    assert_contains "$unused_result" "UNUSED" "应该检测到图片未被引用"
    
    cleanup_test_env
}

# 测试Asset Catalog引用检测
test_asset_catalog_reference() {
    test_case "测试Asset Catalog引用检测"
    
    setup_test_env "asset_test"
    
    # 创建Asset Catalog结构
    mkdir -p "Assets.xcassets/app_icon.imageset"
    create_mock_image "Assets.xcassets/app_icon.imageset/app_icon.png" 1024
    create_mock_code "Assets.xcassets/app_icon.imageset/Contents.json" '{
        "images": [{"filename": "app_icon.png", "idiom": "universal", "scale": "1x"}]
    }'
    
    # 创建引用Asset Catalog的代码
    create_mock_code "Classes/AppDelegate.m" 'UIImage *icon = [UIImage imageNamed:@"app_icon"];'
    
    # 创建结果文件
    local result_file="$TEST_ENV_DIR/result.txt"
    
    # 测试Asset Catalog引用检测
    check_single_image_reference "Assets.xcassets/app_icon.imageset/app_icon.png" "." "$result_file"
    
    local result_content=$(cat "$result_file" 2>/dev/null || echo "")
    assert_contains "$result_content" "REFERENCED" "Asset Catalog图片应该被检测为已引用"
    assert_contains "$result_content" "Asset Catalog" "结果应该标明是Asset Catalog引用"
    
    cleanup_test_env
}

# 测试无引用图片查找
test_find_unused_images() {
    test_case "测试无引用图片查找"
    
    setup_test_env "find_unused_test"
    
    # 创建混合的图片文件
    create_mock_image "Resources/used_icon.png" 1024
    create_mock_image "Resources/unused_icon.png" 1024
    create_mock_image "Resources/another_unused.jpg" 2048
    
    # 创建引用部分图片的代码
    create_mock_code "Classes/MainView.m" 'UIImage *icon = [UIImage imageNamed:@"used_icon"];'
    
    # 测试查找无引用图片
    local unused_file=$(find_unused_images "." 2>/dev/null | tail -n1)
    
    # 验证函数执行不出错
    assert_true "true" "查找无引用图片函数应该正常执行"
    
    # 如果返回了结果文件，验证内容
    if [ -f "$unused_file" ]; then
        local unused_content=$(cat "$unused_file" 2>/dev/null || echo "")
        assert_contains "$unused_content" "unused_icon.png" "应该找到未引用的PNG文件"
        assert_contains "$unused_content" "another_unused.jpg" "应该找到未引用的JPG文件"
        assert_not_contains "$unused_content" "used_icon.png" "不应该包含已引用的文件"
    fi
    
    cleanup_test_env
}

# 测试目录大小计算（新增功能）
test_calculate_directory_size() {
    test_case "测试目录大小计算"
    
    setup_test_env "dir_size_test"
    
    # 创建测试目录和文件
    mkdir -p "test_dir"
    create_mock_image "test_dir/file1.png" 1024
    create_mock_image "test_dir/file2.jpg" 2048
    echo "small content" > "test_dir/file3.txt"
    
    # 测试目录大小计算
    local dir_size=$(calculate_directory_size "test_dir")
    
    # 验证返回值是数字
    if [[ "$dir_size" =~ ^[0-9]+$ ]]; then
        assert_true "true" "目录大小应该是数字: $dir_size"
    else
        assert_true "false" "目录大小应该是数字: $dir_size"
    fi
    
    # 验证大小大于0
    if [ "$dir_size" -gt 0 ]; then
        assert_true "true" "目录大小应该大于0: $dir_size"
    else
        assert_true "false" "目录大小应该大于0: $dir_size"
    fi
    
    # 测试不存在的目录
    local nonexistent_size=$(calculate_directory_size "nonexistent_dir")
    assert_equals "0" "$nonexistent_size" "不存在的目录大小应该是0"
    
    cleanup_test_env
}

# 测试.imageset目录删除逻辑（重点测试）
test_imageset_deletion() {
    test_case "测试.imageset目录删除逻辑"
    
    setup_test_env "imageset_deletion_test"
    
    # 设置数据库
    export DB_PATH="$TEST_ENV_DIR/test.db"
    init_database
    
    # 创建.imageset目录结构
    mkdir -p "Assets.xcassets/test_icon.imageset"
    create_mock_image "Assets.xcassets/test_icon.imageset/test_icon.png" 1024
    create_mock_image "Assets.xcassets/test_icon.imageset/<EMAIL>" 2048
    create_mock_image "Assets.xcassets/test_icon.imageset/<EMAIL>" 3072
    create_mock_code "Assets.xcassets/test_icon.imageset/Contents.json" '{
        "images": [
            {"filename": "test_icon.png", "scale": "1x"},
            {"filename": "<EMAIL>", "scale": "2x"},
            {"filename": "<EMAIL>", "scale": "3x"}
        ]
    }'
    
    # 创建普通图片文件（非.imageset）
    create_mock_image "Resources/normal_unused.png" 1024
    
    # 创建无引用图片列表文件
    local unused_file="$TEST_ENV_DIR/unused_list.txt"
    cat > "$unused_file" << EOF
Assets.xcassets/test_icon.imageset/test_icon.png:1024
Assets.xcassets/test_icon.imageset/<EMAIL>:2048
Assets.xcassets/test_icon.imageset/<EMAIL>:3072
Resources/normal_unused.png:1024
EOF
    
    # 设置备份目录
    local backup_dir="$TEST_ENV_DIR/backup"
    mkdir -p "$backup_dir"
    
    # 模拟delete_unused_images函数的核心逻辑
    local total_saved=0
    local delete_count=0
    local deleted_imagesets=()
    
    while IFS=':' read filepath size; do
        if [ -f "$filepath" ]; then
            local file_dir=$(dirname "$filepath")
            local filename=$(basename "$filepath")
            
            # 检查是否在 .imageset 目录中
            if [[ "$file_dir" == *.imageset ]]; then
                # 检查这个 .imageset 目录是否已经被删除
                local imageset_already_deleted=false
                for deleted_dir in "${deleted_imagesets[@]}"; do
                    if [ "$deleted_dir" = "$file_dir" ]; then
                        imageset_already_deleted=true
                        break
                    fi
                done
                
                if [ "$imageset_already_deleted" = false ]; then
                    # 计算整个 .imageset 目录的大小
                    local imageset_size=$(calculate_directory_size "$file_dir")
                    local imageset_name=$(basename "$file_dir")
                    
                    # 验证目录存在
                    assert_dir_exists "$file_dir" ".imageset目录应该存在"
                    
                    # 备份整个 .imageset 目录（需要特殊处理目录备份）
                    mkdir -p "$backup_dir/$(dirname "$file_dir")"
                    cp -r "$file_dir" "$backup_dir/$file_dir"
                    
                    # 删除整个 .imageset 目录
                    rm -rf "$file_dir"
                    
                    # 验证目录被删除
                    assert_dir_not_exists "$file_dir" ".imageset目录应该被删除"
                    
                    # 验证备份存在
                    assert_dir_exists "$backup_dir/Assets.xcassets/test_icon.imageset" "备份的.imageset目录应该存在"
                    
                    total_saved=$((total_saved + imageset_size))
                    delete_count=$((delete_count + 1))
                    
                    # 记录已删除的 .imageset 目录
                    deleted_imagesets+=("$file_dir")
                fi
            else
                # 普通文件删除逻辑
                backup_file_preserve_structure "$filepath" "$backup_dir"
                rm "$filepath"
                
                # 验证文件被删除
                assert_file_not_exists "$filepath" "普通文件应该被删除"
                
                total_saved=$((total_saved + size))
                delete_count=$((delete_count + 1))
            fi
        fi
    done < "$unused_file"
    
    # 验证删除统计
    assert_equals "2" "$delete_count" "应该删除2个项目（1个.imageset目录 + 1个普通文件）"
    if [ "$total_saved" -gt 0 ]; then
        assert_true "true" "应该节省一些空间: $total_saved 字节"
    else
        assert_true "false" "应该节省一些空间: $total_saved 字节"
    fi
    
    # 验证备份文件
    assert_file_exists "$backup_dir/Resources/normal_unused.png" "普通文件备份应该存在"
    assert_file_exists "$backup_dir/Assets.xcassets/test_icon.imageset/Contents.json" "Contents.json备份应该存在"
    
    cleanup_test_env
}

# 测试删除无引用图片的完整流程
test_delete_unused_images_integration() {
    test_case "测试删除无引用图片完整流程"
    
    setup_test_env "delete_integration_test"
    
    # 设置数据库
    export DB_PATH="$TEST_ENV_DIR/test.db"
    init_database
    
    # 创建测试文件结构
    create_mock_image "Resources/unused1.png" 1024
    create_mock_image "Resources/unused2.jpg" 2048
    create_mock_image "Resources/used.png" 1024
    
    # 创建引用代码
    create_mock_code "Classes/Test.m" 'UIImage *img = [UIImage imageNamed:@"used"];'
    
    # 设置备份目录
    local backup_dir="$TEST_ENV_DIR/backup"
    mkdir -p "$backup_dir"
    
    # 测试完整的删除流程
    local saved_bytes=$(delete_unused_images "TestPod" "$backup_dir" 2>/dev/null || echo "0")
    
    # 验证函数执行不出错
    assert_true "true" "删除无引用图片函数应该正常执行"
    
    # 验证返回值是数字
    if [[ "$saved_bytes" =~ ^[0-9]+$ ]]; then
        assert_true "true" "返回的节省字节数应该是数字: $saved_bytes"
    else
        assert_true "true" "函数应该返回数字值（即使是0）"
    fi
    
    cleanup_test_env
}

# 主测试函数
main() {
    test_suite "无用图片检测器测试"
    
    test_animation_sequence_detection
    test_single_image_reference
    test_asset_catalog_reference
    test_find_unused_images
    test_calculate_directory_size
    test_imageset_deletion
    test_delete_unused_images_integration
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
