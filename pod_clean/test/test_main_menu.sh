#!/bin/bash

# Pod库优化工具 - 主菜单功能测试
# 功能: 测试主脚本的所有菜单选项和命令行参数
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 项目路径
PROJECT_ROOT="$TEST_PROJECT_ROOT"
MAIN_SCRIPT="$PROJECT_ROOT/pod_optimizer.sh"

# 测试主脚本帮助信息
test_help_option() {
    test_case "测试帮助选项"
    
    # 测试 -h 选项
    local help_output=$(bash "$MAIN_SCRIPT" -h 2>&1)
    assert_contains "$help_output" "Pod库优化工具" "帮助信息应包含工具名称"
    assert_contains "$help_output" "用法:" "帮助信息应包含用法说明"
    assert_contains "$help_output" "选项:" "帮助信息应包含选项说明"
    
    # 测试 --help 选项
    local help_output_long=$(bash "$MAIN_SCRIPT" --help 2>&1)
    assert_contains "$help_output_long" "Pod库优化工具" "长选项帮助信息应包含工具名称"
}

# 测试无效选项处理
test_invalid_option() {
    test_case "测试无效选项处理"
    
    # 测试无效选项
    local invalid_output=$(bash "$MAIN_SCRIPT" --invalid-option 2>&1 || true)
    assert_contains "$invalid_output" "未知选项" "应该显示未知选项错误"
}

# 测试批量优化选项
test_batch_option() {
    test_case "测试批量优化选项"
    
    setup_test_env "batch_test"
    
    # 由于批量优化需要实际的Pod环境，我们主要测试选项解析
    # 在测试环境中运行会因为没有Pod而快速退出，这是预期的
    local batch_output=$(timeout 10s bash "$MAIN_SCRIPT" -b 2>&1 || true)
    
    # 检查是否正确识别了批量优化选项
    # 由于没有实际Pod，可能会有相关错误信息，但不应该是"未知选项"错误
    assert_not_contains "$batch_output" "未知选项" "不应该报告未知选项错误"
    
    cleanup_test_env
}

# 测试选择性优化选项
test_selective_option() {
    test_case "测试选择性优化选项"
    
    setup_test_env "selective_test"
    
    # 测试 -s 选项
    local selective_output=$(timeout 5s bash "$MAIN_SCRIPT" -s 2>&1 || true)
    assert_not_contains "$selective_output" "未知选项" "不应该报告未知选项错误"
    
    # 测试 --selective 选项
    local selective_output_long=$(timeout 5s bash "$MAIN_SCRIPT" --selective 2>&1 || true)
    assert_not_contains "$selective_output_long" "未知选项" "长选项不应该报告未知选项错误"
    
    cleanup_test_env
}

# 测试优化类型选项
test_type_option() {
    test_case "测试优化类型选项"
    
    setup_test_env "type_test"
    
    # 测试有效的优化类型
    local valid_types=("compress" "delete" "mixed" "heic")
    
    for type in "${valid_types[@]}"; do
        local type_output=$(timeout 5s bash "$MAIN_SCRIPT" -t "$type" 2>&1 || true)
        assert_contains "$type_output" "测试优化类型: $type" "应该识别优化类型 $type"
    done
    
    # 测试无效的优化类型
    local invalid_type_output=$(bash "$MAIN_SCRIPT" -t "invalid" 2>&1 || true)
    assert_contains "$invalid_type_output" "无效的优化类型" "应该报告无效的优化类型"
    
    # 测试缺少类型参数
    local missing_type_output=$(bash "$MAIN_SCRIPT" -t 2>&1 || true)
    assert_contains "$missing_type_output" "需要指定优化类型" "应该报告缺少类型参数"
    
    cleanup_test_env
}

# 测试Git贮藏选项
test_stash_option() {
    test_case "测试Git贮藏选项"
    
    setup_test_env "stash_test"
    
    # 初始化Git仓库（如果不存在）
    if [ ! -d ".git" ]; then
        git init >/dev/null 2>&1 || true
        git config user.email "<EMAIL>" >/dev/null 2>&1 || true
        git config user.name "Test User" >/dev/null 2>&1 || true
    fi
    
    local stash_output=$(timeout 5s bash "$MAIN_SCRIPT" --stash 2>&1 || true)
    assert_not_contains "$stash_output" "未知选项" "不应该报告未知选项错误"
    
    cleanup_test_env
}

# 测试历史选项
test_history_option() {
    test_case "测试历史选项"
    
    setup_test_env "history_test"
    
    # 测试 -H 选项
    local history_output=$(bash "$MAIN_SCRIPT" -H 2>&1 || true)
    assert_not_contains "$history_output" "未知选项" "不应该报告未知选项错误"
    
    # 测试 --history 选项
    local history_output_long=$(bash "$MAIN_SCRIPT" --history 2>&1 || true)
    assert_not_contains "$history_output_long" "未知选项" "长选项不应该报告未知选项错误"
    
    cleanup_test_env
}

# 测试重置选项
test_reset_option() {
    test_case "测试重置选项"
    
    setup_test_env "reset_test"
    
    local reset_output=$(bash "$MAIN_SCRIPT" --reset 2>&1 || true)
    assert_not_contains "$reset_output" "未知选项" "不应该报告未知选项错误"
    
    cleanup_test_env
}

# 测试质量报告选项
test_quality_option() {
    test_case "测试质量报告选项"
    
    setup_test_env "quality_test"
    
    # 测试 -q 选项
    local quality_output=$(timeout 5s bash "$MAIN_SCRIPT" -q 2>&1 || true)
    assert_not_contains "$quality_output" "未知选项" "不应该报告未知选项错误"
    
    # 测试 --quality 选项
    local quality_output_long=$(timeout 5s bash "$MAIN_SCRIPT" --quality 2>&1 || true)
    assert_not_contains "$quality_output_long" "未知选项" "长选项不应该报告未知选项错误"
    
    cleanup_test_env
}

# 测试脚本依赖检查
test_script_dependencies() {
    test_case "测试脚本依赖"
    
    # 检查主脚本是否存在
    assert_file_exists "$MAIN_SCRIPT" "主脚本应该存在"
    
    # 检查主脚本是否可执行
    if [ -x "$MAIN_SCRIPT" ]; then
        assert_true "true" "主脚本应该可执行"
    else
        assert_true "false" "主脚本应该可执行"
    fi
    
    # 检查核心库文件
    local lib_files=(
        "$PROJECT_ROOT/scripts/lib/common.sh"
        "$PROJECT_ROOT/scripts/lib/database.sh"
        "$PROJECT_ROOT/scripts/lib/image_processor.sh"
        "$PROJECT_ROOT/scripts/lib/unused_detector.sh"
        "$PROJECT_ROOT/scripts/lib/git_manager.sh"
    )
    
    for lib_file in "${lib_files[@]}"; do
        assert_file_exists "$lib_file" "库文件应该存在: $(basename "$lib_file")"
    done
    
    # 检查模块文件
    local module_files=(
        "$PROJECT_ROOT/scripts/modules/batch_optimizer.sh"
        "$PROJECT_ROOT/scripts/modules/selective_optimizer.sh"
    )
    
    for module_file in "${module_files[@]}"; do
        assert_file_exists "$module_file" "模块文件应该存在: $(basename "$module_file")"
    done
}

# 测试脚本语法
test_script_syntax() {
    test_case "测试脚本语法"
    
    # 检查主脚本语法
    if bash -n "$MAIN_SCRIPT" 2>/dev/null; then
        assert_true "true" "主脚本语法正确"
    else
        assert_true "false" "主脚本语法正确"
    fi
    
    # 检查库文件语法
    local script_files=(
        "$PROJECT_ROOT/scripts/lib"/*.sh
        "$PROJECT_ROOT/scripts/modules"/*.sh
    )
    
    for script_file in "${script_files[@]}"; do
        if [ -f "$script_file" ]; then
            if bash -n "$script_file" 2>/dev/null; then
                assert_true "true" "脚本语法正确: $(basename "$script_file")"
            else
                assert_true "false" "脚本语法正确: $(basename "$script_file")"
            fi
        fi
    done
}

# 主测试函数
main() {
    test_suite "主菜单功能测试"
    
    test_script_dependencies
    test_script_syntax
    test_help_option
    test_invalid_option
    test_batch_option
    test_selective_option
    test_type_option
    test_stash_option
    test_history_option
    test_reset_option
    test_quality_option
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
