# Pod库优化工具 - 测试套件

这是Pod库优化工具的完整测试套件，用于验证所有功能模块的正确性和稳定性。

## 测试结构

```
test/
├── test_runner.sh              # 测试运行器（主入口）
├── test_framework.sh           # 测试框架（断言和工具函数）
├── test_main_menu.sh          # 主菜单功能测试
├── test_common.sh             # 通用函数测试
├── test_database.sh           # 数据库功能测试
├── test_image_processor.sh    # 图片处理功能测试
├── test_unused_detector.sh    # 无用图片检测器测试
├── test_git_manager.sh        # Git管理功能测试
├── test_batch_optimizer.sh    # 批量优化器测试
├── test_selective_optimizer.sh # 选择性优化器测试
├── test_integration.sh        # 集成测试
└── README.md                  # 本文件
```

## 快速开始

### 运行所有测试

```bash
cd pod_clean/test
./test_runner.sh
```

### 运行特定测试

```bash
# 运行主菜单测试
./test_runner.sh test_main_menu.sh

# 运行数据库测试
./test_runner.sh test_database

# 运行图片处理测试
./test_runner.sh test_image_processor.sh
```

### 详细模式

```bash
# 详细输出模式
./test_runner.sh -v

# 静默模式
./test_runner.sh -q
```

## 测试覆盖范围

### 1. 主菜单功能测试 (`test_main_menu.sh`)
- ✅ 帮助信息显示
- ✅ 命令行参数解析
- ✅ 批量优化选项
- ✅ 选择性优化选项
- ✅ 优化类型选项
- ✅ Git贮藏选项
- ✅ 历史查看选项
- ✅ 质量报告选项
- ✅ 脚本依赖检查
- ✅ 脚本语法验证

### 2. 通用函数测试 (`test_common.sh`)
- ✅ 日志函数（info, success, warning, error）
- ✅ 文件大小格式化
- ✅ 图片文件扫描
- ✅ 备份功能
- ✅ 并发支持检查
- ✅ 工具可用性检查
- ✅ 横幅显示
- ✅ 路径处理
- ✅ 错误处理

### 3. 数据库功能测试 (`test_database.sh`)
- ✅ 数据库初始化
- ✅ 优化历史记录
- ✅ 文件优化记录
- ✅ 删除进度跟踪
- ✅ 历史显示功能
- ✅ 进度显示功能
- ✅ 数据清理功能
- ✅ 记录重置功能
- ✅ 错误处理

### 4. 图片处理功能测试 (`test_image_processor.sh`)
- ✅ Contents.json更新
- ✅ HEIC转换功能
- ✅ Alpha通道检测
- ✅ 图片压缩功能
- ✅ 重复图片检测
- ✅ Pod HEIC转换
- ✅ 图片扫描集成

### 5. 无用图片检测器测试 (`test_unused_detector.sh`)
- ✅ 动画序列帧检测
- ✅ 单个图片引用检测
- ✅ Asset Catalog引用检测
- ✅ 无引用图片查找
- ✅ **目录大小计算（新功能）**
- ✅ **.imageset目录删除逻辑（重点优化）**
- ✅ 删除流程集成测试

### 6. Git管理功能测试 (`test_git_manager.sh`)
- ✅ Git贮藏创建
- ✅ Git贮藏恢复
- ✅ Git状态检查
- ✅ Git分支操作
- ✅ Git提交操作
- ✅ Git差异检查
- ✅ Git远程检查
- ✅ Git日志操作
- ✅ Git标签操作

### 7. 批量优化器测试 (`test_batch_optimizer.sh`)
- ✅ Pod发现功能
- ✅ 单个Pod优化
- ✅ 批量优化流程
- ✅ 优化类型选择
- ✅ 备份管理
- ✅ 进度报告
- ✅ 错误处理
- ✅ 并发处理

### 8. 选择性优化器测试 (`test_selective_optimizer.sh`)
- ✅ Pod列表生成
- ✅ Pod信息显示
- ✅ Pod选择逻辑
- ✅ 选择性优化执行
- ✅ 优化预览功能
- ✅ 交互式菜单
- ✅ 优化历史记录
- ✅ 错误恢复

### 9. 集成测试 (`test_integration.sh`)
- ✅ 完整优化流程
- ✅ 命令行接口集成
- ✅ 数据持久化
- ✅ 备份和恢复
- ✅ **.imageset删除集成（重点测试）**
- ✅ 错误处理集成
- ✅ 性能和并发
- ✅ 报告生成

## 重点测试功能

### .imageset目录删除逻辑测试

我们特别为您优化的 `.imageset` 目录删除功能进行了全面测试：

1. **目录大小计算测试** (`test_calculate_directory_size`)
   - 测试递归计算目录大小
   - 测试不存在目录的处理

2. **.imageset删除逻辑测试** (`test_imageset_deletion`)
   - 创建完整的 `.imageset` 目录结构
   - 测试整个目录的删除
   - 验证备份机制
   - 测试重复删除避免
   - 验证空间计算准确性

3. **集成测试** (`test_imageset_deletion_integration`)
   - 端到端测试 `.imageset` 删除流程
   - 验证与数据库的集成
   - 测试备份和恢复机制

## 测试环境要求

### 必需工具
- `bash` (4.0+)
- `sqlite3`
- `find`
- `stat`
- `dd`

### 可选工具（用于完整功能测试）
- `git`
- `pngquant`
- `jpegoptim`
- `rg` (ripgrep)
- `parallel`
- `jq`

## 测试输出示例

```
==================================
  Pod库优化工具 - 单元测试套件
==================================
测试目录: /path/to/pod_clean/test
项目根目录: /path/to/pod_clean

[INFO] 运行测试: test_main_menu
=== 测试套件: 主菜单功能测试 ===
--- 测试用例: 测试脚本依赖 ---
  ✓ 主脚本应该存在
  ✓ 主脚本应该可执行
  ✓ 库文件应该存在: common.sh
  ✓ 库文件应该存在: database.sh
[PASS] test_main_menu 测试通过

==================================
           测试结果摘要
==================================
总测试数: 9
通过: 9
失败: 0
跳过: 0

✓ 所有测试通过！
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x test_runner.sh
   chmod +x test_*.sh
   ```

2. **SQLite不可用**
   ```bash
   # macOS
   brew install sqlite3
   
   # Ubuntu/Debian
   sudo apt-get install sqlite3
   ```

3. **测试超时**
   - 某些测试可能需要较长时间
   - 可以调整测试脚本中的超时设置

### 调试模式

```bash
# 启用详细输出
export TEST_VERBOSE=1
./test_runner.sh

# 保留测试临时文件
export TEST_KEEP_TEMP=1
./test_runner.sh
```

## 贡献指南

### 添加新测试

1. 在相应的测试文件中添加新的测试用例
2. 使用测试框架提供的断言函数
3. 确保测试的独立性和可重复性
4. 添加适当的清理逻辑

### 测试最佳实践

1. **独立性**: 每个测试应该独立运行
2. **清理**: 使用 `setup_test_env` 和 `cleanup_test_env`
3. **断言**: 使用明确的断言消息
4. **模拟**: 对外部依赖进行适当的模拟
5. **覆盖**: 测试正常流程和错误情况

## 许可证

与主项目相同的许可证。
