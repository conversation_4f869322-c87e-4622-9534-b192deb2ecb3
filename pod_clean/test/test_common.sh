#!/bin/bash

# Pod库优化工具 - 通用函数测试
# 功能: 测试 common.sh 中的通用工具函数
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 加载被测试的模块
PROJECT_ROOT="$TEST_PROJECT_ROOT"
source "$PROJECT_ROOT/scripts/lib/common.sh"

# 测试日志函数
test_logging_functions() {
    test_case "测试日志函数"
    
    # 测试 log_info
    local info_output=$(log_info "测试信息" 2>&1)
    assert_contains "$info_output" "测试信息" "log_info应该输出信息内容"
    assert_contains "$info_output" "INFO" "log_info应该包含INFO标识"
    
    # 测试 log_success
    local success_output=$(log_success "测试成功" 2>&1)
    assert_contains "$success_output" "测试成功" "log_success应该输出成功内容"
    
    # 测试 log_warning
    local warning_output=$(log_warning "测试警告" 2>&1)
    assert_contains "$warning_output" "测试警告" "log_warning应该输出警告内容"
    
    # 测试 log_error
    local error_output=$(log_error "测试错误" 2>&1)
    assert_contains "$error_output" "测试错误" "log_error应该输出错误内容"
}

# 测试文件大小格式化函数（如果存在）
test_format_size() {
    test_case "测试文件大小格式化"

    # 由于format_size函数可能不存在，我们测试基本的大小计算逻辑
    local test_size=2048
    local kb_size=$((test_size / 1024))
    assert_equals "2" "$kb_size" "2048字节应该等于2KB"

    local test_mb_size=2097152
    local mb_size=$((test_mb_size / 1024 / 1024))
    assert_equals "2" "$mb_size" "2097152字节应该等于2MB"

    # 测试基本的大小比较
    if [ 2048 -gt 1024 ]; then
        assert_true "true" "2048应该大于1024"
    else
        assert_true "false" "2048应该大于1024"
    fi

    if [ 1024 -lt 2048 ]; then
        assert_true "true" "1024应该小于2048"
    else
        assert_true "false" "1024应该小于2048"
    fi
}

# 测试文件扫描函数
test_scan_pod_images() {
    test_case "测试图片文件扫描"
    
    setup_test_env "scan_test"
    
    # 创建测试图片文件
    mkdir -p "test_images"
    touch "test_images/test1.png"
    touch "test_images/test2.jpg"
    touch "test_images/test3.jpeg"
    touch "test_images/test4.gif"
    touch "test_images/test5.txt"  # 非图片文件
    
    # 测试扫描PNG文件
    local png_files=$(scan_pod_images "test_images" "png")
    assert_contains "$png_files" "test1.png" "应该找到PNG文件"
    assert_not_contains "$png_files" "test2.jpg" "PNG扫描不应该包含JPG文件"
    
    # 测试扫描JPG文件
    local jpg_files=$(scan_pod_images "test_images" "jpg")
    assert_contains "$jpg_files" "test2.jpg" "应该找到JPG文件"
    assert_contains "$jpg_files" "test3.jpeg" "应该找到JPEG文件"
    assert_not_contains "$jpg_files" "test1.png" "JPG扫描不应该包含PNG文件"
    
    # 测试扫描所有图片文件
    local all_files=$(scan_pod_images "test_images" "all")
    assert_contains "$all_files" "test1.png" "全部扫描应该包含PNG文件"
    assert_contains "$all_files" "test2.jpg" "全部扫描应该包含JPG文件"
    assert_contains "$all_files" "test3.jpeg" "全部扫描应该包含JPEG文件"
    assert_not_contains "$all_files" "test5.txt" "全部扫描不应该包含非图片文件"
    
    cleanup_test_env
}

# 测试备份函数
test_backup_functions() {
    test_case "测试备份函数"
    
    setup_test_env "backup_test"
    
    # 创建测试文件
    echo "test content" > "test_file.txt"
    mkdir -p "backup_dir"
    
    # 测试文件备份
    backup_file_preserve_structure "test_file.txt" "backup_dir"
    assert_file_exists "backup_dir/test_file.txt" "备份文件应该存在"
    
    # 验证备份内容
    local original_content=$(cat "test_file.txt")
    local backup_content=$(cat "backup_dir/test_file.txt")
    assert_equals "$original_content" "$backup_content" "备份内容应该与原文件相同"
    
    cleanup_test_env
}

# 测试并发支持检查
test_concurrent_support() {
    test_case "测试并发支持检查"
    
    # 测试并发任务数获取
    local jobs=$(get_concurrent_jobs)
    if [ "$jobs" -gt 0 ]; then
        assert_true "true" "并发任务数应该大于0: $jobs"
    else
        assert_true "false" "并发任务数应该大于0: $jobs"
    fi

    # 测试并行支持检查
    local parallel_support=$(check_parallel_support)
    if [ -n "$parallel_support" ]; then
        assert_true "true" "应该返回并行支持类型: $parallel_support"
    else
        assert_true "false" "应该返回并行支持类型"
    fi
    
    # 验证返回值是预期的类型之一
    if [ "$parallel_support" = "parallel" ] || [ "$parallel_support" = "xargs" ] || [ "$parallel_support" = "serial" ]; then
        assert_true "true" "并行支持类型应该是有效值: $parallel_support"
    else
        assert_true "false" "并行支持类型应该是有效值: $parallel_support"
    fi
}

# 测试工具检查函数
test_tool_checks() {
    test_case "测试工具检查函数"
    
    # 测试基本命令检查
    if command -v ls >/dev/null 2>&1; then
        assert_true "true" "ls命令应该可用"
    else
        assert_true "false" "ls命令应该可用"
    fi
    
    # 测试可选工具检查（这些可能不存在，所以只测试函数不报错）
    local tools=("pngquant" "jpegoptim" "rg" "parallel")
    for tool in "${tools[@]}"; do
        # 只要函数调用不出错就算通过
        if command -v "$tool" >/dev/null 2>&1; then
            assert_true "true" "$tool 工具检查函数正常"
        else
            assert_true "true" "$tool 工具检查函数正常（工具不存在但检查正常）"
        fi
    done
}

# 测试横幅显示
test_show_banner() {
    test_case "测试横幅显示"

    local banner_output=$(show_banner 2>&1)
    assert_contains "$banner_output" "Pod库优化工具" "横幅应该包含工具名称"
    assert_contains "$banner_output" "v6.0" "横幅应该包含版本信息"
}

# 测试路径处理函数
test_path_functions() {
    test_case "测试路径处理函数"
    
    setup_test_env "path_test"
    
    # 创建测试目录结构
    mkdir -p "deep/nested/directory"
    touch "deep/nested/directory/file.txt"
    
    # 测试相对路径处理
    cd "deep/nested"
    local relative_path=$(realpath "directory/file.txt" 2>/dev/null || echo "directory/file.txt")
    assert_contains "$relative_path" "file.txt" "应该能处理相对路径"
    
    cleanup_test_env
}

# 测试错误处理
test_error_handling() {
    test_case "测试错误处理"
    
    setup_test_env "error_test"
    
    # 测试不存在的文件处理
    local nonexistent_scan=$(scan_pod_images "nonexistent_dir" "all" 2>/dev/null || echo "")
    assert_equals "" "$nonexistent_scan" "扫描不存在的目录应该返回空结果"
    
    # 测试备份不存在的文件
    if backup_file_preserve_structure "nonexistent_file.txt" "backup_dir" 2>/dev/null; then
        assert_true "false" "备份不存在的文件应该失败"
    else
        assert_true "true" "备份不存在的文件应该失败"
    fi
    
    cleanup_test_env
}

# 主测试函数
main() {
    test_suite "通用函数测试"
    
    test_logging_functions
    test_format_size
    test_scan_pod_images
    test_backup_functions
    test_concurrent_support
    test_tool_checks
    test_show_banner
    test_path_functions
    test_error_handling
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
