#!/bin/bash

# Pod库优化工具 - 数据库功能测试
# 功能: 测试 database.sh 中的数据库操作函数
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 加载被测试的模块
PROJECT_ROOT="$TEST_PROJECT_ROOT"
source "$PROJECT_ROOT/scripts/lib/common.sh"
source "$PROJECT_ROOT/scripts/lib/database.sh"

# 设置测试数据库
setup_test_database() {
    TEST_DB_PATH="$TEST_ENV_DIR/test_optimizer.db"
    export DB_PATH="$TEST_DB_PATH"
}

# 测试数据库初始化
test_database_initialization() {
    test_case "测试数据库初始化"
    
    setup_test_env "db_init_test"
    setup_test_database
    
    # 测试数据库初始化
    init_database
    assert_file_exists "$TEST_DB_PATH" "数据库文件应该被创建"
    
    # 检查表是否存在
    local tables=$(sqlite3 "$TEST_DB_PATH" ".tables" 2>/dev/null || echo "")
    assert_contains "$tables" "optimization_history" "应该创建optimization_history表"
    assert_contains "$tables" "file_optimizations" "应该创建file_optimizations表"
    assert_contains "$tables" "deletion_progress" "应该创建deletion_progress表"
    
    cleanup_test_env
}

# 测试优化历史记录
test_optimization_history() {
    test_case "测试优化历史记录"
    
    setup_test_env "db_history_test"
    setup_test_database
    init_database
    
    # 记录优化历史
    local test_pod="TestPod"
    local test_type="compress"
    local test_saved=1024
    local test_count=5
    
    record_optimization "$test_pod" "$test_type" "$test_saved" "$test_count"
    
    # 验证记录是否存在
    local history_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM optimization_history WHERE pod_name='$test_pod';" 2>/dev/null || echo "0")
    assert_equals "1" "$history_count" "应该有一条优化历史记录"
    
    # 验证记录内容
    local saved_bytes=$(sqlite3 "$TEST_DB_PATH" "SELECT saved_bytes FROM optimization_history WHERE pod_name='$test_pod';" 2>/dev/null || echo "0")
    assert_equals "$test_saved" "$saved_bytes" "保存的字节数应该正确"
    
    local file_count=$(sqlite3 "$TEST_DB_PATH" "SELECT file_count FROM optimization_history WHERE pod_name='$test_pod';" 2>/dev/null || echo "0")
    assert_equals "$test_count" "$file_count" "文件数量应该正确"
    
    cleanup_test_env
}

# 测试文件优化记录
test_file_optimization_records() {
    test_case "测试文件优化记录"
    
    setup_test_env "db_file_test"
    setup_test_database
    init_database
    
    # 记录文件优化
    local test_file="test.png"
    local test_type="compress"
    local original_size=2048
    local new_size=1024
    local compression_ratio=50.0
    
    record_file_optimization "$test_file" "$test_type" "$original_size" "$new_size" "$compression_ratio"
    
    # 验证记录是否存在
    local file_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM file_optimizations WHERE file_path='$test_file';" 2>/dev/null || echo "0")
    assert_equals "1" "$file_count" "应该有一条文件优化记录"
    
    # 测试文件是否已优化检查
    if is_file_optimized "$test_file" "$test_type"; then
        assert_true "true" "应该检测到文件已被优化"
    else
        assert_true "false" "应该检测到文件已被优化"
    fi
    
    # 测试不同类型的优化检查
    if is_file_optimized "$test_file" "delete"; then
        assert_true "false" "不同类型的优化应该返回false"
    else
        assert_true "true" "不同类型的优化应该返回false"
    fi
    
    cleanup_test_env
}

# 测试删除进度记录
test_deletion_progress() {
    test_case "测试删除进度记录"
    
    setup_test_env "db_deletion_test"
    setup_test_database
    init_database
    
    local test_pod="TestPod"
    local test_file="test.png"
    
    # 记录删除候选
    record_deletion_candidate "$test_pod" "$test_file"
    
    # 验证候选记录
    local candidate_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM deletion_progress WHERE pod_name='$test_pod' AND file_path='$test_file';" 2>/dev/null || echo "0")
    assert_equals "1" "$candidate_count" "应该有一条删除候选记录"
    
    # 检查删除状态
    if is_deletion_completed "$test_pod" "$test_file"; then
        assert_true "false" "新记录的删除状态应该是未完成"
    else
        assert_true "true" "新记录的删除状态应该是未完成"
    fi
    
    # 标记为已删除
    mark_deletion_completed "$test_pod" "$test_file"
    
    # 再次检查删除状态
    if is_deletion_completed "$test_pod" "$test_file"; then
        assert_true "true" "标记后的删除状态应该是已完成"
    else
        assert_true "false" "标记后的删除状态应该是已完成"
    fi
    
    cleanup_test_env
}

# 测试优化历史显示
test_show_optimization_history() {
    test_case "测试优化历史显示"
    
    setup_test_env "db_show_test"
    setup_test_database
    init_database
    
    # 添加测试数据
    record_optimization "TestPod1" "compress" 1024 5
    record_optimization "TestPod2" "delete" 2048 3
    
    # 测试历史显示
    local history_output=$(show_optimization_history 2>&1)
    assert_contains "$history_output" "TestPod1" "历史显示应该包含TestPod1"
    assert_contains "$history_output" "TestPod2" "历史显示应该包含TestPod2"
    assert_contains "$history_output" "compress" "历史显示应该包含compress类型"
    assert_contains "$history_output" "delete" "历史显示应该包含delete类型"
    
    cleanup_test_env
}

# 测试删除进度显示
test_show_deletion_progress() {
    test_case "测试删除进度显示"
    
    setup_test_env "db_progress_test"
    setup_test_database
    init_database
    
    # 添加测试数据
    record_deletion_candidate "TestPod" "file1.png"
    record_deletion_candidate "TestPod" "file2.png"
    mark_deletion_completed "TestPod" "file1.png"
    
    # 测试进度显示
    local progress_output=$(show_deletion_progress 2>&1)
    assert_contains "$progress_output" "TestPod" "进度显示应该包含Pod名称"
    assert_contains "$progress_output" "file1.png" "进度显示应该包含已删除文件"
    assert_contains "$progress_output" "file2.png" "进度显示应该包含待删除文件"
    
    cleanup_test_env
}

# 测试数据清理功能
test_data_cleanup() {
    test_case "测试数据清理功能"
    
    setup_test_env "db_cleanup_test"
    setup_test_database
    init_database
    
    # 添加测试数据
    record_deletion_candidate "TestPod" "file1.png"
    record_deletion_candidate "TestPod" "file2.png"
    mark_deletion_completed "TestPod" "file1.png"
    
    # 清理删除进度
    clean_deletion_progress
    
    # 验证数据被清理
    local remaining_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM deletion_progress;" 2>/dev/null || echo "0")
    assert_equals "0" "$remaining_count" "删除进度数据应该被清理"
    
    cleanup_test_env
}

# 测试记录重置功能
test_reset_records() {
    test_case "测试记录重置功能"
    
    setup_test_env "db_reset_test"
    setup_test_database
    init_database
    
    # 添加测试数据
    record_optimization "TestPod" "compress" 1024 5
    record_file_optimization "test.png" "compress" 2048 1024 50.0
    record_deletion_candidate "TestPod" "file1.png"
    
    # 重置记录
    reset_optimization_records
    
    # 验证所有数据被清理
    local history_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM optimization_history;" 2>/dev/null || echo "0")
    assert_equals "0" "$history_count" "优化历史应该被清理"
    
    local file_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM file_optimizations;" 2>/dev/null || echo "0")
    assert_equals "0" "$file_count" "文件优化记录应该被清理"
    
    local deletion_count=$(sqlite3 "$TEST_DB_PATH" "SELECT COUNT(*) FROM deletion_progress;" 2>/dev/null || echo "0")
    assert_equals "0" "$deletion_count" "删除进度应该被清理"
    
    cleanup_test_env
}

# 测试数据库错误处理
test_database_error_handling() {
    test_case "测试数据库错误处理"
    
    setup_test_env "db_error_test"
    
    # 测试无效数据库路径
    export DB_PATH="/invalid/path/test.db"
    
    # 这些操作应该不会崩溃，即使数据库不可用
    record_optimization "TestPod" "compress" 1024 5 2>/dev/null || true
    record_file_optimization "test.png" "compress" 2048 1024 50.0 2>/dev/null || true
    
    # 检查函数应该返回false或安全的默认值
    if is_file_optimized "test.png" "compress" 2>/dev/null; then
        assert_true "false" "无效数据库时应该返回false"
    else
        assert_true "true" "无效数据库时应该返回false"
    fi
    
    cleanup_test_env
}

# 主测试函数
main() {
    test_suite "数据库功能测试"
    
    test_database_initialization
    test_optimization_history
    test_file_optimization_records
    test_deletion_progress
    test_show_optimization_history
    test_show_deletion_progress
    test_data_cleanup
    test_reset_records
    test_database_error_handling
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
