#!/bin/bash

# Pod库优化工具 - Git管理功能测试
# 功能: 测试 git_manager.sh 中的Git相关功能
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 加载被测试的模块
PROJECT_ROOT="$TEST_PROJECT_ROOT"
source "$PROJECT_ROOT/scripts/lib/common.sh"
source "$PROJECT_ROOT/scripts/lib/git_manager.sh"

# 初始化测试Git仓库
setup_test_git_repo() {
    git init >/dev/null 2>&1
    git config user.email "<EMAIL>" >/dev/null 2>&1
    git config user.name "Test User" >/dev/null 2>&1
    
    # 创建初始提交
    echo "initial content" > "README.md"
    git add README.md >/dev/null 2>&1
    git commit -m "Initial commit" >/dev/null 2>&1
}

# 测试Git贮藏创建
test_git_stash_creation() {
    test_case "测试Git贮藏创建"
    
    setup_test_env "git_stash_test"
    setup_test_git_repo
    
    # 创建一些修改
    echo "modified content" > "README.md"
    echo "new file" > "new_file.txt"
    git add new_file.txt >/dev/null 2>&1
    
    # 测试贮藏创建（如果git_manager.sh中有相关函数）
    # 由于我们没有看到git_manager.sh的具体内容，这里测试基本Git操作
    
    # 检查是否有未提交的更改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        assert_true "true" "应该有未提交的更改"
    else
        assert_true "false" "应该有未提交的更改"
    fi
    
    # 创建贮藏
    local stash_output=$(git stash push -m "Test stash" 2>&1)
    
    # 验证贮藏创建成功
    if git stash list | grep -q "Test stash"; then
        assert_true "true" "应该创建Git贮藏"
    else
        assert_true "false" "应该创建Git贮藏"
    fi
    
    # 验证工作目录被清理
    if git diff --quiet && git diff --cached --quiet; then
        assert_true "true" "工作目录应该被清理"
    else
        assert_true "false" "工作目录应该被清理"
    fi
    
    cleanup_test_env
}

# 测试Git贮藏恢复
test_git_stash_restoration() {
    test_case "测试Git贮藏恢复"
    
    setup_test_env "git_restore_test"
    setup_test_git_repo
    
    # 创建修改并贮藏
    echo "modified content" > "README.md"
    echo "new file" > "new_file.txt"
    git add new_file.txt >/dev/null 2>&1
    git stash push -m "Test stash for restore" >/dev/null 2>&1
    
    # 验证贮藏存在
    local stash_count=$(git stash list | wc -l)
    assert_true "$([ "$stash_count" -gt 0 ])" "应该有贮藏存在"
    
    # 恢复贮藏
    git stash pop >/dev/null 2>&1
    
    # 验证文件被恢复
    assert_file_exists "new_file.txt" "新文件应该被恢复"
    
    # 验证修改被恢复
    local readme_content=$(cat "README.md")
    assert_equals "modified content" "$readme_content" "文件修改应该被恢复"
    
    cleanup_test_env
}

# 测试Git状态检查
test_git_status_check() {
    test_case "测试Git状态检查"
    
    setup_test_env "git_status_test"
    setup_test_git_repo
    
    # 测试干净的工作目录
    if git diff --quiet && git diff --cached --quiet; then
        assert_true "true" "初始状态应该是干净的"
    else
        assert_true "false" "初始状态应该是干净的"
    fi
    
    # 创建修改
    echo "modified" > "README.md"
    
    # 测试有修改的状态
    if ! git diff --quiet; then
        assert_true "true" "应该检测到工作目录修改"
    else
        assert_true "false" "应该检测到工作目录修改"
    fi
    
    # 暂存修改
    git add README.md >/dev/null 2>&1
    
    # 测试暂存区状态
    if ! git diff --cached --quiet; then
        assert_true "true" "应该检测到暂存区修改"
    else
        assert_true "false" "应该检测到暂存区修改"
    fi
    
    cleanup_test_env
}

# 测试Git分支操作
test_git_branch_operations() {
    test_case "测试Git分支操作"
    
    setup_test_env "git_branch_test"
    setup_test_git_repo
    
    # 获取当前分支
    local current_branch=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD)
    assert_true "$([ -n "$current_branch" ])" "应该能获取当前分支名"
    
    # 创建新分支
    git checkout -b "test-branch" >/dev/null 2>&1
    
    # 验证分支切换
    local new_branch=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD)
    assert_equals "test-branch" "$new_branch" "应该切换到新分支"
    
    # 切换回原分支
    git checkout "$current_branch" >/dev/null 2>&1
    
    # 验证分支切换回来
    local back_branch=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD)
    assert_equals "$current_branch" "$back_branch" "应该切换回原分支"
    
    cleanup_test_env
}

# 测试Git提交操作
test_git_commit_operations() {
    test_case "测试Git提交操作"
    
    setup_test_env "git_commit_test"
    setup_test_git_repo
    
    # 获取初始提交数
    local initial_commits=$(git rev-list --count HEAD)
    
    # 创建新文件并提交
    echo "test content" > "test_file.txt"
    git add test_file.txt >/dev/null 2>&1
    git commit -m "Add test file" >/dev/null 2>&1
    
    # 验证提交增加
    local new_commits=$(git rev-list --count HEAD)
    assert_equals "$((initial_commits + 1))" "$new_commits" "应该增加一个提交"
    
    # 验证最新提交信息
    local latest_commit_msg=$(git log -1 --pretty=format:"%s")
    assert_equals "Add test file" "$latest_commit_msg" "提交信息应该正确"
    
    cleanup_test_env
}

# 测试Git差异检查
test_git_diff_operations() {
    test_case "测试Git差异检查"
    
    setup_test_env "git_diff_test"
    setup_test_git_repo
    
    # 修改文件
    echo "original content" > "test.txt"
    git add test.txt >/dev/null 2>&1
    git commit -m "Add test.txt" >/dev/null 2>&1
    
    # 修改文件内容
    echo "modified content" > "test.txt"
    
    # 检查差异
    local diff_output=$(git diff test.txt)
    assert_contains "$diff_output" "modified content" "差异应该包含新内容"
    assert_contains "$diff_output" "original content" "差异应该包含原内容"
    
    # 检查文件状态
    local status_output=$(git status --porcelain test.txt)
    assert_contains "$status_output" "M" "文件状态应该显示为修改"
    
    cleanup_test_env
}

# 测试Git远程操作检查
test_git_remote_check() {
    test_case "测试Git远程检查"
    
    setup_test_env "git_remote_test"
    setup_test_git_repo
    
    # 检查远程仓库（应该没有）
    local remotes=$(git remote)
    assert_equals "" "$remotes" "新仓库应该没有远程仓库"
    
    # 添加远程仓库
    git remote add origin "https://github.com/test/test.git" >/dev/null 2>&1
    
    # 验证远程仓库添加
    local new_remotes=$(git remote)
    assert_contains "$new_remotes" "origin" "应该添加origin远程仓库"
    
    # 检查远程URL
    local remote_url=$(git remote get-url origin)
    assert_contains "$remote_url" "github.com" "远程URL应该正确"
    
    cleanup_test_env
}

# 测试Git日志操作
test_git_log_operations() {
    test_case "测试Git日志操作"
    
    setup_test_env "git_log_test"
    setup_test_git_repo
    
    # 添加更多提交
    echo "file1" > "file1.txt"
    git add file1.txt >/dev/null 2>&1
    git commit -m "Add file1" >/dev/null 2>&1
    
    echo "file2" > "file2.txt"
    git add file2.txt >/dev/null 2>&1
    git commit -m "Add file2" >/dev/null 2>&1
    
    # 检查日志
    local log_output=$(git log --oneline)
    assert_contains "$log_output" "Add file1" "日志应该包含file1提交"
    assert_contains "$log_output" "Add file2" "日志应该包含file2提交"
    assert_contains "$log_output" "Initial commit" "日志应该包含初始提交"
    
    # 检查提交数量
    local commit_count=$(git rev-list --count HEAD)
    assert_equals "3" "$commit_count" "应该有3个提交"
    
    cleanup_test_env
}

# 测试Git标签操作
test_git_tag_operations() {
    test_case "测试Git标签操作"
    
    setup_test_env "git_tag_test"
    setup_test_git_repo
    
    # 创建标签
    git tag "v1.0.0" >/dev/null 2>&1
    
    # 验证标签创建
    local tags=$(git tag)
    assert_contains "$tags" "v1.0.0" "应该创建标签v1.0.0"
    
    # 创建带注释的标签
    git tag -a "v1.1.0" -m "Version 1.1.0" >/dev/null 2>&1
    
    # 验证注释标签
    local annotated_tags=$(git tag)
    assert_contains "$annotated_tags" "v1.1.0" "应该创建注释标签v1.1.0"
    
    # 检查标签信息
    local tag_info=$(git show v1.1.0 --format="%s" -s)
    assert_contains "$tag_info" "Version 1.1.0" "标签应该包含注释信息"
    
    cleanup_test_env
}

# 主测试函数
main() {
    test_suite "Git管理功能测试"
    
    # 检查Git是否可用
    if ! command -v git >/dev/null 2>&1; then
        echo "Git不可用，跳过Git相关测试"
        return 0
    fi
    
    test_git_stash_creation
    test_git_stash_restoration
    test_git_status_check
    test_git_branch_operations
    test_git_commit_operations
    test_git_diff_operations
    test_git_remote_check
    test_git_log_operations
    test_git_tag_operations
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
