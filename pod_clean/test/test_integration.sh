#!/bin/bash

# Pod库优化工具 - 集成测试
# 功能: 测试整个系统的集成功能和端到端流程
# 版本: v1.0

# 加载测试框架
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$TEST_DIR/test_framework.sh"

# 项目路径
PROJECT_ROOT="$TEST_PROJECT_ROOT"
MAIN_SCRIPT="$PROJECT_ROOT/pod_optimizer.sh"

# 创建完整的测试环境
create_full_test_environment() {
    local env_name="$1"
    
    setup_test_env "$env_name"
    
    # 创建复杂的Pod结构
    mkdir -p "TestPod1/Classes"
    mkdir -p "TestPod1/Resources/Images"
    mkdir -p "TestPod1/Assets.xcassets/icon.imageset"
    mkdir -p "TestPod2/Classes"
    mkdir -p "TestPod2/Resources"
    
    # 创建Podspec文件
    cat > "TestPod1/TestPod1.podspec" << 'EOF'
Pod::Spec.new do |s|
  s.name         = "TestPod1"
  s.version      = "1.0.0"
  s.summary      = "Test Pod 1"
  s.homepage     = "https://example.com"
  s.license      = "MIT"
  s.author       = "Test Author"
  s.source       = { :git => "https://github.com/test/TestPod1.git", :tag => "1.0.0" }
  s.source_files = "Classes/**/*.{h,m}"
  s.resources    = "Resources/**/*"
end
EOF

    cat > "TestPod2/TestPod2.podspec" << 'EOF'
Pod::Spec.new do |s|
  s.name         = "TestPod2"
  s.version      = "1.0.0"
  s.summary      = "Test Pod 2"
  s.homepage     = "https://example.com"
  s.license      = "MIT"
  s.author       = "Test Author"
  s.source       = { :git => "https://github.com/test/TestPod2.git", :tag => "1.0.0" }
  s.source_files = "Classes/**/*.{h,m}"
  s.resources    = "Resources/**/*"
end
EOF
    
    # 创建代码文件
    cat > "TestPod1/Classes/TestPod1.h" << 'EOF'
#import <Foundation/Foundation.h>

@interface TestPod1 : NSObject
- (UIImage *)getIconImage;
- (UIImage *)getBackgroundImage;
@end
EOF

    cat > "TestPod1/Classes/TestPod1.m" << 'EOF'
#import "TestPod1.h"

@implementation TestPod1

- (UIImage *)getIconImage {
    return [UIImage imageNamed:@"icon"];
}

- (UIImage *)getBackgroundImage {
    return [UIImage imageNamed:@"background"];
}

@end
EOF

    # 创建图片文件
    dd if=/dev/zero of="TestPod1/Resources/Images/background.png" bs=4096 count=1 2>/dev/null
    dd if=/dev/zero of="TestPod1/Resources/Images/unused_image.png" bs=2048 count=1 2>/dev/null
    dd if=/dev/zero of="TestPod1/Assets.xcassets/icon.imageset/icon.png" bs=1024 count=1 2>/dev/null
    dd if=/dev/zero of="TestPod1/Assets.xcassets/icon.imageset/<EMAIL>" bs=2048 count=1 2>/dev/null
    dd if=/dev/zero of="TestPod1/Assets.xcassets/icon.imageset/<EMAIL>" bs=3072 count=1 2>/dev/null
    
    # 创建Contents.json
    cat > "TestPod1/Assets.xcassets/icon.imageset/Contents.json" << 'EOF'
{
  "images": [
    {
      "filename": "icon.png",
      "idiom": "universal",
      "scale": "1x"
    },
    {
      "filename": "<EMAIL>",
      "idiom": "universal",
      "scale": "2x"
    },
    {
      "filename": "<EMAIL>",
      "idiom": "universal",
      "scale": "3x"
    }
  ],
  "info": {
    "author": "xcode",
    "version": 1
  }
}
EOF

    # 创建TestPod2的文件
    dd if=/dev/zero of="TestPod2/Resources/large_image.jpg" bs=8192 count=1 2>/dev/null
    dd if=/dev/zero of="TestPod2/Resources/medium_image.png" bs=4096 count=1 2>/dev/null
    
    echo "#import <Foundation/Foundation.h>" > "TestPod2/Classes/TestPod2.h"
    echo "#import \"TestPod2.h\"" > "TestPod2/Classes/TestPod2.m"
}

# 测试完整的优化流程
test_full_optimization_workflow() {
    test_case "测试完整优化流程"
    
    create_full_test_environment "full_workflow_test"
    
    # 设置环境变量
    export DB_PATH="$TEST_ENV_DIR/test.db"
    
    # 测试数据库初始化
    if source "$PROJECT_ROOT/scripts/lib/database.sh" 2>/dev/null; then
        init_database
        assert_file_exists "$DB_PATH" "数据库应该被创建"
    fi
    
    # 测试图片扫描
    if source "$PROJECT_ROOT/scripts/lib/common.sh" 2>/dev/null; then
        local all_images=$(scan_pod_images "TestPod1" "all" 2>/dev/null || echo "")
        assert_contains "$all_images" "background.png" "应该找到背景图片"
        assert_contains "$all_images" "icon.png" "应该找到图标图片"
    fi
    
    # 测试无引用检测
    if source "$PROJECT_ROOT/scripts/lib/unused_detector.sh" 2>/dev/null; then
        cd "TestPod1"
        local unused_file=$(find_unused_images "." 2>/dev/null | tail -n1 || echo "")
        if [ -f "$unused_file" ]; then
            local unused_content=$(cat "$unused_file" 2>/dev/null || echo "")
            assert_contains "$unused_content" "unused_image.png" "应该检测到未使用的图片"
        fi
        cd ..
    fi
    
    cleanup_test_env
}

# 测试命令行接口集成
test_cli_integration() {
    test_case "测试命令行接口集成"
    
    create_full_test_environment "cli_integration_test"
    
    # 测试帮助命令
    local help_output=$(timeout 5s bash "$MAIN_SCRIPT" -h 2>&1 || echo "timeout")
    assert_contains "$help_output" "Pod库优化工具" "帮助输出应该包含工具名称"
    
    # 测试无效选项
    local invalid_output=$(timeout 5s bash "$MAIN_SCRIPT" --invalid 2>&1 || echo "")
    assert_contains "$invalid_output" "未知选项" "应该报告未知选项"
    
    # 测试类型选项（在测试环境中快速退出）
    cd "TestPod1"
    local type_output=$(timeout 10s bash "$MAIN_SCRIPT" -t compress 2>&1 || echo "timeout")
    assert_contains "$type_output" "测试优化类型" "应该识别优化类型"
    cd ..
    
    cleanup_test_env
}

# 测试数据持久化
test_data_persistence() {
    test_case "测试数据持久化"
    
    create_full_test_environment "persistence_test"
    
    # 设置数据库
    export DB_PATH="$TEST_ENV_DIR/test.db"
    
    if source "$PROJECT_ROOT/scripts/lib/database.sh" 2>/dev/null; then
        init_database
        
        # 记录优化数据
        record_optimization "TestPod1" "compress" 2048 3
        record_file_optimization "test.png" "compress" 1024 512 50.0
        record_deletion_candidate "TestPod1" "unused.png"
        
        # 验证数据持久化
        local history_count=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM optimization_history;" 2>/dev/null || echo "0")
        assert_equals "1" "$history_count" "应该有一条优化历史"
        
        local file_count=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM file_optimizations;" 2>/dev/null || echo "0")
        assert_equals "1" "$file_count" "应该有一条文件优化记录"
        
        local deletion_count=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM deletion_progress;" 2>/dev/null || echo "0")
        assert_equals "1" "$deletion_count" "应该有一条删除进度记录"
    fi
    
    cleanup_test_env
}

# 测试备份和恢复
test_backup_and_recovery() {
    test_case "测试备份和恢复"
    
    create_full_test_environment "backup_recovery_test"
    
    # 设置备份目录
    local backup_dir="$TEST_ENV_DIR/backup"
    mkdir -p "$backup_dir"
    
    if source "$PROJECT_ROOT/scripts/lib/common.sh" 2>/dev/null; then
        # 测试文件备份
        local test_file="TestPod1/Resources/Images/background.png"
        local original_size=$(stat -f%z "$test_file" 2>/dev/null || stat -c%s "$test_file")
        
        backup_file_preserve_structure "$test_file" "$backup_dir"
        assert_file_exists "$backup_dir/$test_file" "备份文件应该存在"
        
        # 验证备份完整性
        local backup_size=$(stat -f%z "$backup_dir/$test_file" 2>/dev/null || stat -c%s "$backup_dir/$test_file")
        assert_equals "$original_size" "$backup_size" "备份文件大小应该与原文件相同"
        
        # 测试恢复
        echo "modified" > "$test_file"
        cp "$backup_dir/$test_file" "$test_file"
        
        local restored_size=$(stat -f%z "$test_file" 2>/dev/null || stat -c%s "$test_file")
        assert_equals "$original_size" "$restored_size" "恢复后文件大小应该正确"
    fi
    
    cleanup_test_env
}

# 测试.imageset删除集成
test_imageset_deletion_integration() {
    test_case "测试.imageset删除集成"
    
    create_full_test_environment "imageset_integration_test"
    
    # 设置数据库和备份
    export DB_PATH="$TEST_ENV_DIR/test.db"
    local backup_dir="$TEST_ENV_DIR/backup"
    mkdir -p "$backup_dir"
    
    if source "$PROJECT_ROOT/scripts/lib/database.sh" 2>/dev/null && \
       source "$PROJECT_ROOT/scripts/lib/unused_detector.sh" 2>/dev/null; then
        
        init_database
        
        # 验证.imageset目录存在
        assert_dir_exists "TestPod1/Assets.xcassets/icon.imageset" ".imageset目录应该存在"
        assert_file_exists "TestPod1/Assets.xcassets/icon.imageset/Contents.json" "Contents.json应该存在"
        
        # 计算目录大小
        local imageset_dir="TestPod1/Assets.xcassets/icon.imageset"
        local dir_size=$(calculate_directory_size "$imageset_dir")
        assert_true "$([ "$dir_size" -gt 0 ])" ".imageset目录应该有内容"
        
        # 模拟删除.imageset目录
        backup_file_preserve_structure "$imageset_dir" "$backup_dir"
        rm -rf "$imageset_dir"
        
        # 验证删除和备份
        assert_dir_not_exists "$imageset_dir" ".imageset目录应该被删除"
        assert_dir_exists "$backup_dir/$imageset_dir" "备份的.imageset目录应该存在"
        assert_file_exists "$backup_dir/$imageset_dir/Contents.json" "备份的Contents.json应该存在"
    fi
    
    cleanup_test_env
}

# 测试错误处理和恢复
test_error_handling_integration() {
    test_case "测试错误处理集成"
    
    create_full_test_environment "error_handling_test"
    
    # 测试权限错误
    mkdir -p "readonly_pod"
    chmod 444 "readonly_pod" 2>/dev/null || true
    
    if source "$PROJECT_ROOT/scripts/lib/common.sh" 2>/dev/null; then
        # 尝试在只读目录中备份文件应该失败
        if backup_file_preserve_structure "readonly_pod/test.txt" "/tmp/backup" 2>/dev/null; then
            assert_true "false" "只读目录操作应该失败"
        else
            assert_true "true" "只读目录操作应该失败"
        fi
    fi
    
    # 恢复权限
    chmod 755 "readonly_pod" 2>/dev/null || true
    
    # 测试数据库错误
    export DB_PATH="/invalid/path/test.db"
    if source "$PROJECT_ROOT/scripts/lib/database.sh" 2>/dev/null; then
        # 数据库操作应该优雅地失败
        record_optimization "TestPod" "compress" 1024 5 2>/dev/null || true
        assert_true "true" "数据库错误应该被优雅处理"
    fi
    
    cleanup_test_env
}

# 测试性能和并发
test_performance_and_concurrency() {
    test_case "测试性能和并发"
    
    create_full_test_environment "performance_test"
    
    if source "$PROJECT_ROOT/scripts/lib/common.sh" 2>/dev/null; then
        # 测试并发支持
        local concurrent_jobs=$(get_concurrent_jobs)
        assert_true "$([ "$concurrent_jobs" -gt 0 ])" "并发任务数应该大于0"
        
        local parallel_support=$(check_parallel_support)
        assert_true "$([ -n "$parallel_support" ])" "应该有并行支持"
        
        # 测试大量文件处理
        mkdir -p "performance_test"
        for i in {1..10}; do
            dd if=/dev/zero of="performance_test/file$i.png" bs=1024 count=1 2>/dev/null
        done
        
        # 测试扫描性能
        local start_time=$(date +%s)
        local files=$(scan_pod_images "performance_test" "all")
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        assert_true "$([ "$duration" -lt 10 ])" "扫描应该在合理时间内完成"
        
        local file_count=$(echo "$files" | wc -l)
        assert_equals "10" "$file_count" "应该找到所有文件"
    fi
    
    cleanup_test_env
}

# 测试报告生成
test_report_generation() {
    test_case "测试报告生成"
    
    create_full_test_environment "report_test"
    
    # 设置数据库
    export DB_PATH="$TEST_ENV_DIR/test.db"
    
    if source "$PROJECT_ROOT/scripts/lib/database.sh" 2>/dev/null; then
        init_database
        
        # 添加测试数据
        record_optimization "TestPod1" "compress" 2048 5
        record_optimization "TestPod2" "delete" 4096 3
        record_file_optimization "test1.png" "compress" 1024 512 50.0
        record_file_optimization "test2.jpg" "compress" 2048 1024 50.0
        
        # 测试历史报告
        local history_output=$(show_optimization_history 2>&1)
        assert_contains "$history_output" "TestPod1" "报告应该包含TestPod1"
        assert_contains "$history_output" "TestPod2" "报告应该包含TestPod2"
        assert_contains "$history_output" "compress" "报告应该包含压缩类型"
        assert_contains "$history_output" "delete" "报告应该包含删除类型"
        
        # 验证统计信息
        assert_contains "$history_output" "2048" "报告应该包含节省的字节数"
        assert_contains "$history_output" "4096" "报告应该包含节省的字节数"
    fi
    
    cleanup_test_env
}

# 主测试函数
main() {
    test_suite "集成测试"
    
    test_full_optimization_workflow
    test_cli_integration
    test_data_persistence
    test_backup_and_recovery
    test_imageset_deletion_integration
    test_error_handling_integration
    test_performance_and_concurrency
    test_report_generation
    
    test_summary
}

# 运行测试
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
