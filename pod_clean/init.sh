#!/bin/bash

# Pod库优化工具依赖安装脚本
# 自动检测系统环境并安装所有必要的依赖工具和包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              Pod库优化工具依赖安装器 v1.0                      ║"
    echo "║                                                              ║"
    echo "║  功能: 自动检测并安装所有必要的依赖工具和包                     ║"
    echo "║  支持: macOS (Homebrew), Ubuntu/Debian (apt), CentOS (yum)   ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "检测到操作系统: macOS"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
        log_info "检测到操作系统: Debian/Ubuntu"
    elif [[ -f /etc/redhat-release ]]; then
        OS="redhat"
        log_info "检测到操作系统: RedHat/CentOS/Fedora"
    else
        OS="unknown"
        log_warning "未知操作系统，将尝试通用安装方法"
    fi
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 安装包管理器
install_package_manager() {
    case "$OS" in
        "macos")
            if ! command_exists brew; then
                log_step "安装 Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
                
                # 添加到PATH
                if [[ -f "/opt/homebrew/bin/brew" ]]; then
                    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
                    eval "$(/opt/homebrew/bin/brew shellenv)"
                elif [[ -f "/usr/local/bin/brew" ]]; then
                    echo 'eval "$(/usr/local/bin/brew shellenv)"' >> ~/.zprofile
                    eval "$(/usr/local/bin/brew shellenv)"
                fi
                
                log_success "Homebrew 安装完成"
            else
                log_info "Homebrew 已安装"
            fi
            ;;
        "debian")
            log_step "更新包管理器..."
            sudo apt-get update
            ;;
        "redhat")
            log_step "更新包管理器..."
            sudo yum update -y
            ;;
    esac
}

# 安装系统工具
install_system_tools() {
    log_step "安装系统工具..."
    
    local tools_to_install=()
    
    # 检查基础工具
    if ! command_exists git; then
        tools_to_install+=("git")
    fi
    
    if ! command_exists curl; then
        tools_to_install+=("curl")
    fi
    
    if ! command_exists wget; then
        tools_to_install+=("wget")
    fi
    
    if ! command_exists sqlite3; then
        case "$OS" in
            "macos") tools_to_install+=("sqlite") ;;
            "debian") tools_to_install+=("sqlite3") ;;
            "redhat") tools_to_install+=("sqlite") ;;
        esac
    fi
    
    # 检查bc计算器
    if ! command_exists bc; then
        tools_to_install+=("bc")
    fi

    # JSON 处理器（用于更新 Assets 的 Contents.json）
    if ! command_exists jq; then
        tools_to_install+=("jq")
    fi
    
    # 安装工具
    if [ ${#tools_to_install[@]} -gt 0 ]; then
        case "$OS" in
            "macos")
                log_info "使用 Homebrew 安装: ${tools_to_install[*]}"
                brew install "${tools_to_install[@]}"
                ;;
            "debian")
                log_info "使用 apt 安装: ${tools_to_install[*]}"
                sudo apt-get install -y "${tools_to_install[@]}"
                ;;
            "redhat")
                log_info "使用 yum 安装: ${tools_to_install[*]}"
                sudo yum install -y "${tools_to_install[@]}"
                ;;
        esac
        log_success "系统工具安装完成"
    else
        log_info "所有系统工具已安装"
    fi
}

# 安装图片优化工具
install_image_tools() {
    log_step "安装图片优化工具..."
    
    local image_tools=()
    
    # 检查pngquant
    if ! command_exists pngquant; then
        image_tools+=("pngquant")
    fi
    
    # 检查jpegoptim
    if ! command_exists jpegoptim; then
        image_tools+=("jpegoptim")
    fi
    
    # 检查coreutils (for md5sum on macOS)
    if [[ "$OS" == "macos" ]] && ! command_exists md5sum; then
        image_tools+=("coreutils")
    fi
    
    # 检查ripgrep (高性能搜索工具)
    if ! command_exists rg; then
        image_tools+=("ripgrep")
    fi

    # HEIC 支持：ImageMagick + libheif / heif-enc
    # 优先安装 ImageMagick（提供 magick 命令），并确保系统安装了 libheif 或 heif-enc
    if ! command_exists magick; then
        case "$OS" in
            "macos") image_tools+=("imagemagick") ;;
            "debian") image_tools+=("imagemagick") ;;
            "redhat") image_tools+=("ImageMagick") ;;
        esac
    fi
    # 安装 libheif / heif-enc 以启用 HEIC 编码
    case "$OS" in
        "macos")
            if ! command_exists heif-enc; then
                image_tools+=("libheif")
            fi
            ;;
        "debian")
            # heif-enc 来自 libheif-examples
            if ! command_exists heif-enc; then
                image_tools+=("libheif-examples" "libheif1")
            fi
            ;;
        "redhat")
            # 不同发行版包名可能不同，尝试常见组合
            if ! command_exists heif-enc; then
                image_tools+=("libheif" "libheif-tools")
            fi
            ;;
    esac
    
    # 安装图片优化工具
    if [ ${#image_tools[@]} -gt 0 ]; then
        case "$OS" in
            "macos")
                log_info "使用 Homebrew 安装图片优化工具: ${image_tools[*]}"
                brew install "${image_tools[@]}"
                ;;
            "debian")
                log_info "使用 apt 安装图片优化工具: ${image_tools[*]}"
                sudo apt-get install -y "${image_tools[@]}"
                ;;
            "redhat")
                log_info "使用 yum 安装图片优化工具: ${image_tools[*]}"
                sudo yum install -y "${image_tools[@]}"
                ;;
        esac
        log_success "图片优化工具安装完成"
    else
        log_info "所有图片优化工具已安装"
    fi
}

# 安装Python和pip
install_python() {
    log_step "检查Python环境..."
    
    if ! command_exists python3; then
        log_info "安装 Python 3..."
        case "$OS" in
            "macos")
                brew install python
                ;;
            "debian")
                sudo apt-get install -y python3 python3-pip python3-dev
                ;;
            "redhat")
                sudo yum install -y python3 python3-pip python3-devel
                ;;
        esac
        log_success "Python 3 安装完成"
    else
        log_info "Python 3 已安装: $(python3 --version)"
    fi
    
    # 检查pip
    if ! command_exists pip3; then
        log_info "安装 pip3..."
        case "$OS" in
            "macos")
                # Homebrew Python 通常包含pip
                if ! command_exists pip3; then
                    python3 -m ensurepip --upgrade
                fi
                ;;
            "debian")
                sudo apt-get install -y python3-pip
                ;;
            "redhat")
                sudo yum install -y python3-pip
                ;;
        esac
        log_success "pip3 安装完成"
    else
        log_info "pip3 已安装: $(pip3 --version)"
    fi
}

# 安装Python依赖包
install_python_packages() {
    log_step "安装Python依赖包..."
    
    local python_packages=(
        "opencv-python"
        "numpy"
        "scikit-image"
        "Pillow"
    )
    
    local missing_packages=()
    
    # 检查每个包
    for package in "${python_packages[@]}"; do
        if ! python3 -c "import ${package//-/_}" 2>/dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    # 安装缺少的包
    if [ ${#missing_packages[@]} -gt 0 ]; then
        log_info "安装Python包: ${missing_packages[*]}"
        
        # 升级pip
        python3 -m pip install --upgrade pip
        
        # 安装包
        python3 -m pip install "${missing_packages[@]}"
        
        log_success "Python依赖包安装完成"
    else
        log_info "所有Python依赖包已安装"
    fi
}

# 验证安装
verify_installation() {
    log_step "验证安装..."
    
    local verification_failed=false
    
    # 验证系统工具
    local system_tools=("git" "curl" "sqlite3" "bc")
    for tool in "${system_tools[@]}"; do
        if command_exists "$tool"; then
            log_success "✓ $tool: $(command -v "$tool")"
        else
            log_error "✗ $tool: 未安装"
            verification_failed=true
        fi
    done
    
    # 验证图片优化工具（含 HEIC 相关）
    local image_tools=("pngquant" "jpegoptim" "magick" "rg")
    for tool in "${image_tools[@]}"; do
        if command_exists "$tool"; then
            log_success "✓ $tool: $(command -v "$tool")"
        else
            log_error "✗ $tool: 未安装"
            verification_failed=true
        fi
    done

    # 验证 heif-enc（若缺失则提示，但不阻断）
    if command_exists heif-enc; then
        log_success "✓ heif-enc: $(command -v heif-enc)"
    else
        log_warning "⚠ heif-enc 未检测到，将尝试使用 ImageMagick/sips 进行 HEIC 转换"
    fi
    
    # 验证md5sum (macOS特殊处理)
    if [[ "$OS" == "macos" ]]; then
        if command_exists md5sum || command_exists md5; then
            log_success "✓ md5工具: 可用"
        else
            log_error "✗ md5工具: 未安装"
            verification_failed=true
        fi
    else
        if command_exists md5sum; then
            log_success "✓ md5sum: $(command -v md5sum)"
        else
            log_error "✗ md5sum: 未安装"
            verification_failed=true
        fi
    fi
    
    # 验证Python
    if command_exists python3; then
        log_success "✓ Python 3: $(python3 --version)"
    else
        log_error "✗ Python 3: 未安装"
        verification_failed=true
    fi
    
    if command_exists pip3; then
        log_success "✓ pip3: $(pip3 --version)"
    else
        log_error "✗ pip3: 未安装"
        verification_failed=true
    fi
    
    # 验证Python包
    local python_packages=("cv2:opencv-python" "numpy:numpy" "skimage:scikit-image" "PIL:Pillow")
    for package_info in "${python_packages[@]}"; do
        local import_name="${package_info%%:*}"
        local package_name="${package_info##*:}"
        
        if python3 -c "import $import_name" 2>/dev/null; then
            log_success "✓ Python包 $package_name: 已安装"
        else
            log_error "✗ Python包 $package_name: 未安装"
            verification_failed=true
        fi
    done
    
    if [ "$verification_failed" = true ]; then
        log_error "部分依赖安装失败，请检查上述错误信息"
        return 1
    else
        log_success "所有依赖验证通过！"
        return 0
    fi
}

# 显示使用说明
show_usage_info() {
    echo ""
    echo -e "${CYAN}=== 安装完成！使用说明 ===${NC}"
    echo ""
    echo "现在您可以使用以下命令："
    echo ""
    echo -e "${GREEN}# 运行Pod库优化工具${NC}"
    echo "cd pod_clean"
    echo "./pod_optimizer.sh"
    echo ""
    echo -e "${GREEN}# 生成质量对比报告${NC}"
    echo "./scripts/generate_quality_report.sh"
    echo ""
    echo -e "${GREEN}# 命令行测试（推荐用于快速验证）${NC}"
    echo "./pod_optimizer.sh -t delete     # 测试删除无引用图片（并发优化）"
    echo "./pod_optimizer.sh -t compress   # 测试图片压缩"
    echo "./pod_optimizer.sh -t heic       # 测试HEIC转换"
    echo "./pod_optimizer.sh -t mixed      # 测试混合优化"
    echo ""
    echo -e "${GREEN}# 启用 HEIC 转换优化（推荐）${NC}"
    echo "./pod_optimizer.sh  # 菜单选择: 转换为HEIC"
    echo "或在批量/选择性优化时选择 HEIC 选项"
    echo ""
    echo -e "${GREEN}# 查看帮助信息${NC}"
    echo "./pod_optimizer.sh --help"
    echo ""
    echo -e "${YELLOW}注意事项：${NC}"
    echo "1. 首次运行前请确保您有足够的磁盘空间用于备份"
    echo "2. 建议在Git仓库中运行，以便版本控制"
    echo "3. 大型项目优化可能需要较长时间，请耐心等待"
    echo ""
}

# 主函数
main() {
    show_banner
    
    # 检测操作系统
    detect_os
    
    # 安装包管理器
    install_package_manager
    
    # 安装系统工具
    install_system_tools
    
    # 安装图片优化工具
    install_image_tools
    
    # 安装Python环境
    install_python
    
    # 安装Python依赖包
    install_python_packages
    
    # 验证安装
    if verify_installation; then
        show_usage_info
        log_success "依赖安装完成！您现在可以使用Pod库优化工具了。"
    else
        log_error "依赖安装过程中出现问题，请检查错误信息并重新运行。"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        echo "Pod库优化工具依赖安装器"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  -h, --help     显示帮助信息"
        echo "  -v, --verify   仅验证依赖，不安装"
        echo ""
        echo "支持的系统:"
        echo "  - macOS (使用 Homebrew)"
        echo "  - Ubuntu/Debian (使用 apt)"
        echo "  - CentOS/RHEL/Fedora (使用 yum)"
        echo ""
        echo "安装的依赖:"
        echo "  系统工具: git, curl, wget, sqlite3, bc"
        echo "  图片优化: pngquant, jpegoptim, coreutils"
        echo "  Python环境: python3, pip3"
        echo "  Python包: opencv-python, numpy, scikit-image, Pillow"
        ;;
    -v|--verify)
        show_banner
        detect_os
        verify_installation
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 $0 --help 查看帮助信息"
        exit 1
        ;;
esac
