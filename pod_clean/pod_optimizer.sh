#!/bin/bash

# Pod库优化工具 - 主入口脚本
# 版本: v6.0
# 
# 功能概述:
#   - 统一入口调度器，支持完整优化、增量优化、重置等功能
#   - 模块化架构，核心逻辑分离到lib/和modules/目录
#   - 支持批量优化、选择性优化、Git贮藏管理、质量报告生成
#
# 架构说明:
#   - lib/: 核心功能库（通用工具、数据库、图片处理、Git管理等）
#   - modules/: 业务模块（批量优化、选择性优化等）
#   - scripts/: 独立功能脚本（报告生成、质量对比等）
#
# 主要功能:
#   1. 批量优化: 处理所有Pod库，支持压缩、删除、HEIC转换等模式
#   2. 选择性优化: 交互式选择特定Pod库进行优化
#   3. Git贮藏管理: 优化前后的版本控制和回滚
#   4. 数据库记录: SQLite记录优化历史，支持增量优化
#   5. 质量报告: 自动生成优化效果和质量对比报告
#
# 依赖工具:
#   - 必需: sqlite3, find, stat, md5/md5sum
#   - 图片处理: pngquant, jpegoptim, ImageMagick/sips
#   - 加速工具: ripgrep, GNU parallel (可选)
#
# 用法示例:
#   ./pod_optimizer.sh              # 交互式菜单
#   ./pod_optimizer.sh -b           # 批量优化所有Pod
#   ./pod_optimizer.sh -s           # 选择性优化
#   ./pod_optimizer.sh -t compress  # 测试压缩模式
#   ./pod_optimizer.sh --stash      # Git贮藏管理
#   ./pod_optimizer.sh -H           # 查看优化历史

set -e

# 脚本目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"
LIB_DIR="$SCRIPTS_DIR/lib"
MODULES_DIR="$SCRIPTS_DIR/modules"
BACKUP_ROOT="$SCRIPT_DIR/backups"

# 加载核心库
source "$LIB_DIR/common.sh"
source "$LIB_DIR/database.sh"
source "$LIB_DIR/image_processor.sh"
source "$LIB_DIR/unused_detector.sh"
source "$LIB_DIR/git_manager.sh"
source "$LIB_DIR/sync_manager.sh"

# 加载业务模块
source "$MODULES_DIR/batch_optimizer.sh"
source "$MODULES_DIR/selective_optimizer.sh"

# 显示帮助信息
show_help() {
    echo "Pod库优化工具 v6.0"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -b, --batch         批量优化所有Pod库"
    echo "  -s, --selective     选择性优化Pod库"
    echo "  -t, --type <类型>   测试指定优化类型"
    echo "      支持类型: compress, delete, mixed, heic"
    echo "  --stash             Git贮藏管理"
    echo "  -H, --history       显示优化历史"
    echo "  --reset             重置优化记录"
    echo "  -q, --quality       生成质量对比报告"
    echo "  --submit            提交优化↑ (导出CSV并推送到远端)"
    echo "  --sync              同步优化↓ (拉取远端CSV并合并到本地)"
    echo ""
    echo "示例:"
    echo "  $0 -t delete        测试删除无引用图片"
    echo "  $0 -t compress      测试压缩图片"
    echo "  $0 -t heic          测试HEIC转换"
    echo ""
    echo "交互模式:"
    echo "  $0                  进入交互式菜单"
}

# 交互式菜单
interactive_menu() {
    while true; do
        echo ""
        echo "=== Pod库优化工具菜单 ==="
        echo "1. 全部优化 (处理所有Pod库，内部增量优化)"
        echo "2. 选择性优化 (手动选择要优化的Pod库)"
        echo "3. Git贮藏管理"
        echo "4. 查看优化历史"
        echo "5. 生成质量对比报告"
        echo "6. 查看删除进度"
        echo "7. 清理删除记录"
        echo "8. 重置优化记录"
        echo "9. 提交优化↑ (导出CSV并推送到远端)"
        echo "10. 同步优化↓ (拉取远端CSV并合并到本地)"
        echo "11. 退出"
        echo -n "请选择: "
        read -r choice

        case "$choice" in
            1)
                batch_optimize_pods
                ;;
            2)
                selective_optimize_pods
                ;;
            3)
                git_stash_optimization
                ;;
            4)
                show_optimization_history
                ;;
            5)
                log_info "启动质量对比报告生成器..."
                "$SCRIPTS_DIR/generate_quality_report.sh"
                ;;
            6)
                show_deletion_progress
                ;;
            7)
                clean_deletion_progress
                ;;
            8)
                reset_optimization_records
                ;;
            9)
                submit_optimization_updates
                ;;
            10)
                sync_optimization_updates
                ;;
            11)
                log_info "退出程序"
                break
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 主函数
main() {
    show_banner
    
    # 初始化数据库
    init_database
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -b|--batch)
            batch_optimize_pods
            ;;
        -s|--selective)
            selective_optimize_pods
            ;;
        -t|--type)
            if [ -n "${2:-}" ]; then
                # 单独的优化类型测试
                local optimization_type="$2"
                case "$optimization_type" in
                    "compress"|"delete"|"mixed"|"heic")
                        echo "测试优化类型: $optimization_type"
                        local current_pod_name="$(basename "$PWD")"
                        local current_pod_dir="$PWD"
                        local test_backup_dir="/tmp/pod_optimizer_test_$(date +%Y%m%d_%H%M%S)"
                        mkdir -p "$test_backup_dir"
                        echo "备份目录: $test_backup_dir"
                        optimize_pod "$current_pod_name" "$current_pod_dir" "$test_backup_dir" "$optimization_type"
                        ;;
                    *)
                        log_error "无效的优化类型: $optimization_type"
                        echo "支持的类型: compress, delete, mixed, heic"
                        exit 1
                        ;;
                esac
            else
                log_error "-t 选项需要指定优化类型"
                echo "用法: $0 -t <compress|delete|mixed|heic>"
                exit 1
            fi
            ;;
        --stash)
            git_stash_optimization
            ;;
        -H|--history)
            show_optimization_history
            ;;
        --reset)
            reset_optimization_records
            ;;
        -q|--quality)
            "$SCRIPTS_DIR/generate_quality_report.sh"
            ;;
        --submit)
            submit_optimization_updates
            ;;
        --sync)
            sync_optimization_updates
            ;;
        "")
            interactive_menu
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
