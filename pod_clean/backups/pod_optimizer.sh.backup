#!/bin/bash

# Pod库优化工具 - 主入口脚本
# 版本: v5.0
# 功能: 统一入口，支持完整优化、增量优化、重置等功能

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"
DB_DIR="$SCRIPT_DIR/db"
DB_FILE="$DB_DIR/optimization.db"
BACKUP_ROOT="$SCRIPT_DIR/backups"

# 全局记录最近一次优化（用于新类型heic准确统计）
LAST_OPTIMIZATION_SAVED=0
LAST_OPTIMIZATION_COUNT=0
HEIC_QUALITY=${HEIC_QUALITY:-70}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 初始化数据库
init_database() {
    if [ ! -f "$DB_FILE" ]; then
        log_info "初始化优化数据库..."
        mkdir -p "$DB_DIR"
        sqlite3 "$DB_FILE" << 'EOF'
CREATE TABLE optimizations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pod_name TEXT NOT NULL,
    optimization_type TEXT NOT NULL,
    file_count INTEGER DEFAULT 0,
    space_saved INTEGER DEFAULT 0,
    optimization_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    backup_path TEXT,
    status TEXT DEFAULT 'completed'
);

CREATE TABLE optimization_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL UNIQUE,
    optimization_type TEXT NOT NULL,
    original_size INTEGER,
    optimized_size INTEGER,
    compression_ratio REAL,
    optimization_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE deletion_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pod_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    deletion_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(pod_name, file_path)
);

CREATE INDEX idx_pod_name ON optimizations(pod_name);
CREATE INDEX idx_optimization_time ON optimizations(optimization_time);
EOF
        log_success "数据库初始化完成"
    fi
}

# 数据库操作函数
is_file_optimized() {
    local file_path="$1"
    local optimization_type="$2"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    # 检查特定文件是否已经被优化过
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimization_files WHERE file_path='$file_path' AND optimization_type='$optimization_type';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

record_file_optimization() {
    local file_path="$1"
    local optimization_type="$2"
    local original_size="$3"
    local optimized_size="$4"
    local compression_ratio="$5"

    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT OR REPLACE INTO optimization_files (file_path, optimization_type, original_size, optimized_size, compression_ratio, optimization_time)
VALUES ('$file_path', '$optimization_type', $original_size, $optimized_size, $compression_ratio, CURRENT_TIMESTAMP);
EOF
    fi
}

record_optimization() {
    local pod_name="$1"
    local optimization_type="$2"
    local file_count="$3"
    local space_saved="$4"
    local backup_path="$5"

    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT INTO optimizations (pod_name, optimization_type, file_count, space_saved, backup_path)
VALUES ('$pod_name', '$optimization_type', $file_count, $space_saved, '$backup_path');
EOF
    fi
}

get_optimization_history() {
    local pod_name="$1"
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "SELECT optimization_time, file_count, space_saved FROM optimizations WHERE pod_name='$pod_name' ORDER BY optimization_time DESC LIMIT 5;" 2>/dev/null
    fi
}

# 检查Pod是否已优化
is_pod_optimized() {
    local pod_name="$1"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    
    # 检查数据库中是否有该Pod的优化记录
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimizations WHERE pod_name='$pod_name';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

# 删除进度管理函数
record_deletion_candidate() {
    local pod_name="$1"
    local file_path="$2"
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "INSERT OR REPLACE INTO deletion_progress (pod_name, file_path, status) VALUES ('$pod_name', '$file_path', 'pending');" 2>/dev/null
    fi
}

mark_deletion_completed() {
    local pod_name="$1"
    local file_path="$2"
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "UPDATE deletion_progress SET status='completed', deletion_time=CURRENT_TIMESTAMP WHERE pod_name='$pod_name' AND file_path='$file_path';" 2>/dev/null
    fi
}

is_deletion_completed() {
    local pod_name="$1"
    local file_path="$2"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM deletion_progress WHERE pod_name='$pod_name' AND file_path='$file_path' AND status='completed';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

# 单个图片引用检测（用于并发处理）
check_single_image_reference() {
    local file="$1"
    local pod_dir="$2"
    local result_file="$3"
    
    if [ ! -f "$file" ]; then
        return 0
    fi
    
    local filename=$(basename "$file")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    local file_ext=$(echo "$filename" | awk -F'.' '{print tolower($NF)}')

    # 移除常见的分辨率和状态后缀
    local clean_name=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//' | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 检查是否为动画序列帧
    if is_animation_sequence_frame "$file" "$pod_dir"; then
        echo "ANIMATION:$filename" >> "$result_file"
        return 0
    fi

    # 构建搜索模式
    local search_patterns=(
        "$filename"
        "$basename_no_ext"
        "$clean_name"
        "${clean_name}_"
    )
    
    # 对于常见的iOS图片引用模式，去掉分辨率后缀后的基础名称
    local base_name_for_ios=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//')
    if [ "$base_name_for_ios" != "$basename_no_ext" ]; then
        search_patterns+=("$base_name_for_ios")
    fi

    # 使用ripgrep（如果可用）或grep进行搜索
    local search_cmd="grep"
    local search_args="-r -l"
    
    if command -v rg >/dev/null 2>&1; then
        search_cmd="rg"
        search_args="--files-with-matches"
    fi
    
    # 构建文件类型过滤参数
    local file_includes=""
    if [ "$search_cmd" = "grep" ]; then
        file_includes="--include=*.m --include=*.h --include=*.swift --include=*.mm --include=*.xib --include=*.storyboard --include=*.plist --include=*.json --include=*.txt --include=*.strings --include=*.xml --include=*.css --include=*.js --include=*.ts --include=*.jsx --include=*.tsx --include=*.vue --include=*.dart --include=*.kt --include=*.java --include=*.cs --include=*.cpp --include=*.c --include=*.cc --include=*.rb --include=*.py --include=*.php --include=*.go --include=*.rs --include=*.scala --include=*.clj --include=*.sh --include=*.bat --include=*.ps1 --include=*.md --include=*.yml --include=*.yaml --include=*.toml --include=*.ini --include=*.cfg --include=*.conf --exclude=Contents.json"
    else
        file_includes="-t c -t cpp -t js -t ts -t py -t java -t swift -t objc -t json -t xml -t yaml -t toml"
    fi
    
    local found=false
    for pattern in "${search_patterns[@]}"; do
        if [ "$search_cmd" = "rg" ]; then
            if rg $search_args $file_includes --glob '!Contents.json' "$pattern" "$pod_dir" >/dev/null 2>&1; then
                found=true
                local reference_files=$(rg $search_args $file_includes --glob '!Contents.json' "$pattern" "$pod_dir" 2>/dev/null | head -3)
                echo "REFERENCED:$filename:$pattern:$(echo "$reference_files" | tr '\n' ' ')" >> "$result_file"
                break
            fi
        else
            if $search_cmd $search_args $file_includes "$pattern" "$pod_dir" >/dev/null 2>&1; then
                found=true
                local reference_files=$($search_cmd $search_args $file_includes "$pattern" "$pod_dir" 2>/dev/null | head -3)
                echo "REFERENCED:$filename:$pattern:$(echo "$reference_files" | tr '\n' ' ')" >> "$result_file"
                break
            fi
        fi
    done

    # 特殊处理：检查是否为 Asset Catalog 中的图片
    if [ "$found" = false ]; then
        local is_asset_catalog_image=false
        local file_dir=$(dirname "$file")
        
        # 检查是否在 .imageset 目录中
        if [[ "$file_dir" == *.imageset ]]; then
            # 提取 imageset 名称（去掉 .imageset 后缀）
            local imageset_name=$(basename "$file_dir" .imageset)
            
            # 在代码中搜索 Asset Catalog 引用
            if [ "$search_cmd" = "rg" ]; then
                if rg --files-with-matches -t c -t cpp -t swift -t objc --glob '!Contents.json' "$imageset_name" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local asset_reference_files=$(rg --files-with-matches -t c -t cpp -t swift -t objc --glob '!Contents.json' "$imageset_name" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Asset Catalog:$imageset_name:$(echo "$asset_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            else
                if grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --exclude="Contents.json" "$imageset_name" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local asset_reference_files=$(grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --exclude="Contents.json" "$imageset_name" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Asset Catalog:$imageset_name:$(echo "$asset_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            fi
        fi
        
        if [ "$is_asset_catalog_image" = false ]; then
            local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "UNUSED:$file:$size" >> "$result_file"
        fi
    fi
}

# 检测是否为动画序列帧
is_animation_sequence_frame() {
    local file_path="$1"
    local pod_dir="$2"
    local filename=$(basename "$file_path")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    
    # 检测常见的动画序列帧命名模式
    # 如: ani_good1, ani_good2, ..., ani_good12
    # 或: frame_01, frame_02, ..., frame_30
    # 或: loading_1, loading_2, ..., loading_8
    local base_pattern=""
    local sequence_number=""
    
    # 提取基础名称和数字
    if [[ "$basename_no_ext" =~ ^(.+[_-])([0-9]+)(@[0-9]x)?$ ]]; then
        base_pattern="${BASH_REMATCH[1]}"
        sequence_number="${BASH_REMATCH[2]}"
        local resolution_suffix="${BASH_REMATCH[3]}"
        
        # 在代码中搜索动画相关的用法模式
        # 1. 搜索循环加载序列帧的代码模式
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "(for|NSInteger|Int).*[0-9]+.*${base_pattern}|animationImages.*${base_pattern}|${base_pattern}.*images" \
           "$pod_dir" >/dev/null 2>&1; then
            return 0
        fi
        
        # 2. 搜索字符串格式化的模式 stringWithFormat:@"${base_pattern}%
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "stringWithFormat.*${base_pattern}.*%|String.*format.*${base_pattern}" \
           "$pod_dir" >/dev/null 2>&1; then
            return 0
        fi
        
        # 3. 检查是否有其他序列帧文件存在（至少3个连续编号）
        local sequence_count=0
        # 强制转换为十进制，避免八进制解析错误
        local sequence_num=$((10#$sequence_number))
        local start_num=$((sequence_num - 2))
        local end_num=$((sequence_num + 2))
        
        for i in $(seq $start_num $end_num); do
            if [ $i -le 0 ]; then continue; fi
            local test_name="${base_pattern}${i}${resolution_suffix}"
            local test_files=$(find "$pod_dir" -name "${test_name}.*" -type f 2>/dev/null)
            if [ -n "$test_files" ]; then
                sequence_count=$((sequence_count + 1))
            fi
        done
        
        # 如果找到3个或以上连续编号的文件，认为是动画序列帧
        if [ $sequence_count -ge 3 ]; then
            return 0
        fi
    fi
    
    return 1
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 备份工具函数：在备份目录中保留原始相对路径结构
backup_file_preserve_structure() {
    local src_file="$1"
    local backup_root="$2"

    # 移除前导的 ./ 或 /
    local rel_path="${src_file#./}"
    rel_path="${rel_path#/}"

    local dest_path="$backup_root/$rel_path"
    mkdir -p "$(dirname "$dest_path")"
    cp "$src_file" "$dest_path"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Pod库优化工具 v6.0                         ║"
    echo "║                                                              ║"
    echo "║  功能: 增量优化、选择性优化、Git贮藏、HEIC转换                   ║"
    echo "║  特性: SQLite记录、避免重复优化、自动报告生成                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 初始化数据库
init_database

# 深度扫描Pod目录中的所有图片文件 - 确保覆盖所有子目录
scan_pod_images() {
    local pod_dir="$1"
    local scan_type="$2"  # "all", "png", "jpg", "gif"

    case "$scan_type" in
        "all")
            find "$pod_dir" -type f \( -iname "*.png" -o -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.gif" -o -iname "*.webp" -o -iname "*.bmp" \) 2>/dev/null
            ;;
        "png")
            find "$pod_dir" -type f -iname "*.png" 2>/dev/null
            ;;
        "jpg")
            find "$pod_dir" -type f \( -iname "*.jpg" -o -iname "*.jpeg" \) 2>/dev/null
            ;;
        "gif")
            find "$pod_dir" -type f -iname "*.gif" 2>/dev/null
            ;;
        *)
            find "$pod_dir" -type f \( -iname "*.png" -o -iname "*.jpg" -o -iname "*.jpeg" \) 2>/dev/null
            ;;
    esac
}

# 判断图片是否包含Alpha通道（PNG用），JPG默认无Alpha
has_alpha_channel() {
    local file="$1"
    # macOS: sips 可用；通用：ImageMagick identify
    if command -v sips >/dev/null 2>&1; then
        local alpha=$(sips -g hasAlpha "$file" 2>/dev/null | awk '/hasAlpha/ {print $2}')
        [[ "$alpha" == "yes" ]]
        return $?
    elif command -v magick >/dev/null 2>&1; then
        local channels=$(magick identify -format "%[channels]" "$file" 2>/dev/null || echo "")
        [[ "$channels" == *"alpha"* ]]
        return $?
    fi
    # 无法判断时，保守认为有Alpha以避免破坏透明度
    return 0
}

# 在同目录的 Contents.json 中，将旧文件名更新为新文件名（Asset Catalog 兼容）
update_contents_json_for_heic() {
    local file_path="$1"         # 原始文件路径（png/jpg）
    local new_filename="$2"      # 新的 HEIC 文件名（仅文件名）

    local dir_path
    dir_path=$(dirname "$file_path")
    local json_path="$dir_path/Contents.json"

    if [ ! -f "$json_path" ]; then
        return 0
    fi

    local old_filename
    old_filename=$(basename "$file_path")

    # 备份 JSON
    backup_file_preserve_structure "$json_path" "$BACKUP_ROOT/contents_json_backups_$(date +%Y%m%d)"

    if command -v jq >/dev/null 2>&1; then
        local tmp
        tmp=$(mktemp)
        if jq --arg old "$old_filename" --arg new "$new_filename" '(.images[]? | select(.filename == $old) | .filename) = $new' "$json_path" > "$tmp" 2>/dev/null; then
            mv "$tmp" "$json_path"
            echo "    更新 Contents.json: $old_filename -> $new_filename"
            return 0
        else
            rm -f "$tmp" 2>/dev/null || true
        fi
    fi

    # jq 不可用时，使用 sed 简单替换（仅替换完全匹配的旧文件名）
    # 注意：此方法假设 JSON 中旧文件名不会出现在非 filename 字段
    if command -v gsed >/dev/null 2>&1; then
        gsed -i "s/\"$old_filename\"/\"$new_filename\"/g" "$json_path" 2>/dev/null || true
        echo "    更新 Contents.json: $old_filename -> $new_filename"
    else
        # macOS bsd sed
        sed -i '' "s/\"$old_filename\"/\"$new_filename\"/g" "$json_path" 2>/dev/null || true
        echo "    更新 Contents.json: $old_filename -> $new_filename"
    fi
}

# 将单个图片转换为HEIC（优先使用 ImageMagick，其次 sips，最后 heif-enc）
convert_one_to_heic() {
    local src="$1"
    local dst="$2"
    local quality="$3"

    # 优先 ImageMagick
    if command -v magick >/dev/null 2>&1; then
        magick "$src" -quality "$quality" -define heic:speed=1 -strip "$dst" 2>/dev/null && return 0
    fi

    # macOS 备选：sips（质量参数在部分系统不生效，仍可用作后备）
    if command -v sips >/dev/null 2>&1; then
        sips -s format heic "$src" --out "$dst" >/dev/null 2>&1 && return 0
    fi

    # heif-enc 备选
    if command -v heif-enc >/dev/null 2>&1; then
        # heif-enc 质量范围通常 0-100，数值越大越好
        heif-enc -q "$quality" "$src" -o "$dst" >/dev/null 2>&1 && return 0
    fi

    return 1
}

# 将Pod中的图片尽可能转换为HEIC，跳过含Alpha的PNG，并记录数据库
convert_pod_images_to_heic() {
    local pod_name="$1"
    local backup_dir="$2"

    local total_saved=0
    local converted_count=0

    echo "  扫描可转换为HEIC的图片..."
    scan_pod_images "." "all" | while read file; do
        if [ -f "$file" ]; then
            local ext_lower=$(echo "${file##*.}" | tr 'A-Z' 'a-z')
            # 仅处理 jpg/jpeg；png需无Alpha
            if [[ "$ext_lower" == "jpg" || "$ext_lower" == "jpeg" ]]; then
                :
            elif [[ "$ext_lower" == "png" ]]; then
                if has_alpha_channel "$file"; then
                    echo "    跳过含Alpha PNG: $(basename "$file")"
                    continue
                fi
            else
                continue
            fi

            # 已经是HEIC的跳过
            if [[ "$ext_lower" == "heic" ]]; then
                continue
            fi

            # 如果该文件之前进行过heic转换记录，则跳过
            if is_file_optimized "$file" "heic"; then
                echo "    跳过已转换: $(basename "$file")"
                continue
            fi

            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            local heic_path="${file%.*}.heic"

            # 执行转换
            if convert_one_to_heic "$file" "$heic_path" "$HEIC_QUALITY"; then
                if [ -f "$heic_path" ]; then
                    local new_size=$(stat -f%z "$heic_path" 2>/dev/null || stat -c%s "$heic_path")
                    if [ "$new_size" -gt 0 ] && [ "$new_size" -lt "$original_size" ]; then
                        # 备份原文件
                        backup_file_preserve_structure "$file" "$backup_dir"
                        # 用HEIC替换原图（删除原图，仅保留.heic）
                        rm -f "$file"
                        # 若存在 Contents.json，则同步更新文件名
                        update_contents_json_for_heic "$file" "$(basename "$heic_path")"
                        echo "    转换: $(basename "$heic_path") 节省 $(((original_size - new_size)/1024))KB"
                        total_saved=$((total_saved + (original_size - new_size)))
                        converted_count=$((converted_count + 1))
                        # 记录文件级优化
                        local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                        record_file_optimization "$heic_path" "heic" "$original_size" "$new_size" "$compression_ratio"
                    else
                        # 未节省空间或失败，删除生成的heic
                        rm -f "$heic_path" 2>/dev/null || true
                    fi
                fi
            else
                echo "    转换失败: $(basename "$file")"
            fi
        fi
    done

    LAST_OPTIMIZATION_SAVED=$((total_saved))
    LAST_OPTIMIZATION_COUNT=$((converted_count))

    # 返回码不可靠，保持与历史实现一致
    return $total_saved
}

# 检测重复图片文件
find_duplicate_images() {
    local pod_dir="$1"
    local temp_file=$(mktemp)
    local duplicates_file=$(mktemp)

    log_info "检测重复图片文件..."

    # 计算所有图片文件的MD5
    scan_pod_images "$pod_dir" "all" | while read file; do
        if [ -f "$file" ]; then
            local md5=$(md5 -q "$file" 2>/dev/null || md5sum "$file" | cut -d' ' -f1)
            local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "$md5:$size:$file"
        fi
    done | sort > "$temp_file"

    # 查找重复的MD5
    cut -d':' -f1 "$temp_file" | uniq -d | while read md5; do
        echo "=== 重复文件组 (MD5: $md5) ===" >> "$duplicates_file"
        grep "^$md5:" "$temp_file" | while IFS=':' read hash size filepath; do
            echo "  $filepath ($(($size / 1024))KB)" >> "$duplicates_file"
        done
        echo "" >> "$duplicates_file"
    done

    if [ -s "$duplicates_file" ]; then
        echo "发现重复图片文件:"
        cat "$duplicates_file"
        echo ""
        echo "重复文件详情已保存到: $duplicates_file"
    else
        log_success "未发现重复图片文件"
        rm -f "$duplicates_file"
    fi

    rm -f "$temp_file"
    echo "$duplicates_file"
}

# 检测无引用图片文件
find_unused_images() {
    local pod_dir="$1"
    local unused_file=$(mktemp)

    log_info "检测无引用图片文件..."

    # 获取总文件数用于进度显示
    local total_files=$(scan_pod_images "$pod_dir" "all" | wc -l | tr -d ' ')
    echo "  将扫描 $total_files 个图片文件以检测引用..."

    # 检查是否支持并发处理
    local concurrent_jobs=1
    local use_parallel=false
    
    # 检测系统核心数并设置并发数
    if command -v nproc >/dev/null 2>&1; then
        concurrent_jobs=$(($(nproc) * 2))
    elif command -v sysctl >/dev/null 2>&1; then
        concurrent_jobs=$(($(sysctl -n hw.ncpu) * 2))
    else
        concurrent_jobs=4  # 默认值
    fi
    
    # 限制最大并发数，避免系统资源耗尽
    if [ $concurrent_jobs -gt 16 ]; then
        concurrent_jobs=16
    fi
    
    # 检查是否支持GNU parallel或xargs -P
    if command -v parallel >/dev/null 2>&1; then
        use_parallel="parallel"
        echo "  使用 GNU parallel，并发数: $concurrent_jobs"
    elif xargs --help 2>&1 | grep -q -- '-P' 2>/dev/null; then
        use_parallel="xargs"
        echo "  使用 xargs -P，并发数: $concurrent_jobs"
    else
        echo "  警告: 不支持并发处理，使用串行模式"
        concurrent_jobs=1
    fi

    # 创建临时结果文件
    local temp_result_file=$(mktemp)
    local temp_image_list=$(mktemp)
    
    # 生成图片文件列表
    scan_pod_images "$pod_dir" "all" > "$temp_image_list"
    
    # 导出必要的函数和变量给子进程使用
    export -f check_single_image_reference
    export -f is_animation_sequence_frame
    export pod_dir
    export temp_result_file
    
    # 开始并发处理
    local start_time=$(date +%s)

    if [ "$use_parallel" = "parallel" ]; then
        # 使用GNU parallel
        cat "$temp_image_list" | parallel -j"$concurrent_jobs" --line-buffer \
            check_single_image_reference {} "$pod_dir" "$temp_result_file"
    elif [ "$use_parallel" = "xargs" ]; then
        # 使用xargs -P
        cat "$temp_image_list" | xargs -P"$concurrent_jobs" -I{} \
            bash -c 'check_single_image_reference "$1" "$2" "$3"' _ {} "$pod_dir" "$temp_result_file"
    else
        # 串行处理（回退方案）
        while read file; do
            check_single_image_reference "$file" "$pod_dir" "$temp_result_file"
        done < "$temp_image_list"
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "  检测完成，耗时: ${duration}秒"
    
    # 处理结果
    local unused_count=0
    local animation_count=0
    local referenced_count=0
    
    while IFS=':' read -r type data; do
        case "$type" in
            "UNUSED")
                # 格式: UNUSED:filepath:size
                local filepath=$(echo "$data" | cut -d':' -f1)
                local size=$(echo "$data" | cut -d':' -f2)
                echo "$filepath:$size" >> "$unused_file"
                unused_count=$((unused_count + 1))
                local filename=$(basename "$filepath")
                echo "    未引用: $filename ($((size / 1024))KB)"
                ;;
            "ANIMATION")
                # 格式: ANIMATION:filename
                echo "    动画序列帧: $data (跳过检测)"
                animation_count=$((animation_count + 1))
                ;;
            "REFERENCED")
                # 格式: REFERENCED:filename:pattern:files
                local filename=$(echo "$data" | cut -d':' -f1)
                local pattern=$(echo "$data" | cut -d':' -f2)
                local files=$(echo "$data" | cut -d':' -f3-)
                echo "    引用发现: $filename -> 模式:'$pattern' 在文件: $files"
                referenced_count=$((referenced_count + 1))
                ;;
        esac
    done < "$temp_result_file"
    
    # 清理临时文件
    rm -f "$temp_result_file" "$temp_image_list"

    echo "  完成引用检测："
    echo "    - 无引用图片: $unused_count 个"
    echo "    - 有引用图片: $referenced_count 个" 
    echo "    - 动画序列帧: $animation_count 个"
    echo "    - 总计处理: $total_files 个"
    
    if [ -s "$unused_file" ]; then
        echo ""
        echo "发现无引用图片文件:"
        while IFS=':' read filepath size; do
            echo "  $(basename "$filepath") ($(($size / 1024))KB) - $filepath"
        done < "$unused_file"
        echo ""
        echo "无引用文件详情已保存到: $unused_file"
    else
        log_success "未发现无引用图片文件"
        rm -f "$unused_file"
    fi

    echo "$unused_file"
}

# Pod库优化功能 - 分为压缩和删除两种模式，支持增量优化
optimize_pod() {
    local pod_name="$1"
    local pod_dir="$2"
    local backup_dir="$3"
    local optimization_type="${4:-compress}"  # "compress" 或 "delete"

    log_info "开始优化 $pod_name (模式: $optimization_type)..."

    # 进入Pod目录
    cd "$pod_dir"

    local total_saved=0
    local processed_files=0

    if [ "$optimization_type" = "compress" ]; then
        # 压缩模式：优化图片质量
        compress_pod_images "$pod_name" "$backup_dir"
        total_saved=$?
    elif [ "$optimization_type" = "delete" ]; then
        # 删除模式：删除重复和无引用图片
        delete_unused_images "$pod_name" "$backup_dir"
        total_saved=$?
    elif [ "$optimization_type" = "heic" ]; then
        # HEIC模式：先转HEIC，再对剩余PNG/JPG执行压缩
        convert_pod_images_to_heic "$pod_name" "$backup_dir"
        # 使用全局变量拿到准确统计
        total_saved=$LAST_OPTIMIZATION_SAVED
        processed_files=$LAST_OPTIMIZATION_COUNT

        # 记录一次“heic”聚合
        if [ $total_saved -gt 0 ]; then
            record_optimization "$pod_name" "heic" "$processed_files" "$total_saved" "$backup_dir"
            log_success "$pod_name HEIC转换完成: 节省 $((total_saved / 1024))KB"
        else
            log_info "$pod_name 没有可转换为HEIC的文件"
        fi

        # 对剩余的PNG/JPG继续压缩（作为独立一次操作记录）
        compress_pod_images "$pod_name" "$backup_dir"
        local compress_saved=$?
        if [ $compress_saved -gt 0 ]; then
            record_optimization "$pod_name" "compress" "0" "$compress_saved" "$backup_dir"
            log_success "$pod_name 压缩完成: 额外节省 $((compress_saved / 1024))KB"
        fi

        # heic模式已自行记录聚合，提前返回
        cd - > /dev/null
        return 0
    else
        log_error "未知的优化类型: $optimization_type"
        cd - > /dev/null
        return 1
    fi

    # 返回原目录
    cd - > /dev/null

    # 记录优化结果
    if [ $total_saved -gt 0 ]; then
        record_optimization "$pod_name" "$optimization_type" "$processed_files" "$total_saved" "$backup_dir"
        log_success "$pod_name 优化完成: 节省 $((total_saved / 1024))KB"
    else
        log_info "$pod_name 没有新的文件需要优化"
    fi

    return 0
}

# 压缩图片文件
compress_pod_images() {
    local pod_name="$1"
    local backup_dir="$2"
    local total_saved=0

    # 统计可优化的文件 - 使用深度扫描
    local large_png=$(scan_pod_images "." "png" | xargs -I {} stat -f%z {} 2>/dev/null | awk '$1 > 51200' | wc -l)
    local medium_png=$(scan_pod_images "." "png" | xargs -I {} stat -f%z {} 2>/dev/null | awk '$1 > 20480 && $1 <= 51200' | wc -l)
    local small_png=$(scan_pod_images "." "png" | xargs -I {} stat -f%z {} 2>/dev/null | awk '$1 > 10240 && $1 <= 20480' | wc -l)
    local total_jpg=$(scan_pod_images "." "jpg" | wc -l)

    local optimizable=$((large_png + medium_png + small_png + total_jpg))

    if [ $optimizable -eq 0 ]; then
        log_warning "$pod_name 没有可压缩的图片文件"
        return 0
    fi

    echo "  发现可压缩文件: PNG $((large_png + medium_png + small_png)) 个, JPG $total_jpg 个"

    # 优化大型PNG文件 (>50KB) - 使用深度扫描和增量优化
    if [ $large_png -gt 0 ]; then
        echo "  处理大型PNG文件 ($large_png 个)..."
        scan_pod_images "." "png" | while read file; do
            if [ -f "$file" ]; then
                local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                if [ $file_size -gt 51200 ]; then  # >50KB
                    # 检查文件是否已经被压缩优化过
                    if is_file_optimized "$file" "compress"; then
                        echo "    跳过已优化: $(basename "$file")"
                        continue
                    fi

                    local original_size=$file_size

                    # 备份：保留原目录结构
                    backup_file_preserve_structure "$file" "$backup_dir"

                    # 压缩
                    if command -v pngquant >/dev/null 2>&1; then
                        pngquant --quality=65-80 --force --ext .png "$file" 2>/dev/null || true
                        local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                        local saved=$((original_size - new_size))
                        if [ $saved -gt 0 ]; then
                            echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                            total_saved=$((total_saved + saved))
                            # 记录文件优化信息
                            local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                            record_file_optimization "$file" "compress" "$original_size" "$new_size" "$compression_ratio"
                        fi
                    fi
                fi
            fi
        done
    fi

    # 优化中型PNG文件 (20KB-50KB)
    if [ $medium_png -gt 0 ]; then
        echo "  处理中型PNG文件 ($medium_png 个)..."
        scan_pod_images "." "png" | while read file; do
            if [ -f "$file" ]; then
                local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                if [ $file_size -gt 20480 ] && [ $file_size -le 51200 ]; then  # 20KB-50KB
                    # 检查文件是否已经被压缩优化过
                    if is_file_optimized "$file" "compress"; then
                        echo "    跳过已优化: $(basename "$file")"
                        continue
                    fi

                    local original_size=$file_size

                    # 备份：保留原目录结构
                    backup_file_preserve_structure "$file" "$backup_dir"

                    # 温和压缩
                    if command -v pngquant >/dev/null 2>&1; then
                        pngquant --quality=70-85 --force --ext .png "$file" 2>/dev/null || true
                        local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                        local saved=$((original_size - new_size))
                        if [ $saved -gt 0 ]; then
                            echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                            total_saved=$((total_saved + saved))
                            # 记录文件优化信息
                            local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                            record_file_optimization "$file" "compress" "$original_size" "$new_size" "$compression_ratio"
                        fi
                    fi
                fi
            fi
        done
    fi

    # 优化小型PNG文件 (10KB-20KB)
    if [ $small_png -gt 0 ]; then
        echo "  处理小型PNG文件 ($small_png 个)..."
        scan_pod_images "." "png" | while read file; do
            if [ -f "$file" ]; then
                local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                if [ $file_size -gt 10240 ] && [ $file_size -le 20480 ]; then  # 10KB-20KB
                    # 检查文件是否已经被压缩优化过
                    if is_file_optimized "$file" "compress"; then
                        echo "    跳过已优化: $(basename "$file")"
                        continue
                    fi

                    local original_size=$file_size

                    # 备份：保留原目录结构
                    backup_file_preserve_structure "$file" "$backup_dir"

                    # 轻度压缩
                    if command -v pngquant >/dev/null 2>&1; then
                        pngquant --quality=75-90 --force --ext .png "$file" 2>/dev/null || true
                        local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                        local saved=$((original_size - new_size))
                        if [ $saved -gt 0 ]; then
                            echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                            total_saved=$((total_saved + saved))
                            # 记录文件优化信息
                            local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                            record_file_optimization "$file" "compress" "$original_size" "$new_size" "$compression_ratio"
                        fi
                    fi
                fi
            fi
        done
    fi

    # 优化JPG文件 - 使用深度扫描和增量优化
    if [ $total_jpg -gt 0 ]; then
        echo "  处理JPG文件 ($total_jpg 个)..."
        scan_pod_images "." "jpg" | while read file; do
            if [ -f "$file" ]; then
                # 检查文件是否已经被压缩优化过
                if is_file_optimized "$file" "compress"; then
                    echo "    跳过已优化: $(basename "$file")"
                    continue
                fi

                local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")

                # 备份：保留原目录结构
                backup_file_preserve_structure "$file" "$backup_dir"

                # 优化
                if command -v jpegoptim >/dev/null 2>&1; then
                    jpegoptim --max=80 --strip-all "$file" 2>/dev/null || true
                    local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                    local saved=$((original_size - new_size))
                    if [ $saved -gt 0 ]; then
                        echo "    优化: $(basename "$file") 节省 $((saved / 1024))KB"
                        total_saved=$((total_saved + saved))
                        # 记录文件优化信息
                        local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                        record_file_optimization "$file" "compress" "$original_size" "$new_size" "$compression_ratio"
                    fi
                fi
            fi
        done
    fi

    return $total_saved
}

# 删除无引用和重复图片
delete_unused_images() {
    local pod_name="$1"
    local backup_dir="$2"
    local total_saved=0

    echo "  分析无引用图片..."

    # 仅查找无引用图片
    echo "  [DEBUG] 准备调用 find_unused_images('.')..."
    local unused_file
    unused_file=$(find_unused_images "." | tee /dev/stderr | tail -n1)
    echo "  [DEBUG] find_unused_images 返回: $unused_file"
    if [ -f "$unused_file" ]; then
        local candidate_count=$(wc -l < "$unused_file" 2>/dev/null || echo 0)
        echo "  [DEBUG] 未引用候选数量: $candidate_count"
    else
        echo "  [DEBUG] 未生成未引用候选列表文件"
    fi

    local delete_count=0

    # 处理无引用图片
    if [ -f "$unused_file" ] && [ -s "$unused_file" ]; then
        echo "  处理无引用图片..."
        
        # 先记录所有待删除文件到数据库
        echo "  记录删除进度到数据库..."
        while IFS=':' read filepath size; do
            if [ -f "$filepath" ]; then
                record_deletion_candidate "$pod_name" "$filepath"
            fi
        done < "$unused_file"
        
        # 执行删除操作
        while IFS=':' read filepath size; do
            if [ -f "$filepath" ]; then
                # 检查是否已经删除过
                if is_deletion_completed "$pod_name" "$filepath"; then
                    echo "    跳过已删除: $(basename "$filepath")"
                    continue
                fi
                
                # 备份：保留原目录结构
                backup_file_preserve_structure "$filepath" "$backup_dir"

                # 删除
                rm "$filepath"
                echo "    删除: $(basename "$filepath") 节省 $((size / 1024))KB"
                total_saved=$((total_saved + size))
                delete_count=$((delete_count + 1))
                
                # 标记为已删除
                mark_deletion_completed "$pod_name" "$filepath"
            fi
        done < "$unused_file"

        rm -f "$unused_file"
    fi

    # 清理临时文件

    if [ $delete_count -gt 0 ]; then
        echo "  删除了 $delete_count 个无引用图片文件"
    else
        echo "  没有发现可删除的图片文件"
    fi

    return $total_saved
}

# 全部优化所有Pod库 - 内部增量优化
batch_optimize_pods() {
    echo ""
    echo "选择优化类型:"
    echo "1. 压缩图片 (减少文件大小，保持文件)"
    echo "2. 删除图片 (仅删除无引用图片)"
    echo "3. 混合优化 (先删除无引用，再压缩)"
    echo "4. 转换为HEIC (先转HEIC，再压缩剩余PNG/JPG)"
    echo -n "请选择 [1/2/3/4]: "
    read -r opt_type

    local optimization_mode="compress"
    case "$opt_type" in
        1) optimization_mode="compress" ;;
        2) optimization_mode="delete" ;;
        3) optimization_mode="mixed" ;;
        4) optimization_mode="heic" ;;
        *)
            log_error "无效选择，使用默认压缩模式"
            optimization_mode="compress"
            ;;
    esac

    log_info "开始全部优化 (模式: $optimization_mode，内部增量优化)..."

    # 创建全局备份目录到统一的 backups 目录下
    mkdir -p "$BACKUP_ROOT"
    local global_backup="$BACKUP_ROOT/full_optimization_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$global_backup"
    echo "全局备份目录: $global_backup"
    echo ""

    local processed_count=0
    local total_pods=0

    # 统计总数
    total_pods=$(find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        if [[ "$name" != backup_* ]] && [[ "$name" != "BBJShellApp" ]] && [[ "$name" != "MeetYouApp" ]] && [[ "$name" != "YoubaobaoApp_ci2" ]] && [[ "$name" != "meetyou-ci-5" ]] && [[ "$name" != "iOS" ]] && [[ "$name" != "vibe_coding" ]] && [[ "$name" != "pod_clean" ]]; then
            echo "$name"
        fi
    done | wc -l)

    echo "发现 $total_pods 个Pod库，开始处理..."
    echo ""

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
            continue
        fi

        processed_count=$((processed_count + 1))
        echo "[$processed_count/$total_pods] 处理 $name..."

        # 创建Pod专用备份目录
        local pod_backup="$global_backup/${name}"
        mkdir -p "$pod_backup"

        # 优化Pod库 - 内部会自动跳过已优化的文件
        if [ "$optimization_mode" = "mixed" ]; then
            # 混合模式：先删除，再压缩
            optimize_pod "$name" "$dir" "$pod_backup" "delete"
            optimize_pod "$name" "$dir" "$pod_backup" "compress"
        elif [ "$optimization_mode" = "heic" ]; then
            optimize_pod "$name" "$dir" "$pod_backup" "heic"
        else
            optimize_pod "$name" "$dir" "$pod_backup" "$optimization_mode"
        fi

        echo ""
    done

    log_success "全部优化完成: 处理了 $total_pods 个Pod库"
    echo "全局备份目录: $global_backup"
}

# 选择性优化Pod库
selective_optimize_pods() {
    log_info "选择性Pod库优化..."
    echo ""

    # 选择优化类型
    echo "选择优化类型:"
    echo "1. 压缩图片 (减少文件大小，保持文件)"
    echo "2. 删除图片 (仅删除无引用图片)"
    echo "3. 混合优化 (先删除无引用，再压缩)"
    echo "4. 转换为HEIC (先转HEIC，再压缩剩余PNG/JPG)"
    echo -n "请选择 [1/2/3/4]: "
    read -r opt_type

    local optimization_mode="compress"
    case "$opt_type" in
        1) optimization_mode="compress" ;;
        2) optimization_mode="delete" ;;
        3) optimization_mode="mixed" ;;
        4) optimization_mode="heic" ;;
        *)
            log_error "无效选择，使用默认压缩模式"
            optimization_mode="compress"
            ;;
    esac

    # 列出所有可用的Pod库
    echo ""
    echo "可用的Pod库:"
    local pod_list=()
    local index=1

    # 使用临时文件避免子shell问题
    local temp_pods=$(mktemp)
    find .. -maxdepth 2 -name "*.podspec" > "$temp_pods"
    
    while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
            continue
        fi

        # 检查优化状态
        local status="未优化"
        if is_pod_optimized "$name"; then
            status="已优化"
        fi

        # 统计图片文件 - 使用深度扫描
        cd "$dir"
        local png_count=$(scan_pod_images "." "png" | wc -l)
        local jpg_count=$(scan_pod_images "." "jpg" | wc -l)
        local gif_count=$(scan_pod_images "." "gif" | wc -l)
        local total_images=$((png_count + jpg_count + gif_count))
        cd - > /dev/null

        echo "$index. $name (PNG: $png_count, JPG: $jpg_count, GIF: $gif_count, 总计: $total_images) [$status]"
        pod_list+=("$name:$dir")
        index=$((index + 1))
    done < "$temp_pods"
    
    rm -f "$temp_pods"

    if [ ${#pod_list[@]} -eq 0 ]; then
        log_warning "没有找到可优化的Pod库"
        return
    fi

    echo ""
    echo "请选择要优化的Pod库 (输入序号，多个用空格分隔，或输入 'all' 优化所有):"
    read -r selection

    # 创建备份目录到统一的 backups 目录下
    mkdir -p "$BACKUP_ROOT"
    local backup_dir="$BACKUP_ROOT/selective_optimization_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    if [ "$selection" = "all" ]; then
        log_info "优化所有Pod库 (模式: $optimization_mode)..."
        for pod_entry in "${pod_list[@]}"; do
            local pod_name="${pod_entry%%:*}"
            local pod_dir="${pod_entry##*:}"
            local pod_backup="$backup_dir/${pod_name}"
            mkdir -p "$pod_backup"

            if [ "$optimization_mode" = "mixed" ]; then
                # 混合模式：先删除，再压缩
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "delete"
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "compress"
            elif [ "$optimization_mode" = "heic" ]; then
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "heic"
            else
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "$optimization_mode"
            fi
            echo ""
        done
    else
        # 解析选择的序号
        for num in $selection; do
            if [[ "$num" =~ ^[0-9]+$ ]] && [ "$num" -ge 1 ] && [ "$num" -le ${#pod_list[@]} ]; then
                local selected_pod="${pod_list[$((num - 1))]}"
                local pod_name="${selected_pod%%:*}"
                local pod_dir="${selected_pod##*:}"
                local pod_backup="$backup_dir/${pod_name}"
                mkdir -p "$pod_backup"

                if [ "$optimization_mode" = "mixed" ]; then
                    # 混合模式：先删除，再压缩
                    optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "delete"
                    optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "compress"
                else
                    optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "$optimization_mode"
                fi
                echo ""
            else
                log_error "无效选择: $num"
            fi
        done
    fi

    log_success "选择性优化完成，备份目录: $backup_dir"
}

# 显示优化历史
show_optimization_history() {
    log_info "优化历史记录:"
    echo ""
    
    if [ ! -f "$DB_FILE" ]; then
        log_warning "暂无优化记录"
        return
    fi
    
    sqlite3 -header -column "$DB_FILE" << 'EOF'
SELECT 
    pod_name as "Pod库",
    optimization_type as "优化类型",
    file_count as "文件数",
    ROUND(space_saved/1024.0, 2) as "节省(KB)",
    datetime(optimization_time, 'localtime') as "优化时间"
FROM optimizations 
ORDER BY optimization_time DESC 
LIMIT 20;
EOF
}

# Git贮藏功能 - 采用与完整优化相同的目录处理逻辑
git_stash_optimization() {
    log_info "Git贮藏管理..."

    echo ""
    echo "贮藏选项:"
    echo "1. 贮藏所有Pod库的优化修改"
    echo "2. 查看所有Pod库的贮藏列表"
    echo "3. 恢复所有Pod库的最近贮藏"
    echo "4. 恢复指定Pod库的贮藏"
    echo "5. 删除指定Pod库的贮藏"
    echo "6. 查看所有Pod库的修改状态"
    echo "7. 返回主菜单"
    echo -n "请选择: "
    read -r choice

    case "$choice" in
        1)
            log_info "贮藏所有Pod库的优化修改..."
            local stash_message="Pod优化修改_$(date +%Y%m%d_%H%M%S)"
            local stashed_count=0

            # 遍历所有Pod目录 - 采用与完整优化相同的逻辑
            find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
                local dir=$(dirname "$podspec")
                local name=$(basename "$dir")

                # 跳过备份和应用目录
                if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                    continue
                fi

                # 进入Pod目录 - 与完整优化保持一致
                cd "$dir"

                # 检查是否有图片文件修改
                if ! git diff --quiet -- "*.png" "*.jpg" "*.jpeg" 2>/dev/null || ! git diff --cached --quiet -- "*.png" "*.jpg" "*.jpeg" 2>/dev/null; then
                    echo "  贮藏 $name 的修改..."
                    local pod_stash_message="${stash_message}_${name}"

                    # 贮藏图片文件修改
                    if git stash push -m "$pod_stash_message" -- "*.png" "*.jpg" "*.jpeg" 2>/dev/null || \
                       git stash save "$pod_stash_message" 2>/dev/null; then
                        echo "    ✅ 已贮藏: $name"
                        stashed_count=$((stashed_count + 1))
                    else
                        echo "    ❌ 贮藏失败: $name"
                    fi
                else
                    echo "  跳过 $name (无图片修改)"
                fi

                # 返回原目录 - 与完整优化保持一致
                cd - > /dev/null
            done

            if [ $stashed_count -gt 0 ]; then
                log_success "已贮藏 $stashed_count 个Pod库的优化修改"
            else
                log_warning "没有发现需要贮藏的图片修改"
            fi
            ;;
        2)
            log_info "查看所有Pod库的贮藏列表..."

            find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
                local dir=$(dirname "$podspec")
                local name=$(basename "$dir")

                # 跳过备份和应用目录
                if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                    continue
                fi

                # 进入Pod目录
                cd "$dir"

                local stash_list=$(git stash list | grep -E "(Pod优化|optimization)" 2>/dev/null)
                if [ -n "$stash_list" ]; then
                    echo "📦 $name:"
                    echo "$stash_list" | head -5 | sed 's/^/    /'
                    echo ""
                fi

                # 返回原目录
                cd - > /dev/null
            done
            ;;
        3)
            log_info "恢复所有Pod库的最近贮藏..."
            local restored_count=0

            find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
                local dir=$(dirname "$podspec")
                local name=$(basename "$dir")

                # 跳过备份和应用目录
                if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                    continue
                fi

                # 进入Pod目录
                cd "$dir"

                # 检查是否有贮藏
                if git stash list | grep -q -E "(Pod优化|optimization)" 2>/dev/null; then
                    echo "  恢复 $name 的最近贮藏..."
                    if git stash pop 2>/dev/null; then
                        echo "    ✅ 已恢复: $name"
                        restored_count=$((restored_count + 1))
                    else
                        echo "    ❌ 恢复失败: $name"
                    fi
                fi

                # 返回原目录
                cd - > /dev/null
            done

            if [ $restored_count -gt 0 ]; then
                log_success "已恢复 $restored_count 个Pod库的贮藏"
            else
                log_warning "没有找到可恢复的贮藏"
            fi
            ;;
        4)
            echo "请选择要操作的Pod库:"
            echo ""
            local pod_list=()
            local index=1

            find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
                local dir=$(dirname "$podspec")
                local name=$(basename "$dir")

                # 跳过备份和应用目录
                if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                    continue
                fi

                # 检查是否有贮藏
                cd "$dir"
                local stash_count=$(git stash list | grep -c -E "(Pod优化|optimization)" 2>/dev/null || echo "0")
                cd - > /dev/null

                if [ "$stash_count" -gt 0 ]; then
                    echo "$index. $name ($stash_count 个贮藏)"
                    pod_list+=("$name:$dir")
                    index=$((index + 1))
                fi
            done

            if [ ${#pod_list[@]} -eq 0 ]; then
                log_warning "没有找到有贮藏的Pod库"
                return
            fi

            echo -n "请输入序号: "
            read -r pod_choice

            if [[ "$pod_choice" =~ ^[0-9]+$ ]] && [ "$pod_choice" -ge 1 ] && [ "$pod_choice" -le ${#pod_list[@]} ]; then
                local selected_pod="${pod_list[$((pod_choice - 1))]}"
                local pod_name="${selected_pod%%:*}"
                local pod_dir="${selected_pod##*:}"

                cd "$pod_dir"
                echo "贮藏列表 ($pod_name):"
                git stash list | grep -E "(Pod优化|optimization)" | head -10
                echo -n "请输入贮藏编号 (如: stash@{0}): "
                read -r stash_ref

                if [ -n "$stash_ref" ]; then
                    if git stash apply "$stash_ref" 2>/dev/null; then
                        log_success "已恢复 $pod_name 的贮藏: $stash_ref"
                    else
                        log_error "恢复失败"
                    fi
                fi
                cd - > /dev/null
            else
                log_error "无效选择"
            fi
            ;;
        5)
            echo "请选择要操作的Pod库:"
            echo ""
            local pod_list=()
            local index=1

            find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
                local dir=$(dirname "$podspec")
                local name=$(basename "$dir")

                # 跳过备份和应用目录
                if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                    continue
                fi

                # 检查是否有贮藏
                cd "$dir"
                local stash_count=$(git stash list | grep -c -E "(Pod优化|optimization)" 2>/dev/null || echo "0")
                cd - > /dev/null

                if [ "$stash_count" -gt 0 ]; then
                    echo "$index. $name ($stash_count 个贮藏)"
                    pod_list+=("$name:$dir")
                    index=$((index + 1))
                fi
            done

            if [ ${#pod_list[@]} -eq 0 ]; then
                log_warning "没有找到有贮藏的Pod库"
                return
            fi

            echo -n "请输入序号: "
            read -r pod_choice

            if [[ "$pod_choice" =~ ^[0-9]+$ ]] && [ "$pod_choice" -ge 1 ] && [ "$pod_choice" -le ${#pod_list[@]} ]; then
                local selected_pod="${pod_list[$((pod_choice - 1))]}"
                local pod_name="${selected_pod%%:*}"
                local pod_dir="${selected_pod##*:}"

                cd "$pod_dir"
                echo "贮藏列表 ($pod_name):"
                git stash list | grep -E "(Pod优化|optimization)" | head -10
                echo -n "请输入要删除的贮藏编号 (如: stash@{0}): "
                read -r stash_ref

                if [ -n "$stash_ref" ]; then
                    echo -n "确认删除 $pod_name 的贮藏 $stash_ref? [y/N]: "
                    read -r confirm
                    if [[ "$confirm" =~ ^[Yy]$ ]]; then
                        if git stash drop "$stash_ref" 2>/dev/null; then
                            log_success "已删除 $pod_name 的贮藏: $stash_ref"
                        else
                            log_error "删除失败"
                        fi
                    fi
                fi
                cd - > /dev/null
            else
                log_error "无效选择"
            fi
            ;;
        6)
            log_info "查看所有Pod库的修改状态..."

            find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
                local dir=$(dirname "$podspec")
                local name=$(basename "$dir")

                # 跳过备份和应用目录
                if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
                    continue
                fi

                # 进入Pod目录
                cd "$dir"

                local status=$(git status --porcelain | grep -E '\.(png|jpg|jpeg)$' 2>/dev/null)
                if [ -n "$status" ]; then
                    echo "📦 $name:"
                    echo "$status" | head -10 | sed 's/^/    /'
                    echo ""
                fi

                # 返回原目录
                cd - > /dev/null
            done
            ;;
        7)
            return 0
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 增量优化
incremental_optimization() {
    log_info "开始增量优化..."
    
    # 发现所有Pod库
    local pods=()
    local new_pods=()
    
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        
        # 跳过排除的目录
        if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
            continue
        fi
        
        echo "$name"
    done > /tmp/all_pods.txt
    
    local total_pods=$(wc -l < /tmp/all_pods.txt)
    local new_count=0
    
    echo "发现 $total_pods 个Pod库，检查优化状态..."
    echo ""
    
    while read -r pod_name; do
        if ! is_pod_optimized "$pod_name"; then
            echo "  ✅ $pod_name (未优化)"
            new_count=$((new_count + 1))
        else
            echo "  ⏭️  $pod_name (已优化)"
        fi
    done < /tmp/all_pods.txt
    
    rm -f /tmp/all_pods.txt
    
    if [ $new_count -eq 0 ]; then
        log_success "所有Pod库均已优化，无需增量优化"
        return 0
    fi
    
    echo ""
    log_info "发现 $new_count 个未优化的Pod库"
    echo -n "是否执行增量优化? [y/N]: "
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        log_info "调用完整优化脚本进行增量处理..."
        "$SCRIPTS_DIR/complete_pod_optimizer.sh" --incremental
    fi
}

# 显示帮助信息
show_help() {
    echo "Pod库优化工具 v6.0"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -b, --batch         批量优化所有Pod库"
    echo "  -s, --selective     选择性优化Pod库"
    echo "  -t, --type <类型>   测试指定优化类型"
    echo "      支持类型: compress, delete, mixed, heic"
    echo "  --stash             Git贮藏管理"
    echo "  -H, --history       显示优化历史"
    echo "  --reset             重置优化记录"
    echo "  -q, --quality       生成质量对比报告"
    echo ""
    echo "示例:"
    echo "  $0 -t delete        测试删除无引用图片"
    echo "  $0 -t compress      测试压缩图片"
    echo "  $0 -t heic          测试HEIC转换"
    echo ""
    echo "交互模式:"
    echo "  $0                  进入交互式菜单"
}

# 交互式菜单
interactive_menu() {
    while true; do
        echo ""
        echo "=== Pod库优化工具菜单 ==="
        echo "1. 全部优化 (处理所有Pod库，内部增量优化)"
        echo "2. 选择性优化 (手动选择要优化的Pod库)"
        echo "3. Git贮藏管理"
        echo "4. 查看优化历史"
        echo "5. 生成质量对比报告"
        echo "6. 查看删除进度"
        echo "7. 清理删除记录"
        echo "8. 重置优化记录"
        echo "9. 退出"
        echo -n "请选择: "
        read -r choice

        case "$choice" in
            1)
                batch_optimize_pods
                ;;
            2)
                selective_optimize_pods
                ;;
            3)
                git_stash_optimization
                ;;
            4)
                show_optimization_history
                ;;
            5)
                log_info "启动质量对比报告生成器..."
                "$SCRIPTS_DIR/generate_quality_report.sh"
                ;;
            6)
                show_deletion_progress
                ;;
            7)
                clean_deletion_progress
                ;;
            8)
                reset_optimization_records
                ;;
            9)
                log_info "退出程序"
                break
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 重置优化记录
reset_optimization_records() {
    echo -n "确认重置所有优化记录? 这将允许重新优化所有Pod库 [y/N]: "
    read -r confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        if [ -f "$DB_FILE" ]; then
            sqlite3 "$DB_FILE" "DELETE FROM optimizations;"
            sqlite3 "$DB_FILE" "DELETE FROM optimization_files;"
            sqlite3 "$DB_FILE" "DELETE FROM deletion_progress;"
            log_success "优化记录已重置"
        else
            log_warning "没有找到优化记录数据库"
        fi
    else
        log_info "取消重置操作"
    fi
}

# 显示删除进度
show_deletion_progress() {
    log_info "删除进度记录:"
    echo ""
    
    if [ ! -f "$DB_FILE" ]; then
        log_warning "暂无删除进度记录"
        return
    fi
    
    sqlite3 -header -column "$DB_FILE" << 'EOF'
SELECT 
    pod_name as "Pod库",
    COUNT(*) as "待删除文件数",
    SUM(CASE WHEN status='completed' THEN 1 ELSE 0 END) as "已删除",
    SUM(CASE WHEN status='pending' THEN 1 ELSE 0 END) as "待处理"
FROM deletion_progress 
GROUP BY pod_name
ORDER BY pod_name;
EOF
}

# 清理已完成的删除记录
clean_deletion_progress() {
    echo -n "确认清理已完成的删除记录? [y/N]: "
    read -r confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        if [ -f "$DB_FILE" ]; then
            local deleted_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM deletion_progress WHERE status='completed';" 2>/dev/null || echo "0")
            sqlite3 "$DB_FILE" "DELETE FROM deletion_progress WHERE status='completed';"
            log_success "已清理 $deleted_count 条已完成的删除记录"
        else
            log_warning "没有找到删除进度数据库"
        fi
    else
        log_info "取消清理操作"
    fi
}

# 主函数
main() {
    show_banner
    
    # 初始化数据库
    init_database
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -b|--batch)
            batch_optimize_pods
            ;;
        -s|--selective)
            selective_optimize_pods
            ;;
        -t|--type)
            if [ -n "${2:-}" ]; then
                # 单独的优化类型测试
                local optimization_type="$2"
                case "$optimization_type" in
                    "compress"|"delete"|"mixed"|"heic")
                        echo "测试优化类型: $optimization_type"
                        local current_pod_name="$(basename "$PWD")"
                        local current_pod_dir="$PWD"
                        local test_backup_dir="/tmp/pod_optimizer_test_$(date +%Y%m%d_%H%M%S)"
                        mkdir -p "$test_backup_dir"
                        echo "备份目录: $test_backup_dir"
                        optimize_pod "$current_pod_name" "$current_pod_dir" "$test_backup_dir" "$optimization_type"
                        ;;
                    *)
                        log_error "无效的优化类型: $optimization_type"
                        echo "支持的类型: compress, delete, mixed, heic"
                        exit 1
                        ;;
                esac
            else
                log_error "-t 选项需要指定优化类型"
                echo "用法: $0 -t <compress|delete|mixed|heic>"
                exit 1
            fi
            ;;
        --stash)
            git_stash_optimization
            ;;

        -H|--history)
            show_optimization_history
            ;;
        --reset)
            reset_optimization_records
            ;;
        -q|--quality)
            "$SCRIPTS_DIR/generate_quality_report.sh"
            ;;
        "")
            interactive_menu
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
