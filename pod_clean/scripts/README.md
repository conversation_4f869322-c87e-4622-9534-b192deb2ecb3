# Pod库优化工具 - 脚本架构说明

## 架构概览

本项目采用模块化架构设计，将功能拆分为多个专门的功能模块，提供清晰的代码结构和易于维护的设计。

```
scripts/
├── lib/                      # 核心功能库
│   ├── common.sh            # 通用工具函数
│   ├── database.sh          # 数据库操作
│   ├── image_processor.sh   # 图片处理核心
│   ├── git_manager.sh       # Git操作管理
│   └── unused_detector.sh   # 无引用图片检测
├── modules/                  # 业务功能模块
│   ├── batch_optimizer.sh   # 批量优化
│   └── selective_optimizer.sh # 选择性优化
└── [现有独立脚本]            # 保持现有功能脚本
```

## 核心库说明 (lib/)

### common.sh - 通用工具函数库
- **功能**: 日志记录、颜色定义、文件备份、图片扫描等基础工具
- **主要函数**:
  - `log_*()`: 日志输出函数族
  - `show_banner()`: 显示程序横幅
  - `backup_file_preserve_structure()`: 保持目录结构的文件备份
  - `scan_pod_images()`: 深度扫描Pod目录中的图片文件
  - `has_alpha_channel()`: 检测PNG是否包含Alpha通道
  - `is_valid_pod_directory()`: 检测有效的Pod目录
  - `get_concurrent_jobs()`: 获取系统并发数
- **依赖**: 无

### database.sh - 数据库操作模块
- **功能**: 管理优化记录的SQLite数据库，支持文件级和Pod级记录
- **主要函数**:
  - `init_database()`: 初始化数据库表结构
  - `is_file_optimized()`: 检查文件是否已优化
  - `record_file_optimization()`: 记录文件级优化信息
  - `is_pod_optimized()`: 检查Pod是否已优化
  - `record_optimization()`: 记录Pod级优化信息
  - `show_optimization_history()`: 显示优化历史
  - `reset_optimization_records()`: 重置优化记录
- **依赖**: sqlite3, common.sh

### image_processor.sh - 图片处理核心模块
- **功能**: 图片压缩、HEIC转换、重复图片检测等核心处理逻辑
- **主要函数**:
  - `convert_pod_images_to_heic()`: 批量转换图片为HEIC格式
  - `compress_pod_images()`: 压缩PNG/JPG图片
  - `find_duplicate_images()`: 检测重复图片文件
  - `convert_one_to_heic()`: 单个图片HEIC转换
  - `update_contents_json_for_heic()`: 更新Asset Catalog配置
- **依赖**: pngquant, jpegoptim, ImageMagick/sips, common.sh, database.sh

### unused_detector.sh - 无引用图片检测模块
- **功能**: 检测Pod中无引用的图片文件，支持并发处理和动画序列帧检测
- **主要函数**:
  - `find_unused_images()`: 检测无引用图片
  - `check_single_image_reference()`: 单个图片引用检测（并发）
  - `is_animation_sequence_frame()`: 动画序列帧检测
  - `delete_unused_images()`: 删除无引用图片
- **依赖**: ripgrep/grep, common.sh, database.sh

### git_manager.sh - Git管理模块
- **功能**: Git贮藏管理，支持批量贮藏、恢复、查看等操作
- **主要函数**:
  - `git_stash_optimization()`: Git贮藏管理主入口
  - `stash_all_pods_modifications()`: 贮藏所有Pod修改
  - `list_all_pods_stashes()`: 查看所有贮藏
  - `restore_all_pods_recent_stash()`: 恢复最近贮藏
  - `show_all_pods_status()`: 查看修改状态
- **依赖**: git, common.sh

## 业务模块说明 (modules/)

### batch_optimizer.sh - 批量优化模块
- **功能**: 批量处理所有Pod库的优化任务，支持多种优化模式
- **主要函数**:
  - `optimize_pod()`: 单个Pod优化核心逻辑
  - `batch_optimize_pods()`: 批量优化所有Pod
  - `incremental_optimization()`: 增量优化功能
- **依赖**: common.sh, database.sh, image_processor.sh, unused_detector.sh

### selective_optimizer.sh - 选择性优化模块
- **功能**: 提供交互式Pod库选择和优化功能
- **主要函数**:
  - `selective_optimize_pods()`: 选择性优化主入口
- **依赖**: common.sh, database.sh, batch_optimizer.sh

## 现有独立脚本说明

### complete_pod_optimizer.sh
- **功能**: 完整Pod库优化工具，处理所有图片文件
- **特点**: 基于成功的basic_pod_list.sh逻辑，支持增量模式
- **状态**: 保持现有功能，可与新架构并存

### enhanced_pod_optimizer.sh  
- **功能**: 增强版Pod库批量优化工具
- **特点**: 提供更详细的统计信息
- **状态**: 建议逐步迁移到新的batch_optimizer.sh

### clean_unused_resources.sh
- **功能**: ZZIMYMain智能资源清理脚本
- **特点**: 智能检测和清理未使用的资源文件
- **状态**: 功能已整合到unused_detector.sh

### generate_quality_report.sh
- **功能**: Pod库图片优化质量对比报告生成脚本
- **特点**: 基于数据库记录生成详细的质量对比报告
- **状态**: 保持独立，与新架构完全兼容

### simple_pod_optimizer.sh
- **功能**: 简化版Pod库优化工具
- **特点**: 专注于核心功能，便于调试
- **状态**: 建议迁移到新架构

## 主要改进

### 1. 代码重用和模块化
- 统一了日志函数、颜色定义等通用代码
- 将功能按职责分离到不同模块
- 主脚本保持简洁，专注于调度功能

### 2. 更好的可维护性
- 每个模块有明确的职责和接口
- 模块间依赖关系清晰
- 便于单独测试和调试

### 3. 向后兼容
- 保持所有现有功能不变
- 现有脚本可继续使用
- 逐步迁移策略

### 4. 扩展性
- 新功能可以轻松添加为新模块
- 核心库可以被其他脚本复用
- 支持插件式架构

## 使用建议

### 新用户
建议直接使用 `pod_optimizer.sh`，它提供了完整的功能集和最佳的用户体验。

### 现有用户
可以继续使用熟悉的脚本，所有原有功能都得到保留。

### 开发者
新功能建议基于模块化架构开发，可以复用核心库的功能。
