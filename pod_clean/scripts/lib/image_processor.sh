#!/bin/bash

# Pod库优化工具 - 图片处理核心模块
# 功能: 图片压缩、HEIC转换、重复图片检测等核心图片处理逻辑
# 用法: source "$SCRIPT_DIR/lib/image_processor.sh"
# 依赖: pngquant, jpegoptim, ImageMagick/sips, common.sh, database.sh
# 版本: v1.0

# HEIC质量设置
export HEIC_QUALITY=${HEIC_QUALITY:-70}

# 全局记录最近一次优化（用于新类型heic准确统计）
export LAST_OPTIMIZATION_SAVED=0
export LAST_OPTIMIZATION_COUNT=0

# 在同目录的 Contents.json 中，将旧文件名更新为新文件名（Asset Catalog 兼容）
update_contents_json_for_heic() {
    local file_path="$1"         # 原始文件路径（png/jpg）
    local new_filename="$2"      # 新的 HEIC 文件名（仅文件名）

    local dir_path
    dir_path=$(dirname "$file_path")
    local json_path="$dir_path/Contents.json"

    if [ ! -f "$json_path" ]; then
        return 0
    fi

    local old_filename
    old_filename=$(basename "$file_path")

    # 备份 JSON
    backup_file_preserve_structure "$json_path" "$BACKUP_ROOT/contents_json_backups_$(date +%Y%m%d)"

    if command -v jq >/dev/null 2>&1; then
        local tmp
        tmp=$(mktemp)
        if jq --arg old "$old_filename" --arg new "$new_filename" '(.images[]? | select(.filename == $old) | .filename) = $new' "$json_path" > "$tmp" 2>/dev/null; then
            mv "$tmp" "$json_path"
            echo "    更新 Contents.json: $old_filename -> $new_filename"
            return 0
        else
            rm -f "$tmp" 2>/dev/null || true
        fi
    fi

    # jq 不可用时，使用 sed 简单替换（仅替换完全匹配的旧文件名）
    if command -v gsed >/dev/null 2>&1; then
        gsed -i "s/\"$old_filename\"/\"$new_filename\"/g" "$json_path" 2>/dev/null || true
        echo "    更新 Contents.json: $old_filename -> $new_filename"
    else
        # macOS bsd sed
        sed -i '' "s/\"$old_filename\"/\"$new_filename\"/g" "$json_path" 2>/dev/null || true
        echo "    更新 Contents.json: $old_filename -> $new_filename"
    fi
}

# 将单个图片转换为HEIC（优先使用 ImageMagick，其次 sips，最后 heif-enc）
convert_one_to_heic() {
    local src="$1"
    local dst="$2"
    local quality="$3"

    # 优先 ImageMagick
    if command -v magick >/dev/null 2>&1; then
        magick "$src" -quality "$quality" -define heic:speed=1 -strip "$dst" 2>/dev/null && return 0
    fi

    # macOS 备选：sips（质量参数在部分系统不生效，仍可用作后备）
    if command -v sips >/dev/null 2>&1; then
        sips -s format heic "$src" --out "$dst" >/dev/null 2>&1 && return 0
    fi

    # heif-enc 备选
    if command -v heif-enc >/dev/null 2>&1; then
        # heif-enc 质量范围通常 0-100，数值越大越好
        heif-enc -q "$quality" "$src" -o "$dst" >/dev/null 2>&1 && return 0
    fi

    return 1
}

# 将Pod中的图片尽可能转换为HEIC，跳过含Alpha的PNG，并记录数据库
convert_pod_images_to_heic() {
    local pod_name="$1"
    local backup_dir="$2"

    local total_saved=0
    local converted_count=0

    echo "  扫描可转换为HEIC的图片..."
    scan_pod_images "." "all" | while read file; do
        if [ -f "$file" ]; then
            local ext_lower=$(echo "${file##*.}" | tr 'A-Z' 'a-z')
            # 仅处理 jpg/jpeg；png需无Alpha
            if [[ "$ext_lower" == "jpg" || "$ext_lower" == "jpeg" ]]; then
                :
            elif [[ "$ext_lower" == "png" ]]; then
                if has_alpha_channel "$file"; then
                    echo "    跳过含Alpha PNG: $(basename "$file")"
                    continue
                fi
            else
                continue
            fi

            # 已经是HEIC的跳过
            if [[ "$ext_lower" == "heic" ]]; then
                continue
            fi

            # 如果该文件之前进行过heic转换记录，则跳过
            if is_file_optimized "$file" "heic"; then
                echo "    跳过已转换: $(basename "$file")"
                continue
            fi

            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            local heic_path="${file%.*}.heic"

            # 执行转换
            if convert_one_to_heic "$file" "$heic_path" "$HEIC_QUALITY"; then
                if [ -f "$heic_path" ]; then
                    local new_size=$(stat -f%z "$heic_path" 2>/dev/null || stat -c%s "$heic_path")
                    if [ "$new_size" -gt 0 ] && [ "$new_size" -lt "$original_size" ]; then
                        # 备份原文件
                        backup_file_preserve_structure "$file" "$backup_dir"
                        # 用HEIC替换原图（删除原图，仅保留.heic）
                        rm -f "$file"
                        # 若存在 Contents.json，则同步更新文件名
                        update_contents_json_for_heic "$file" "$(basename "$heic_path")"
                        echo "    转换: $(basename "$heic_path") 节省 $(((original_size - new_size)/1024))KB"
                        total_saved=$((total_saved + (original_size - new_size)))
                        converted_count=$((converted_count + 1))
                        # 记录文件级优化
                        local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                        record_file_optimization "$heic_path" "heic" "$original_size" "$new_size" "$compression_ratio"
                    else
                        # 未节省空间或失败，删除生成的heic
                        rm -f "$heic_path" 2>/dev/null || true
                    fi
                fi
            else
                echo "    转换失败: $(basename "$file")"
            fi
        fi
    done

    LAST_OPTIMIZATION_SAVED=$((total_saved))
    LAST_OPTIMIZATION_COUNT=$((converted_count))

    # 返回码不可靠，保持与历史实现一致
    return $total_saved
}

# 压缩图片文件
compress_pod_images() {
    local pod_name="$1"
    local backup_dir="$2"
    local total_saved=0

    # 统计可优化的文件 - 使用深度扫描
    local large_png=$(scan_pod_images "." "png" | xargs -I {} stat -f%z {} 2>/dev/null | awk '$1 > 51200' | wc -l)
    local medium_png=$(scan_pod_images "." "png" | xargs -I {} stat -f%z {} 2>/dev/null | awk '$1 > 20480 && $1 <= 51200' | wc -l)
    local small_png=$(scan_pod_images "." "png" | xargs -I {} stat -f%z {} 2>/dev/null | awk '$1 > 10240 && $1 <= 20480' | wc -l)
    local total_jpg=$(scan_pod_images "." "jpg" | wc -l)

    local optimizable=$((large_png + medium_png + small_png + total_jpg))

    if [ $optimizable -eq 0 ]; then
        log_warning "$pod_name 没有可压缩的图片文件"
        return 0
    fi

    echo "  发现可压缩文件: PNG $((large_png + medium_png + small_png)) 个, JPG $total_jpg 个"

    # 压缩PNG文件的通用函数
    compress_png_files() {
        local size_range="$1"
        local quality_range="$2"
        local min_size="$3"
        local max_size="$4"
        
        scan_pod_images "." "png" | while read file; do
            if [ -f "$file" ]; then
                local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                
                # 检查文件大小范围
                if [ -n "$min_size" ] && [ $file_size -le $min_size ]; then
                    continue
                fi
                if [ -n "$max_size" ] && [ $file_size -gt $max_size ]; then
                    continue
                fi
                
                # 检查文件是否已经被压缩优化过
                if is_file_optimized "$file" "compress"; then
                    echo "    跳过已优化: $(basename "$file")"
                    continue
                fi

                local original_size=$file_size

                # 备份：保留原目录结构
                backup_file_preserve_structure "$file" "$backup_dir"

                # 压缩
                if command -v pngquant >/dev/null 2>&1; then
                    pngquant --quality="$quality_range" --force --ext .png "$file" 2>/dev/null || true
                    local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                    local saved=$((original_size - new_size))
                    if [ $saved -gt 0 ]; then
                        echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                        total_saved=$((total_saved + saved))
                        # 记录文件优化信息
                        local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                        record_file_optimization "$file" "compress" "$original_size" "$new_size" "$compression_ratio"
                    fi
                fi
            fi
        done
    }

    # 优化大型PNG文件 (>50KB)
    if [ $large_png -gt 0 ]; then
        echo "  处理大型PNG文件 ($large_png 个)..."
        compress_png_files "大型" "65-80" 51200 ""
    fi

    # 优化中型PNG文件 (20KB-50KB)
    if [ $medium_png -gt 0 ]; then
        echo "  处理中型PNG文件 ($medium_png 个)..."
        compress_png_files "中型" "70-85" 20480 51200
    fi

    # 优化小型PNG文件 (10KB-20KB)
    if [ $small_png -gt 0 ]; then
        echo "  处理小型PNG文件 ($small_png 个)..."
        compress_png_files "小型" "75-90" 10240 20480
    fi

    # 优化JPG文件
    if [ $total_jpg -gt 0 ]; then
        echo "  处理JPG文件 ($total_jpg 个)..."
        scan_pod_images "." "jpg" | while read file; do
            if [ -f "$file" ]; then
                # 检查文件是否已经被压缩优化过
                if is_file_optimized "$file" "compress"; then
                    echo "    跳过已优化: $(basename "$file")"
                    continue
                fi

                local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")

                # 备份：保留原目录结构
                backup_file_preserve_structure "$file" "$backup_dir"

                # 优化
                if command -v jpegoptim >/dev/null 2>&1; then
                    jpegoptim --max=80 --strip-all "$file" 2>/dev/null || true
                    local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                    local saved=$((original_size - new_size))
                    if [ $saved -gt 0 ]; then
                        echo "    优化: $(basename "$file") 节省 $((saved / 1024))KB"
                        total_saved=$((total_saved + saved))
                        # 记录文件优化信息
                        local compression_ratio=$(echo "scale=2; $new_size * 100 / $original_size" | bc -l 2>/dev/null || echo "0")
                        record_file_optimization "$file" "compress" "$original_size" "$new_size" "$compression_ratio"
                    fi
                fi
            fi
        done
    fi

    return $total_saved
}

# 检测重复图片文件
find_duplicate_images() {
    local pod_dir="$1"
    local temp_file=$(mktemp)
    local duplicates_file=$(mktemp)

    log_info "检测重复图片文件..."

    # 计算所有图片文件的MD5
    scan_pod_images "$pod_dir" "all" | while read file; do
        if [ -f "$file" ]; then
            local md5=$(md5 -q "$file" 2>/dev/null || md5sum "$file" | cut -d' ' -f1)
            local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "$md5:$size:$file"
        fi
    done | sort > "$temp_file"

    # 查找重复的MD5
    cut -d':' -f1 "$temp_file" | uniq -d | while read md5; do
        echo "=== 重复文件组 (MD5: $md5) ===" >> "$duplicates_file"
        grep "^$md5:" "$temp_file" | while IFS=':' read hash size filepath; do
            echo "  $filepath ($(($size / 1024))KB)" >> "$duplicates_file"
        done
        echo "" >> "$duplicates_file"
    done

    if [ -s "$duplicates_file" ]; then
        echo "发现重复图片文件:"
        cat "$duplicates_file"
        echo ""
        echo "重复文件详情已保存到: $duplicates_file"
    else
        log_success "未发现重复图片文件"
        rm -f "$duplicates_file"
    fi

    rm -f "$temp_file"
    echo "$duplicates_file"
}
