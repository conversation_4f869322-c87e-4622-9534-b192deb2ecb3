#!/bin/bash

# Pod库优化工具 - 通用工具函数库
# 功能: 提供日志记录、颜色定义、文件备份等通用工具函数
# 用法: source "$SCRIPT_DIR/lib/common.sh"
# 依赖: 无
# 版本: v1.0

# 颜色定义
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export PURPLE='\033[0;35m'
export CYAN='\033[0;36m'
export NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 显示主横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Pod库优化工具 v6.0                         ║"
    echo "║                                                              ║"
    echo "║  功能: 增量优化、选择性优化、Git贮藏、HEIC转换                   ║"
    echo "║  特性: SQLite记录、避免重复优化、自动报告生成                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 备份工具函数：在备份目录中保留原始相对路径结构
backup_file_preserve_structure() {
    local src_file="$1"
    local backup_root="$2"

    # 移除前导的 ./ 或 /
    local rel_path="${src_file#./}"
    rel_path="${rel_path#/}"

    local dest_path="$backup_root/$rel_path"
    mkdir -p "$(dirname "$dest_path")"
    cp "$src_file" "$dest_path"
}

# 深度扫描Pod目录中的所有图片文件 - 确保覆盖所有子目录
scan_pod_images() {
    local pod_dir="$1"
    local scan_type="$2"  # "all", "png", "jpg", "gif"

    case "$scan_type" in
        "all")
            find "$pod_dir" -type f \( -iname "*.png" -o -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.gif" -o -iname "*.webp" -o -iname "*.bmp" \) 2>/dev/null
            ;;
        "png")
            find "$pod_dir" -type f -iname "*.png" 2>/dev/null
            ;;
        "jpg")
            find "$pod_dir" -type f \( -iname "*.jpg" -o -iname "*.jpeg" \) 2>/dev/null
            ;;
        "gif")
            find "$pod_dir" -type f -iname "*.gif" 2>/dev/null
            ;;
        *)
            find "$pod_dir" -type f \( -iname "*.png" -o -iname "*.jpg" -o -iname "*.jpeg" \) 2>/dev/null
            ;;
    esac
}

# 判断图片是否包含Alpha通道（PNG用），JPG默认无Alpha
has_alpha_channel() {
    local file="$1"
    # macOS: sips 可用；通用：ImageMagick identify
    if command -v sips >/dev/null 2>&1; then
        local alpha=$(sips -g hasAlpha "$file" 2>/dev/null | awk '/hasAlpha/ {print $2}')
        [[ "$alpha" == "yes" ]]
        return $?
    elif command -v magick >/dev/null 2>&1; then
        local channels=$(magick identify -format "%[channels]" "$file" 2>/dev/null || echo "")
        [[ "$channels" == *"alpha"* ]]
        return $?
    fi
    # 无法判断时，保守认为有Alpha以避免破坏透明度
    return 0
}

# 检测常见的Pod目录，跳过备份和应用目录
is_valid_pod_directory() {
    local name="$1"
    if [[ "$name" == backup_* ]] || [[ "$name" == "BBJShellApp" ]] || [[ "$name" == "MeetYouApp" ]] || [[ "$name" == "YoubaobaoApp_ci2" ]] || [[ "$name" == "meetyou-ci-5" ]] || [[ "$name" == "iOS" ]] || [[ "$name" == "vibe_coding" ]] || [[ "$name" == "pod_clean" ]]; then
        return 1
    fi
    return 0
}

# 获取系统并发数
get_concurrent_jobs() {
    local concurrent_jobs=1
    
    # 检测系统核心数并设置并发数
    if command -v nproc >/dev/null 2>&1; then
        concurrent_jobs=$(($(nproc) * 2))
    elif command -v sysctl >/dev/null 2>&1; then
        concurrent_jobs=$(($(sysctl -n hw.ncpu) * 2))
    else
        concurrent_jobs=4  # 默认值
    fi
    
    # 限制最大并发数，避免系统资源耗尽
    if [ $concurrent_jobs -gt 16 ]; then
        concurrent_jobs=16
    fi
    
    echo $concurrent_jobs
}

# 检查并发处理工具的可用性
check_parallel_support() {
    if command -v parallel >/dev/null 2>&1; then
        echo "parallel"
    elif xargs --help 2>&1 | grep -q -- '-P' 2>/dev/null; then
        echo "xargs"
    else
        echo "none"
    fi
}
