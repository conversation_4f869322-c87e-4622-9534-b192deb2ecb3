#!/bin/bash

# Pod库优化工具 - Git管理模块
# 功能: Git贮藏管理，支持批量贮藏、恢复、查看等操作
# 用法: source "$SCRIPT_DIR/lib/git_manager.sh"
# 依赖: git, common.sh
# 版本: v1.0

# Git贮藏功能 - 采用与完整优化相同的目录处理逻辑
git_stash_optimization() {
    log_info "Git贮藏管理..."

    echo ""
    echo "贮藏选项:"
    echo "1. 贮藏所有Pod库的优化修改"
    echo "2. 查看所有Pod库的贮藏列表"
    echo "3. 恢复所有Pod库的最近贮藏"
    echo "4. 恢复指定Pod库的贮藏"
    echo "5. 删除指定Pod库的贮藏"
    echo "6. 查看所有Pod库的修改状态"
    echo "7. 返回主菜单"
    echo -n "请选择: "
    read -r choice

    case "$choice" in
        1)
            stash_all_pods_modifications
            ;;
        2)
            list_all_pods_stashes
            ;;
        3)
            restore_all_pods_recent_stash
            ;;
        4)
            restore_specific_pod_stash
            ;;
        5)
            delete_specific_pod_stash
            ;;
        6)
            show_all_pods_status
            ;;
        7)
            return 0
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 贮藏所有Pod库的优化修改
stash_all_pods_modifications() {
    log_info "贮藏所有Pod库的优化修改..."
    local stash_message="Pod优化修改_$(date +%Y%m%d_%H%M%S)"
    local stashed_count=0

    # 遍历所有Pod目录 - 采用与完整优化相同的逻辑
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 进入Pod目录 - 与完整优化保持一致
        cd "$dir"

        # 检查是否有图片文件修改
        if ! git diff --quiet -- "*.png" "*.jpg" "*.jpeg" 2>/dev/null || ! git diff --cached --quiet -- "*.png" "*.jpg" "*.jpeg" 2>/dev/null; then
            echo "  贮藏 $name 的修改..."
            local pod_stash_message="${stash_message}_${name}"

            # 贮藏图片文件修改
            if git stash push -m "$pod_stash_message" -- "*.png" "*.jpg" "*.jpeg" 2>/dev/null || \
               git stash save "$pod_stash_message" 2>/dev/null; then
                echo "    ✅ 已贮藏: $name"
                stashed_count=$((stashed_count + 1))
            else
                echo "    ❌ 贮藏失败: $name"
            fi
        else
            echo "  跳过 $name (无图片修改)"
        fi

        # 返回原目录 - 与完整优化保持一致
        cd - > /dev/null
    done

    if [ $stashed_count -gt 0 ]; then
        log_success "已贮藏 $stashed_count 个Pod库的优化修改"
    else
        log_warning "没有发现需要贮藏的图片修改"
    fi
}

# 查看所有Pod库的贮藏列表
list_all_pods_stashes() {
    log_info "查看所有Pod库的贮藏列表..."

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 进入Pod目录
        cd "$dir"

        local stash_list=$(git stash list | grep -E "(Pod优化|optimization)" 2>/dev/null)
        if [ -n "$stash_list" ]; then
            echo "📦 $name:"
            echo "$stash_list" | head -5 | sed 's/^/    /'
            echo ""
        fi

        # 返回原目录
        cd - > /dev/null
    done
}

# 恢复所有Pod库的最近贮藏
restore_all_pods_recent_stash() {
    log_info "恢复所有Pod库的最近贮藏..."
    local restored_count=0

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 进入Pod目录
        cd "$dir"

        # 检查是否有贮藏
        if git stash list | grep -q -E "(Pod优化|optimization)" 2>/dev/null; then
            echo "  恢复 $name 的最近贮藏..."
            if git stash pop 2>/dev/null; then
                echo "    ✅ 已恢复: $name"
                restored_count=$((restored_count + 1))
            else
                echo "    ❌ 恢复失败: $name"
            fi
        fi

        # 返回原目录
        cd - > /dev/null
    done

    if [ $restored_count -gt 0 ]; then
        log_success "已恢复 $restored_count 个Pod库的贮藏"
    else
        log_warning "没有找到可恢复的贮藏"
    fi
}

# 恢复指定Pod库的贮藏
restore_specific_pod_stash() {
    echo "请选择要操作的Pod库:"
    echo ""
    local pod_list=()
    local index=1

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 检查是否有贮藏
        cd "$dir"
        local stash_count=$(git stash list | grep -c -E "(Pod优化|optimization)" 2>/dev/null || echo "0")
        cd - > /dev/null

        if [ "$stash_count" -gt 0 ]; then
            echo "$index. $name ($stash_count 个贮藏)"
            pod_list+=("$name:$dir")
            index=$((index + 1))
        fi
    done

    if [ ${#pod_list[@]} -eq 0 ]; then
        log_warning "没有找到有贮藏的Pod库"
        return
    fi

    echo -n "请输入序号: "
    read -r pod_choice

    if [[ "$pod_choice" =~ ^[0-9]+$ ]] && [ "$pod_choice" -ge 1 ] && [ "$pod_choice" -le ${#pod_list[@]} ]; then
        local selected_pod="${pod_list[$((pod_choice - 1))]}"
        local pod_name="${selected_pod%%:*}"
        local pod_dir="${selected_pod##*:}"

        cd "$pod_dir"
        echo "贮藏列表 ($pod_name):"
        git stash list | grep -E "(Pod优化|optimization)" | head -10
        echo -n "请输入贮藏编号 (如: stash@{0}): "
        read -r stash_ref

        if [ -n "$stash_ref" ]; then
            if git stash apply "$stash_ref" 2>/dev/null; then
                log_success "已恢复 $pod_name 的贮藏: $stash_ref"
            else
                log_error "恢复失败"
            fi
        fi
        cd - > /dev/null
    else
        log_error "无效选择"
    fi
}

# 删除指定Pod库的贮藏
delete_specific_pod_stash() {
    echo "请选择要操作的Pod库:"
    echo ""
    local pod_list=()
    local index=1

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 检查是否有贮藏
        cd "$dir"
        local stash_count=$(git stash list | grep -c -E "(Pod优化|optimization)" 2>/dev/null || echo "0")
        cd - > /dev/null

        if [ "$stash_count" -gt 0 ]; then
            echo "$index. $name ($stash_count 个贮藏)"
            pod_list+=("$name:$dir")
            index=$((index + 1))
        fi
    done

    if [ ${#pod_list[@]} -eq 0 ]; then
        log_warning "没有找到有贮藏的Pod库"
        return
    fi

    echo -n "请输入序号: "
    read -r pod_choice

    if [[ "$pod_choice" =~ ^[0-9]+$ ]] && [ "$pod_choice" -ge 1 ] && [ "$pod_choice" -le ${#pod_list[@]} ]; then
        local selected_pod="${pod_list[$((pod_choice - 1))]}"
        local pod_name="${selected_pod%%:*}"
        local pod_dir="${selected_pod##*:}"

        cd "$pod_dir"
        echo "贮藏列表 ($pod_name):"
        git stash list | grep -E "(Pod优化|optimization)" | head -10
        echo -n "请输入要删除的贮藏编号 (如: stash@{0}): "
        read -r stash_ref

        if [ -n "$stash_ref" ]; then
            echo -n "确认删除 $pod_name 的贮藏 $stash_ref? [y/N]: "
            read -r confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                if git stash drop "$stash_ref" 2>/dev/null; then
                    log_success "已删除 $pod_name 的贮藏: $stash_ref"
                else
                    log_error "删除失败"
                fi
            fi
        fi
        cd - > /dev/null
    else
        log_error "无效选择"
    fi
}

# 查看所有Pod库的修改状态
show_all_pods_status() {
    log_info "查看所有Pod库的修改状态..."

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 进入Pod目录
        cd "$dir"

        local status=$(git status --porcelain | grep -E '\.(png|jpg|jpeg)$' 2>/dev/null)
        if [ -n "$status" ]; then
            echo "📦 $name:"
            echo "$status" | head -10 | sed 's/^/    /'
            echo ""
        fi

        # 返回原目录
        cd - > /dev/null
    done
}
