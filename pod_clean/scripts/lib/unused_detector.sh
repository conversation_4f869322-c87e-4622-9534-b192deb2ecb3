#!/bin/bash

# Pod库优化工具 - 无引用图片检测模块
# 功能: 检测Pod中无引用的图片文件，支持并发处理和动画序列帧检测
# 用法: source "$SCRIPT_DIR/lib/unused_detector.sh"
# 依赖: ripgrep/grep, common.sh, database.sh
# 版本: v1.0

# 现代化进度条显示（类似 yarn install）
show_modern_progress() {
    local current="$1"
    local total="$2" 
    local message="${3:-扫描图片}"
    
    # Spinner 字符（Unicode Braille patterns）
    local spinner_chars="⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    local spinner_index=$(( ($(date +%s%N) / 100000000) % 10 ))
    local spinner_char="${spinner_chars:$spinner_index:1}"
    
    if [ "$total" -gt 0 ]; then
        local percent=$(( current * 100 / total ))
        local bar_width=30
        local filled=$(( percent * bar_width / 100 ))
        local empty=$(( bar_width - filled ))
        
        # 构建进度条
        local progress_bar=""
        for i in $(seq 1 $filled); do 
            progress_bar+="█"
        done
        for i in $(seq 1 $empty); do 
            progress_bar+="░"
        done
        
        printf "\r  %s %s [%s] %d%% (%d/%d)" \
            "$spinner_char" "$message" "$progress_bar" "$percent" "$current" "$total"
    else
        printf "\r  %s %s..." "$spinner_char" "$message"
    fi
}

# 动态进度条（用于不确定进度的任务）
show_dynamic_progress() {
    local message="${1:-处理中}"
    local i=0
    local spinner_chars="⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    local length=${#spinner_chars}
    
    while true; do
        local pos=$((i % length))
        local spinner_char="${spinner_chars:$pos:1}"
        printf "\r  %s %s..." "$spinner_char" "$message"
        sleep 0.1
        i=$((i + 1))
    done
}

# 向后兼容的旧函数名
show_progress() {
    show_dynamic_progress "$@"
}

# 检测是否为动画序列帧
is_animation_sequence_frame() {
    local file_path="$1"
    local pod_dir="$2"
    local filename=$(basename "$file_path")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    
    # 检测常见的动画序列帧命名模式
    # 如: ani_good1, ani_good2, ..., ani_good12
    # 或: frame_01, frame_02, ..., frame_30
    # 或: loading_1, loading_2, ..., loading_8
    local base_pattern=""
    local sequence_number=""
    
    # 提取基础名称和数字
    if [[ "$basename_no_ext" =~ ^(.+[_-])([0-9]+)(@[0-9]x)?$ ]]; then
        base_pattern="${BASH_REMATCH[1]}"
        sequence_number="${BASH_REMATCH[2]}"
        local resolution_suffix="${BASH_REMATCH[3]}"
        
        # 在代码中搜索动画相关的用法模式
        # 1. 搜索循环加载序列帧的代码模式
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "(for|NSInteger|Int).*[0-9]+.*${base_pattern}|animationImages.*${base_pattern}|${base_pattern}.*images" \
           "$pod_dir" >/dev/null 2>&1; then
            return 0
        fi
        
        # 2. 搜索字符串格式化的模式 stringWithFormat:@"${base_pattern}%
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "stringWithFormat.*${base_pattern}.*%|String.*format.*${base_pattern}" \
           "$pod_dir" >/dev/null 2>&1; then
            return 0
        fi
        
        # 3. 检查是否有其他序列帧文件存在（至少3个连续编号）
        local sequence_count=0
        # 强制转换为十进制，避免八进制解析错误
        local sequence_num=$((10#$sequence_number))
        local start_num=$((sequence_num - 2))
        local end_num=$((sequence_num + 2))
        
        for i in $(seq $start_num $end_num); do
            if [ $i -le 0 ]; then continue; fi
            local test_name="${base_pattern}${i}${resolution_suffix}"
            local test_files=$(find "$pod_dir" -name "${test_name}.*" -type f 2>/dev/null)
            if [ -n "$test_files" ]; then
                sequence_count=$((sequence_count + 1))
            fi
        done
        
        # 如果找到2个或以上连续编号的文件，认为是动画序列帧
        if [ $sequence_count -ge 2 ]; then
            return 0
        fi
    fi
    
    return 1
}

# 单个图片引用检测（用于并发处理）
check_single_image_reference() {
    local file="$1"
    local pod_dir="$2"
    local result_file="$3"
    
    if [ ! -f "$file" ]; then
        return 0
    fi
    
    local filename=$(basename "$file")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    local file_ext=$(echo "$filename" | awk -F'.' '{print tolower($NF)}')

    # 移除常见的分辨率和状态后缀
    local clean_name=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//' | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 检查是否为动画序列帧
    if is_animation_sequence_frame "$file" "$pod_dir"; then
        echo "ANIMATION:$filename" >> "$result_file"
        return 0
    fi

    # 构建搜索模式
    local search_patterns=(
        "$filename"
        "$basename_no_ext"
        "$clean_name"
        "${clean_name}_"
    )
    
    # 对于常见的iOS图片引用模式，去掉分辨率后缀后的基础名称
    local base_name_for_ios=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//')
    if [ "$base_name_for_ios" != "$basename_no_ext" ]; then
        search_patterns+=("$base_name_for_ios")
    fi

    # 使用ripgrep（如果可用）或grep进行搜索
    local search_cmd="grep"
    local search_args="-r -l"
    
    if command -v rg >/dev/null 2>&1; then
        search_cmd="rg"
        search_args="--files-with-matches"
    fi
    
    # 构建文件类型过滤参数
    local file_includes=""
    if [ "$search_cmd" = "grep" ]; then
        file_includes="--include=*.m --include=*.h --include=*.swift --include=*.mm --include=*.xib --include=*.storyboard --include=*.plist --include=*.json --include=*.txt --include=*.strings --include=*.xml --include=*.css --include=*.js --include=*.ts --include=*.jsx --include=*.tsx --include=*.vue --include=*.dart --include=*.kt --include=*.java --include=*.cs --include=*.cpp --include=*.c --include=*.cc --include=*.rb --include=*.py --include=*.php --include=*.go --include=*.rs --include=*.scala --include=*.clj --include=*.sh --include=*.bat --include=*.ps1 --include=*.md --include=*.yml --include=*.yaml --include=*.toml --include=*.ini --include=*.cfg --include=*.conf --exclude=Contents.json"
    else
        # ripgrep文件类型参数，确保包含plist文件
        file_includes="-t c -t cpp -t js -t ts -t py -t java -t swift -t objc -t json -t xml -t yaml -t toml"
    fi
    
    local found=false
    for pattern in "${search_patterns[@]}"; do
        # 调试输出：记录搜索过程
        if [[ "$filename" == "all_spreadkuang.png" ]]; then
            echo "[DEBUG] 搜索 $filename，模式: $pattern，目录: $pod_dir" >&2
        fi

        if [ "$search_cmd" = "rg" ]; then
            if rg $search_args $file_includes --glob '*.plist' --glob '!Contents.json' "$pattern" "$pod_dir" >/dev/null 2>&1; then
                found=true
                local reference_files=$(rg $search_args $file_includes --glob '*.plist' --glob '!Contents.json' "$pattern" "$pod_dir" 2>/dev/null | head -3)
                echo "REFERENCED:$filename:$pattern:$(echo "$reference_files" | tr '\n' ' ')" >> "$result_file"
                if [[ "$filename" == "all_spreadkuang.png" ]]; then
                    echo "[DEBUG] $filename 找到引用，模式: $pattern" >&2
                fi
                break
            fi
        else
            if $search_cmd $search_args $file_includes "$pattern" "$pod_dir" >/dev/null 2>&1; then
                found=true
                local reference_files=$($search_cmd $search_args $file_includes "$pattern" "$pod_dir" 2>/dev/null | head -3)
                echo "REFERENCED:$filename:$pattern:$(echo "$reference_files" | tr '\n' ' ')" >> "$result_file"
                if [[ "$filename" == "all_spreadkuang.png" ]]; then
                    echo "[DEBUG] $filename 找到引用，模式: $pattern，文件: $reference_files" >&2
                fi
                break
            fi
        fi

        # 调试输出：记录未找到的情况
        if [[ "$filename" == "all_spreadkuang.png" ]]; then
            echo "[DEBUG] 模式 $pattern 未找到引用" >&2
        fi
    done

    # 特殊处理：检查是否为 Asset Catalog 中的图片
    if [ "$found" = false ]; then
        local is_asset_catalog_image=false
        local file_dir=$(dirname "$file")
        
        # 检查是否在 .imageset 目录中
        if [[ "$file_dir" == *.imageset ]]; then
            # 提取 imageset 名称（去掉 .imageset 后缀）
            local imageset_name=$(basename "$file_dir" .imageset)
            
            # 在代码中搜索 Asset Catalog 引用
            if [ "$search_cmd" = "rg" ]; then
                if rg --files-with-matches -t c -t cpp -t swift -t objc --glob '!Contents.json' "$imageset_name" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local asset_reference_files=$(rg --files-with-matches -t c -t cpp -t swift -t objc --glob '!Contents.json' "$imageset_name" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Asset Catalog:$imageset_name:$(echo "$asset_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            else
                if grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --exclude="Contents.json" "$imageset_name" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local asset_reference_files=$(grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --exclude="Contents.json" "$imageset_name" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Asset Catalog:$imageset_name:$(echo "$asset_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            fi
        fi
        
        if [ "$is_asset_catalog_image" = false ]; then
            local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "UNUSED:$file:$size" >> "$result_file"
        fi
    fi
}

# 检测无引用图片文件
find_unused_images() {
    local pod_dir="$1"
    local unused_file=$(mktemp)

    log_info "检测无引用图片文件..."

    # 获取总文件数用于进度显示
    local total_files=$(scan_pod_images "$pod_dir" "all" | wc -l | tr -d ' ')
    echo "  将扫描 $total_files 个图片文件以检测引用..."

    # 检查是否支持并发处理
    local concurrent_jobs=$(get_concurrent_jobs)
    local use_parallel=$(check_parallel_support)
    
    echo "  使用 $use_parallel，并发数: $concurrent_jobs"

    # 创建临时结果文件
    local temp_result_file=$(mktemp)
    local temp_image_list=$(mktemp)
    
    # 生成图片文件列表
    scan_pod_images "$pod_dir" "all" > "$temp_image_list"
    
    # 导出必要的函数和变量给子进程使用
    export -f check_single_image_reference
    export -f is_animation_sequence_frame
    export -f show_modern_progress
    export -f show_dynamic_progress
    export pod_dir
    export temp_result_file
    
    # 开始并发处理
    local start_time=$(date +%s)
    local progress_pid=""
    
    # 启动进度条显示
    show_dynamic_progress "扫描图片" &
    progress_pid=$!
    
    if [ "$use_parallel" = "parallel" ]; then
        # 使用GNU parallel
        cat "$temp_image_list" | parallel -j"$concurrent_jobs" --line-buffer \
            check_single_image_reference {} "$pod_dir" "$temp_result_file"
    elif [ "$use_parallel" = "xargs" ]; then
        # 使用xargs -P
        cat "$temp_image_list" | xargs -P"$concurrent_jobs" -I{} \
            bash -c 'check_single_image_reference "$1" "$2" "$3"' _ {} "$pod_dir" "$temp_result_file"
    else
        # 串行处理（回退方案）
        local current=0
        while read file; do
            check_single_image_reference "$file" "$pod_dir" "$temp_result_file"
            current=$((current + 1))
            # 每处理5个文件更新一次进度
            if [ $((current % 5)) -eq 0 ] || [ $current -eq $total_files ]; then
                show_modern_progress "$current" "$total_files" "扫描图片"
            fi
        done < "$temp_image_list"
        echo ""  # 换行确保输出完整
    fi
    
    # 停止进度条
    if [ -n "$progress_pid" ]; then
        kill $progress_pid 2>/dev/null || true
        wait $progress_pid 2>/dev/null || true
    fi
    
    # 清除进度条显示
    printf "\r%*s\r" "80" ""
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "  检测完成，耗时: ${duration}秒"
    
    # 处理结果
    local unused_count=0
    local animation_count=0
    local referenced_count=0
    
    while IFS=':' read -r type data; do
        case "$type" in
            "UNUSED")
                # 格式: UNUSED:filepath:size
                local filepath=$(echo "$data" | cut -d':' -f1)
                local size=$(echo "$data" | cut -d':' -f2)
                echo "$filepath:$size" >> "$unused_file"
                unused_count=$((unused_count + 1))
                local filename=$(basename "$filepath")
                echo "    未引用: $filename ($((size / 1024))KB)"
                ;;
            "ANIMATION")
                # 格式: ANIMATION:filename
                echo "    动画序列帧: $data (跳过检测)"
                animation_count=$((animation_count + 1))
                ;;
            "REFERENCED")
                # 格式: REFERENCED:filename:pattern:files
                local filename=$(echo "$data" | cut -d':' -f1)
                local pattern=$(echo "$data" | cut -d':' -f2)
                local files=$(echo "$data" | cut -d':' -f3-)
                echo "    引用发现: $filename -> 模式:'$pattern' 在文件: $files"
                referenced_count=$((referenced_count + 1))
                ;;
        esac
    done < "$temp_result_file"
    
    # 清理临时文件
    rm -f "$temp_result_file" "$temp_image_list"

    echo "  完成引用检测："
    echo "    - 无引用图片: $unused_count 个"
    echo "    - 有引用图片: $referenced_count 个" 
    echo "    - 动画序列帧: $animation_count 个"
    echo "    - 总计处理: $total_files 个"
    
    if [ -s "$unused_file" ]; then
        echo ""
        echo "发现无引用图片文件:"
        while IFS=':' read filepath size; do
            echo "  $(basename "$filepath") ($(($size / 1024))KB) - $filepath"
        done < "$unused_file"
        echo ""

        # 将详情保存到 logs 目录
        local logs_dir="logs"
        mkdir -p "$logs_dir"
        local log_file="$logs_dir/unused_files_$(date +%Y%m%d_%H%M%S).txt"
        cp "$unused_file" "$log_file"
        echo "无引用文件详情已保存到: $log_file"
    else
        log_success "未发现无引用图片文件"
        rm -f "$unused_file"
    fi

    echo "$unused_file"
}

# 计算目录大小（递归计算所有文件）
calculate_directory_size() {
    local dir_path="$1"
    if [ ! -d "$dir_path" ]; then
        echo "0"
        return
    fi

    local total_size=0
    while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
            total_size=$((total_size + file_size))
        fi
    done < <(find "$dir_path" -type f -print0 2>/dev/null)

    echo "$total_size"
}

# 删除无引用和重复图片
delete_unused_images() {
    local pod_name="$1"
    local backup_dir="$2"
    local total_saved=0

    echo "  分析无引用图片..."

    # 仅查找无引用图片
    echo "  [DEBUG] 准备调用 find_unused_images('.')..."
    local unused_file
    unused_file=$(find_unused_images "." | tee /dev/stderr | tail -n1)
    echo "  [DEBUG] find_unused_images 返回: $unused_file"
    if [ -f "$unused_file" ]; then
        local candidate_count=$(wc -l < "$unused_file" 2>/dev/null || echo 0)
        echo "  [DEBUG] 未引用候选数量: $candidate_count"
    else
        echo "  [DEBUG] 未生成未引用候选列表文件"
    fi

    local delete_count=0
    # 用于跟踪已删除的 .imageset 目录，避免重复删除
    local deleted_imagesets=()

    # 处理无引用图片
    if [ -f "$unused_file" ] && [ -s "$unused_file" ]; then
        echo "  处理无引用图片..."

        # 先记录所有待删除文件到数据库
        echo "  记录删除进度到数据库..."
        while IFS=':' read filepath size; do
            if [ -f "$filepath" ]; then
                record_deletion_candidate "$pod_name" "$filepath"
            fi
        done < "$unused_file"

        # 执行删除操作
        while IFS=':' read filepath size; do
            if [ -f "$filepath" ]; then
                # 检查是否已经删除过
                if is_deletion_completed "$pod_name" "$filepath"; then
                    echo "    跳过已删除: $(basename "$filepath")"
                    continue
                fi

                local file_dir=$(dirname "$filepath")
                local filename=$(basename "$filepath")

                # 检查是否在 .imageset 目录中
                if [[ "$file_dir" == *.imageset ]]; then
                    # 检查这个 .imageset 目录是否已经被删除
                    local imageset_already_deleted=false
                    for deleted_dir in "${deleted_imagesets[@]}"; do
                        if [ "$deleted_dir" = "$file_dir" ]; then
                            imageset_already_deleted=true
                            break
                        fi
                    done

                    if [ "$imageset_already_deleted" = true ]; then
                        echo "    跳过已删除的imageset目录: $(basename "$file_dir")"
                        # 仍然标记为已删除
                        mark_deletion_completed "$pod_name" "$filepath"
                        continue
                    fi

                    # 计算整个 .imageset 目录的大小
                    local imageset_size=$(calculate_directory_size "$file_dir")
                    local imageset_name=$(basename "$file_dir")

                    # 备份整个 .imageset 目录
                    echo "    备份imageset目录: $imageset_name"
                    backup_file_preserve_structure "$file_dir" "$backup_dir"

                    # 删除整个 .imageset 目录
                    rm -rf "$file_dir"
                    echo "    删除: $imageset_name 目录 节省 $((imageset_size / 1024))KB"
                    total_saved=$((total_saved + imageset_size))
                    delete_count=$((delete_count + 1))

                    # 记录已删除的 .imageset 目录
                    deleted_imagesets+=("$file_dir")

                    # 标记为已删除（这里标记的是原始文件路径）
                    mark_deletion_completed "$pod_name" "$filepath"

                    # 同时标记该 .imageset 目录中的其他文件为已删除
                    # 这样后续遇到同一目录中的其他文件时就会跳过
                    while IFS=':' read other_filepath other_size; do
                        local other_file_dir=$(dirname "$other_filepath")
                        if [ "$other_file_dir" = "$file_dir" ] && [ "$other_filepath" != "$filepath" ]; then
                            mark_deletion_completed "$pod_name" "$other_filepath"
                        fi
                    done < "$unused_file"

                else
                    # 普通文件删除逻辑（非 .imageset 目录中的文件）
                    # 备份：保留原目录结构
                    backup_file_preserve_structure "$filepath" "$backup_dir"

                    # 删除
                    rm "$filepath"
                    echo "    删除: $filename 节省 $((size / 1024))KB"
                    total_saved=$((total_saved + size))
                    delete_count=$((delete_count + 1))

                    # 标记为已删除
                    mark_deletion_completed "$pod_name" "$filepath"
                fi
            fi
        done < "$unused_file"

        rm -f "$unused_file"
    fi

    if [ $delete_count -gt 0 ]; then
        echo "  删除了 $delete_count 个无引用图片文件/目录"
    else
        echo "  没有发现可删除的图片文件"
    fi

    return $total_saved
}
