#!/bin/bash

# Pod库优化工具 - 同步管理模块
# 功能: 提交优化↑（导出CSV并推送到远端）、同步优化↓（拉取CSV并合并到本地SQLite）
# 特性: 多用户协作支持，时间戳分离，智能冲突解决
# 用法: source "$SCRIPT_DIR/lib/sync_manager.sh"
# 依赖: sqlite3, git, database.sh, common.sh
# 版本: v2.0

# 获取用户标识
get_user_id() {
    # 优先使用 git config，其次环境变量，最后默认值
    local user_id
    user_id=$(git config user.name 2>/dev/null || echo "${USER:-unknown}")
    echo "$user_id"
}

# 确保同步目录存在（用于存放可版本化的CSV）
ensure_sync_dir() {
    # 如果 SCRIPT_DIR 未设置，使用当前脚本所在目录的父目录
    if [ -z "$SCRIPT_DIR" ]; then
        export SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
    fi
    if [ -z "$DB_DIR" ]; then
        export DB_DIR="$SCRIPT_DIR/db"
    fi
    if [ -z "$DB_FILE" ]; then
        export DB_FILE="$DB_DIR/optimization.db"
    fi
    mkdir -p "$DB_DIR/sync"
    mkdir -p "$DB_DIR/sync/processed"
}

# 导出数据库为CSV（使用时间戳和用户名避免冲突）
export_db_to_csv() {
    ensure_sync_dir
    local sync_dir="$DB_DIR/sync"
    local user_id=$(get_user_id)
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local sync_file="sync_${timestamp}_${user_id}.csv"
    local sync_path="$sync_dir/$sync_file"

    if [ ! -f "$DB_FILE" ]; then
        log_warning "本地数据库不存在，跳过导出"
        return 0
    fi

    # 导出所有表到单个CSV文件，添加表标识和用户信息
    sqlite3 "$DB_FILE" << EOF
.mode csv
.output "$sync_path"
-- 导出 optimizations 表
SELECT 'optimizations', pod_name, optimization_type, CAST(file_count AS TEXT), CAST(space_saved AS TEXT), optimization_time, backup_path, status, '$user_id', '$timestamp' FROM optimizations
UNION ALL
-- 导出 optimization_files 表
SELECT 'optimization_files', file_path, optimization_type, CAST(original_size AS TEXT), CAST(optimized_size AS TEXT), CAST(compression_ratio AS TEXT), optimization_time, IFNULL(user_id,'$user_id'), '$timestamp', '' FROM optimization_files
UNION ALL
-- 导出 deletion_progress 表
SELECT 'deletion_progress', pod_name, file_path, status, deletion_time, '', '', IFNULL(user_id,'$user_id'), '$timestamp', '' FROM deletion_progress;
.output stdout
EOF

    log_success "已导出 CSV 到: $sync_path"
    echo "$sync_file"
}

# 处理单个同步文件
process_sync_file() {
    local sync_file="$1"
    local sync_path="$2"

    if [ ! -f "$sync_path" ]; then
        return 1
    fi

    # 检查是否已处理过
    local processed_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM sync_log WHERE sync_file='$sync_file';" 2>/dev/null || echo "0")
    if [ "$processed_count" -gt 0 ]; then
        echo "  跳过已处理文件: $sync_file"
        return 0
    fi

    echo "  处理同步文件: $sync_file"

    # 解析文件名获取用户和时间信息
    local file_user=$(echo "$sync_file" | sed 's/sync_[0-9_]*_\(.*\)\.csv/\1/')

    # 导入数据，使用智能冲突解决
    sqlite3 "$DB_FILE" << EOF
PRAGMA foreign_keys=OFF;
BEGIN;

-- 创建临时表
DROP TABLE IF EXISTS _tmp_sync_data;
CREATE TEMP TABLE _tmp_sync_data (
    table_name TEXT,
    col1 TEXT, col2 TEXT, col3 TEXT, col4 TEXT, col5 TEXT, col6 TEXT, col7 TEXT, col8 TEXT, col9 TEXT
);

.mode csv
.import "$sync_path" _tmp_sync_data

-- 处理 optimization_files 表（最新优先策略）
INSERT OR REPLACE INTO optimization_files (file_path, optimization_type, original_size, optimized_size, compression_ratio, optimization_time, user_id, last_modified)
SELECT col2, col3, CAST(col4 AS INTEGER), CAST(col5 AS INTEGER), CAST(col6 AS REAL), col7, col8, CURRENT_TIMESTAMP
FROM _tmp_sync_data
WHERE table_name = 'optimization_files';

-- 处理 deletion_progress 表（最新优先策略）
INSERT OR REPLACE INTO deletion_progress (pod_name, file_path, status, deletion_time, user_id, last_modified)
SELECT col2, col3, col4, col5, col8, CURRENT_TIMESTAMP
FROM _tmp_sync_data
WHERE table_name = 'deletion_progress';

-- 处理 optimizations 表（避免重复插入）
INSERT INTO optimizations (pod_name, optimization_type, file_count, space_saved, optimization_time, backup_path, status)
SELECT col2, col3, CAST(col4 AS INTEGER), CAST(col5 AS INTEGER), col6, col7, col8
FROM _tmp_sync_data t
WHERE table_name = 'optimizations'
  AND NOT EXISTS (
    SELECT 1 FROM optimizations o
    WHERE o.pod_name = t.col2
      AND o.optimization_type = t.col3
      AND o.optimization_time = t.col6
      AND IFNULL(o.backup_path,'') = IFNULL(t.col7,'')
      AND IFNULL(o.status,'') = IFNULL(t.col8,'')
  );

-- 记录处理日志
INSERT INTO sync_log (sync_file, user_id, status) VALUES ('$sync_file', '$file_user', 'processed');

COMMIT;
EOF

    # 移动到已处理目录
    mv "$sync_path" "$DB_DIR/sync/processed/"
    return 0
}

# 从所有CSV文件合并到本地数据库
import_csv_to_db() {
    ensure_sync_dir
    local sync_dir="$DB_DIR/sync"

    if [ ! -f "$DB_FILE" ]; then
        log_info "检测到本地无数据库文件，先初始化..."
        init_database
    fi

    # 查找所有未处理的同步文件
    local sync_files=$(find "$sync_dir" -maxdepth 1 -name "sync_*.csv" -type f | sort)
    local processed_count=0

    if [ -z "$sync_files" ]; then
        log_info "没有找到待处理的同步文件"
        return 0
    fi

    echo "发现待处理的同步文件:"
    for sync_path in $sync_files; do
        local sync_file=$(basename "$sync_path")
        if process_sync_file "$sync_file" "$sync_path"; then
            processed_count=$((processed_count + 1))
        fi
    done

    if [ $processed_count -gt 0 ]; then
        log_success "已处理 $processed_count 个同步文件"
    else
        log_info "没有新的同步文件需要处理"
    fi
}

# 清理旧的同步文件（保留最近30天）
cleanup_old_sync_files() {
    local sync_dir="$DB_DIR/sync/processed"
    local days_to_keep=30

    if [ -d "$sync_dir" ]; then
        # 删除30天前的文件
        find "$sync_dir" -name "sync_*.csv" -type f -mtime +$days_to_keep -delete 2>/dev/null || true

        # 清理对应的日志记录
        local cutoff_date=$(date -d "$days_to_keep days ago" +%Y-%m-%d 2>/dev/null || date -v-${days_to_keep}d +%Y-%m-%d)
        sqlite3 "$DB_FILE" "DELETE FROM sync_log WHERE sync_time < '$cutoff_date';" 2>/dev/null || true
    fi
}

# 提交优化↑：导出CSV并推送到远端
submit_optimization_updates() {
    local sync_file=$(export_db_to_csv)

    if [ -z "$sync_file" ]; then
        log_warning "导出失败，跳过提交"
        return 1
    fi

    # 切换到仓库根目录进行 Git 操作
    local repo_root
    repo_root=$(git rev-parse --show-toplevel 2>/dev/null || echo "$SCRIPT_DIR/..")
    local user_id=$(get_user_id)

    (
      cd "$repo_root" || exit 1
      git add "pod_clean/db/sync/$sync_file" 2>/dev/null || true
      if git diff --cached --quiet; then
          log_warning "没有需要提交的CSV变更"
      else
          local msg="feat(pod_clean): 提交优化数据同步 by $user_id - $(date +%Y-%m-%dT%H:%M:%S)"
          git commit -m "$msg"
          local current_branch
          current_branch=$(git rev-parse --abbrev-ref HEAD)
          git push origin "$current_branch"
          log_success "已推送同步文件到远端: $sync_file"
      fi
    )

    # 清理旧文件
    cleanup_old_sync_files
}

# 同步优化↓：拉取远端CSV并合并到本地DB
sync_optimization_updates() {
    # 拉取远端
    local repo_root
    repo_root=$(git rev-parse --show-toplevel 2>/dev/null || echo "$SCRIPT_DIR/..")
    (
      cd "$repo_root" || exit 1
      local current_branch
      current_branch=$(git rev-parse --abbrev-ref HEAD)
      git pull --rebase origin "$current_branch" || true
    )

    # 合并到本地数据库
    import_csv_to_db
}

