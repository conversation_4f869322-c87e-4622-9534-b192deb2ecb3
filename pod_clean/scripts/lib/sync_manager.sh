#!/bin/bash

# Pod库优化工具 - 同步管理模块
# 功能: 提交优化↑（导出CSV并推送到远端）、同步优化↓（拉取CSV并合并到本地SQLite）
# 用法: source "$SCRIPT_DIR/lib/sync_manager.sh"
# 依赖: sqlite3, git, database.sh, common.sh
# 版本: v1.0

# 确保同步目录存在（用于存放可版本化的CSV）
ensure_sync_dir() {
    # 如果 SCRIPT_DIR 未设置，使用当前脚本所在目录的父目录
    if [ -z "$SCRIPT_DIR" ]; then
        export SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
    fi
    if [ -z "$DB_DIR" ]; then
        export DB_DIR="$SCRIPT_DIR/db"
    fi
    if [ -z "$DB_FILE" ]; then
        export DB_FILE="$DB_DIR/optimization.db"
    fi
    mkdir -p "$DB_DIR/sync"
}

# 导出数据库为CSV（不包含自增id列，便于跨环境导入）
export_db_to_csv() {
    ensure_sync_dir
    local sync_dir="$DB_DIR/sync"

    if [ ! -f "$DB_FILE" ]; then
        log_warning "本地数据库不存在，跳过导出"
        return 0
    fi

    # 导出表：optimizations（不含id）
    sqlite3 "$DB_FILE" << EOF
.mode csv
.headers off
.once "$sync_dir/optimizations.csv"
SELECT pod_name, optimization_type, file_count, space_saved, optimization_time, backup_path, status FROM optimizations;
.once "$sync_dir/optimization_files.csv"
SELECT file_path, optimization_type, original_size, optimized_size, compression_ratio, optimization_time FROM optimization_files;
.once "$sync_dir/deletion_progress.csv"
SELECT pod_name, file_path, status, deletion_time FROM deletion_progress;
EOF

    log_success "已导出 CSV 到: $sync_dir"
}

# 从CSV合并到本地数据库
import_csv_to_db() {
    ensure_sync_dir
    local sync_dir="$DB_DIR/sync"

    if [ ! -f "$DB_FILE" ]; then
        log_info "检测到本地无数据库文件，先初始化..."
        init_database
    fi

    # 仅当存在对应CSV时才导入
    sqlite3 "$DB_FILE" << EOF
PRAGMA foreign_keys=OFF;
BEGIN;
.mode csv

-- 临时表（不含id）
DROP TABLE IF EXISTS _tmp_optimizations;
CREATE TEMP TABLE _tmp_optimizations (
    pod_name TEXT,
    optimization_type TEXT,
    file_count INTEGER,
    space_saved INTEGER,
    optimization_time TEXT,
    backup_path TEXT,
    status TEXT
);

DROP TABLE IF EXISTS _tmp_optimization_files;
CREATE TEMP TABLE _tmp_optimization_files (
    file_path TEXT,
    optimization_type TEXT,
    original_size INTEGER,
    optimized_size INTEGER,
    compression_ratio REAL,
    optimization_time TEXT
);

DROP TABLE IF EXISTS _tmp_deletion_progress;
CREATE TEMP TABLE _tmp_deletion_progress (
    pod_name TEXT,
    file_path TEXT,
    status TEXT,
    deletion_time TEXT
);

-- 条件导入（文件存在才导入）
.bail off
.shell test -f "$sync_dir/optimizations.csv" && echo "> 导入 optimizations.csv" || true
.import "$sync_dir/optimizations.csv" _tmp_optimizations

.shell test -f "$sync_dir/optimization_files.csv" && echo "> 导入 optimization_files.csv" || true
.import "$sync_dir/optimization_files.csv" _tmp_optimization_files

.shell test -f "$sync_dir/deletion_progress.csv" && echo "> 导入 deletion_progress.csv" || true
.import "$sync_dir/deletion_progress.csv" _tmp_deletion_progress

-- 合并策略：
-- 1) optimization_files：file_path 唯一，使用 INSERT OR REPLACE
INSERT OR REPLACE INTO optimization_files (file_path, optimization_type, original_size, optimized_size, compression_ratio, optimization_time)
SELECT file_path, optimization_type, original_size, optimized_size, compression_ratio, optimization_time
FROM _tmp_optimization_files;

-- 2) deletion_progress：UNIQUE(pod_name,file_path)，使用 INSERT OR REPLACE
INSERT OR REPLACE INTO deletion_progress (pod_name, file_path, status, deletion_time)
SELECT pod_name, file_path, status, deletion_time
FROM _tmp_deletion_progress;

-- 3) optimizations：避免重复，按关键字段去重插入
INSERT INTO optimizations (pod_name, optimization_type, file_count, space_saved, optimization_time, backup_path, status)
SELECT t.pod_name, t.optimization_type, t.file_count, t.space_saved, t.optimization_time, t.backup_path, t.status
FROM _tmp_optimizations t
WHERE NOT EXISTS (
    SELECT 1 FROM optimizations o
    WHERE o.pod_name = t.pod_name
      AND o.optimization_type = t.optimization_type
      AND o.optimization_time = t.optimization_time
      AND IFNULL(o.backup_path,'') = IFNULL(t.backup_path,'')
      AND IFNULL(o.status,'') = IFNULL(t.status,'')
      AND IFNULL(o.file_count,0) = IFNULL(t.file_count,0)
      AND IFNULL(o.space_saved,0) = IFNULL(t.space_saved,0)
);

COMMIT;
EOF

    log_success "CSV 同步至本地数据库完成"
}

# 提交优化↑：导出CSV并推送到远端
submit_optimization_updates() {
    export_db_to_csv

    # 切换到仓库根目录进行 Git 操作
    local repo_root
    repo_root=$(git rev-parse --show-toplevel 2>/dev/null || echo "$SCRIPT_DIR/..")
    ( 
      cd "$repo_root" || exit 1
      git add pod_clean/db/sync/*.csv 2>/dev/null || true
      if git diff --cached --quiet; then
          log_warning "没有需要提交的CSV变更"
      else
          local msg="chore(pod_clean/db): 提交优化数据同步 $(date +%Y-%m-%dT%H:%M:%S)"
          git commit -m "$msg"
          local current_branch
          current_branch=$(git rev-parse --abbrev-ref HEAD)
          git push origin "$current_branch"
          log_success "已推送 CSV 到远端: origin/$current_branch"
      fi
    )
}

# 同步优化↓：拉取远端CSV并合并到本地DB
sync_optimization_updates() {
    # 拉取远端
    local repo_root
    repo_root=$(git rev-parse --show-toplevel 2>/dev/null || echo "$SCRIPT_DIR/..")
    (
      cd "$repo_root" || exit 1
      local current_branch
      current_branch=$(git rev-parse --abbrev-ref HEAD)
      git pull --rebase origin "$current_branch" || true
    )

    # 合并到本地数据库
    import_csv_to_db
}

