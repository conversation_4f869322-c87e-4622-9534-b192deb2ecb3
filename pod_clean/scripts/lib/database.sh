#!/bin/bash

# Pod库优化工具 - 数据库操作模块
# 功能: 管理优化记录的SQLite数据库操作，包括文件级和Pod级优化记录
# 用法: source "$SCRIPT_DIR/lib/database.sh"
# 依赖: sqlite3, common.sh
# 版本: v1.0

# 数据库配置
export DB_DIR="${SCRIPT_DIR}/db"
export DB_FILE="$DB_DIR/optimization.db"

# 数据库迁移：为现有表添加新字段
migrate_database() {
    if [ -f "$DB_FILE" ]; then
        # 检查是否需要迁移（检查 user_id 字段是否存在）
        local has_user_id=$(sqlite3 "$DB_FILE" "PRAGMA table_info(optimization_files);" 2>/dev/null | grep -c "user_id" 2>/dev/null || echo "0")

        if [ "$has_user_id" -eq 0 ]; then
            log_info "检测到旧版数据库，执行迁移..."
            sqlite3 "$DB_FILE" << 'EOF'
-- 为 optimization_files 表添加新字段
ALTER TABLE optimization_files ADD COLUMN user_id TEXT DEFAULT '';
ALTER TABLE optimization_files ADD COLUMN last_modified DATETIME DEFAULT CURRENT_TIMESTAMP;

-- 为 deletion_progress 表添加新字段
ALTER TABLE deletion_progress ADD COLUMN user_id TEXT DEFAULT '';
ALTER TABLE deletion_progress ADD COLUMN last_modified DATETIME DEFAULT CURRENT_TIMESTAMP;

-- 创建同步日志表（如果不存在）
CREATE TABLE IF NOT EXISTS sync_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sync_file TEXT NOT NULL,
    sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT NOT NULL,
    status TEXT DEFAULT 'processed'
);

-- 创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_sync_file ON sync_log(sync_file);
EOF
            log_success "数据库迁移完成"
        fi
    fi
}

# 初始化数据库
init_database() {
    if [ ! -f "$DB_FILE" ]; then
        log_info "初始化优化数据库..."
        mkdir -p "$DB_DIR"
        sqlite3 "$DB_FILE" << 'EOF'
CREATE TABLE optimizations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pod_name TEXT NOT NULL,
    optimization_type TEXT NOT NULL,
    file_count INTEGER DEFAULT 0,
    space_saved INTEGER DEFAULT 0,
    optimization_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    backup_path TEXT,
    status TEXT DEFAULT 'completed'
);

CREATE TABLE optimization_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL UNIQUE,
    optimization_type TEXT NOT NULL,
    original_size INTEGER,
    optimized_size INTEGER,
    compression_ratio REAL,
    optimization_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT DEFAULT '',
    last_modified DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE deletion_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pod_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    deletion_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT DEFAULT '',
    last_modified DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(pod_name, file_path)
);

-- 添加同步日志表
CREATE TABLE sync_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sync_file TEXT NOT NULL,
    sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT NOT NULL,
    status TEXT DEFAULT 'processed'
);

CREATE INDEX idx_pod_name ON optimizations(pod_name);
CREATE INDEX idx_optimization_time ON optimizations(optimization_time);
CREATE INDEX idx_sync_file ON sync_log(sync_file);
EOF
        log_success "数据库初始化完成"
    else
        # 对现有数据库执行迁移
        migrate_database
    fi
}

# 文件级优化检查和记录
is_file_optimized() {
    local file_path="$1"
    local optimization_type="$2"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    # 检查特定文件是否已经被优化过
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimization_files WHERE file_path='$file_path' AND optimization_type='$optimization_type';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

record_file_optimization() {
    local file_path="$1"
    local optimization_type="$2"
    local original_size="$3"
    local optimized_size="$4"
    local compression_ratio="$5"
    local user_id="${6:-$(git config user.name 2>/dev/null || echo "${USER:-unknown}")}"

    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT OR REPLACE INTO optimization_files (file_path, optimization_type, original_size, optimized_size, compression_ratio, optimization_time, user_id, last_modified)
VALUES ('$file_path', '$optimization_type', $original_size, $optimized_size, $compression_ratio, CURRENT_TIMESTAMP, '$user_id', CURRENT_TIMESTAMP);
EOF
    fi
}

# Pod级优化检查和记录
is_pod_optimized() {
    local pod_name="$1"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    
    # 检查数据库中是否有该Pod的优化记录
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimizations WHERE pod_name='$pod_name';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

record_optimization() {
    local pod_name="$1"
    local optimization_type="$2"
    local file_count="$3"
    local space_saved="$4"
    local backup_path="$5"

    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT INTO optimizations (pod_name, optimization_type, file_count, space_saved, backup_path)
VALUES ('$pod_name', '$optimization_type', $file_count, $space_saved, '$backup_path');
EOF
    fi
}

get_optimization_history() {
    local pod_name="$1"
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "SELECT optimization_time, file_count, space_saved FROM optimizations WHERE pod_name='$pod_name' ORDER BY optimization_time DESC LIMIT 5;" 2>/dev/null
    fi
}

# 删除进度管理函数
record_deletion_candidate() {
    local pod_name="$1"
    local file_path="$2"
    local user_id="${3:-$(git config user.name 2>/dev/null || echo "${USER:-unknown}")}"
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "INSERT OR REPLACE INTO deletion_progress (pod_name, file_path, status, user_id, last_modified) VALUES ('$pod_name', '$file_path', 'pending', '$user_id', CURRENT_TIMESTAMP);" 2>/dev/null
    fi
}

mark_deletion_completed() {
    local pod_name="$1"
    local file_path="$2"
    local user_id="${3:-$(git config user.name 2>/dev/null || echo "${USER:-unknown}")}"
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "UPDATE deletion_progress SET status='completed', deletion_time=CURRENT_TIMESTAMP, user_id='$user_id', last_modified=CURRENT_TIMESTAMP WHERE pod_name='$pod_name' AND file_path='$file_path';" 2>/dev/null
    fi
}

is_deletion_completed() {
    local pod_name="$1"
    local file_path="$2"
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM deletion_progress WHERE pod_name='$pod_name' AND file_path='$file_path' AND status='completed';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

# 数据库清理和重置函数
reset_optimization_records() {
    echo -n "确认重置所有优化记录? 这将允许重新优化所有Pod库 [y/N]: "
    read -r confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        if [ -f "$DB_FILE" ]; then
            sqlite3 "$DB_FILE" "DELETE FROM optimizations;"
            sqlite3 "$DB_FILE" "DELETE FROM optimization_files;"
            sqlite3 "$DB_FILE" "DELETE FROM deletion_progress;"
            log_success "优化记录已重置"
        else
            log_warning "没有找到优化记录数据库"
        fi
    else
        log_info "取消重置操作"
    fi
}

show_deletion_progress() {
    log_info "删除进度记录:"
    echo ""
    
    if [ ! -f "$DB_FILE" ]; then
        log_warning "暂无删除进度记录"
        return
    fi
    
    sqlite3 -header -column "$DB_FILE" << 'EOF'
SELECT 
    pod_name as "Pod库",
    COUNT(*) as "待删除文件数",
    SUM(CASE WHEN status='completed' THEN 1 ELSE 0 END) as "已删除",
    SUM(CASE WHEN status='pending' THEN 1 ELSE 0 END) as "待处理"
FROM deletion_progress 
GROUP BY pod_name
ORDER BY pod_name;
EOF
}

clean_deletion_progress() {
    echo -n "确认清理已完成的删除记录? [y/N]: "
    read -r confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        if [ -f "$DB_FILE" ]; then
            local deleted_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM deletion_progress WHERE status='completed';" 2>/dev/null || echo "0")
            sqlite3 "$DB_FILE" "DELETE FROM deletion_progress WHERE status='completed';"
            log_success "已清理 $deleted_count 条已完成的删除记录"
        else
            log_warning "没有找到删除进度数据库"
        fi
    else
        log_info "取消清理操作"
    fi
}

show_optimization_history() {
    log_info "优化历史记录:"
    echo ""
    
    if [ ! -f "$DB_FILE" ]; then
        log_warning "暂无优化记录"
        return
    fi
    
    sqlite3 -header -column "$DB_FILE" << 'EOF'
SELECT 
    pod_name as "Pod库",
    optimization_type as "优化类型",
    file_count as "文件数",
    ROUND(space_saved/1024.0, 2) as "节省(KB)",
    datetime(optimization_time, 'localtime') as "优化时间"
FROM optimizations 
ORDER BY optimization_time DESC 
LIMIT 20;
EOF
}
