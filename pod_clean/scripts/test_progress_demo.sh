#!/bin/bash

# 进度条效果演示脚本
# 用于展示新的现代化进度条效果

# 导入进度条函数
source "$(dirname "$0")/scripts/lib/unused_detector.sh"

echo "=== 现代化进度条演示 ==="
echo ""

echo "1. 动态 Spinner 效果 (类似 yarn install)："
echo "   启动动态进度条，按 Ctrl+C 停止"
echo ""

# 演示动态进度条（3秒后自动停止）
show_dynamic_progress "正在下载依赖" &
progress_pid=$!
sleep 3
kill $progress_pid 2>/dev/null || true
wait $progress_pid 2>/dev/null || true
printf "\r%*s\r" "80" ""
echo "   ✓ 动态进度条演示完成"
echo ""

echo "2. 百分比进度条效果："
echo ""

# 演示百分比进度条
total=50
for current in $(seq 0 5 $total); do
    show_modern_progress "$current" "$total" "处理文件"
    sleep 0.2
done
echo ""
echo "   ✓ 百分比进度条演示完成"
echo ""

echo "3. 实际扫描场景模拟："
echo ""

# 模拟真实扫描场景
total_files=100
for current in $(seq 1 $total_files); do
    show_modern_progress "$current" "$total_files" "扫描图片"
    # 模拟处理时间
    sleep 0.05
done
echo ""
echo "   ✓ 扫描场景模拟完成"
echo ""

echo "=== 演示结束 ==="
echo ""
echo "新进度条特性："
echo "  ✓ Unicode Spinner 动画 (⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏)"
echo "  ✓ 可视化进度条 [████████████░░░░░░░░]"
echo "  ✓ 百分比显示"
echo "  ✓ 计数器显示 (当前/总数)"
echo "  ✓ 自定义消息支持"
echo "  ✓ 实时更新，不换行"
