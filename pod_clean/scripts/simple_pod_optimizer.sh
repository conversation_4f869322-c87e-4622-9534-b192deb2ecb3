#!/bin/bash

# 简化版Pod库优化工具
# 专注于核心功能，便于调试
# 
# 注意: 此脚本为独立版本
# 推荐使用: ../pod_optimizer.sh (基于新架构，功能更完整)
# 迁移建议: 功能已整合到新的模块化架构中
# 版本: v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 路径配置（统一备份根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_ROOT="$BASE_DIR/backups"
RUN_BACKUP_DIR="$BACKUP_ROOT/simple_optimization_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RUN_BACKUP_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 简化版Pod库优化工具 v1.0                      ║"
    echo "║                                                              ║"
    echo "║  功能: 发现并优化Pod库中的图片资源                            ║"
    echo "║  支持: 图片压缩、资源统计、批量处理                           ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 备份：保留原相对目录结构
backup_file_preserve_structure() {
    local src_file="$1"
    local backup_root="$2"
    local rel_path="${src_file#./}"
    rel_path="${rel_path#/}"
    local dest_path="$backup_root/$rel_path"
    mkdir -p "$(dirname "$dest_path")"
    cp "$src_file" "$dest_path"
}

# 发现Pod库
discover_pods() {
    log_info "扫描Pod库..."
    
    local pods=()
    local excluded=("BBJShellApp" "MeetYouApp" "YoubaobaoApp_ci2" "meetyou-ci-5" "iOS" "vibe_coding" "ImageComparison" "ImageQualityComparison" "PictureComparison" "pod_clean")
    
    # 使用临时文件收集Pod库
    local temp_file=$(mktemp)
    
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        
        # 跳过备份目录
        if [[ "$name" == backup_* ]] || [[ "$name" == resource_backup_* ]] || [[ "$name" == unused_resources_backup_* ]]; then
            continue
        fi
        
        # 检查排除列表
        local skip=false
        for ex in "${excluded[@]}"; do
            if [[ "$name" == "$ex" ]]; then
                skip=true
                break
            fi
        done
        
        if [ "$skip" = false ]; then
            echo "$dir" >> "$temp_file"
        fi
    done
    
    # 读取到数组
    while IFS= read -r line; do
        pods+=("$line")
    done < "$temp_file"
    rm -f "$temp_file"
    
    echo "${pods[@]}"
}

# 分析单个Pod库
analyze_pod() {
    local pod_dir="$1"
    local pod_name=$(basename "$pod_dir")
    
    log_info "分析Pod库: $pod_name"
    
    # 获取基本信息
    local size=$(du -sh "$pod_dir" 2>/dev/null | cut -f1 || echo "未知")
    local png_count=$(find "$pod_dir" -name "*.png" 2>/dev/null | wc -l)
    local jpg_count=$(find "$pod_dir" -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
    local gif_count=$(find "$pod_dir" -name "*.gif" 2>/dev/null | wc -l)
    local total_images=$((png_count + jpg_count + gif_count))
    
    echo "  📁 目录: $pod_dir"
    echo "  📏 大小: $size"
    echo "  🖼️  图片: $total_images 个 (PNG: $png_count, JPG: $jpg_count, GIF: $gif_count)"
    
    if [ $total_images -gt 0 ]; then
        echo "  ✅ 适合优化"
        return 0
    else
        echo "  ⚠️  无图片资源"
        return 1
    fi
}

# 优化单个Pod库
optimize_pod() {
    local pod_dir="$1"
    local pod_name=$(basename "$pod_dir")
    
    log_header "开始优化: $pod_name"
    
    # 创建备份目录（统一到 backups 下，以本次运行批次+pod名归档）
    local backup_dir="$RUN_BACKUP_DIR/$pod_name"
    mkdir -p "$backup_dir"
    
    local total_saved=0
    local compressed_count=0
    
    # 进入Pod目录
    cd "$pod_dir"
    
    # 压缩PNG文件（大于50KB的）
    log_info "压缩PNG文件..."
    find . -name "*.png" -size +50k | while read file; do
        if [ -f "$file" ]; then
            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            
            # 备份原文件（保留相对目录结构）
            backup_file_preserve_structure "$file" "$backup_dir"
            
            # 压缩（如果有pngquant）
            if command -v pngquant >/dev/null 2>&1; then
                pngquant --quality=65-80 --force --ext .png "$file" 2>/dev/null || true
                
                local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                local saved=$((original_size - new_size))
                
                if [ $saved -gt 0 ]; then
                    echo "    压缩: $(basename "$file") 节省 $((saved / 1024))KB"
                fi
            else
                echo "    跳过: $(basename "$file") (未安装pngquant)"
            fi
        fi
    done
    
    # 优化JPG文件
    log_info "优化JPG文件..."
    find . -name "*.jpg" -o -name "*.jpeg" | while read file; do
        if [ -f "$file" ]; then
            local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            
            # 备份原文件（保留相对目录结构）
            backup_file_preserve_structure "$file" "$backup_dir"
            
            # 优化（如果有jpegoptim）
            if command -v jpegoptim >/dev/null 2>&1; then
                jpegoptim --max=80 --strip-all "$file" 2>/dev/null || true
                
                local new_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
                local saved=$((original_size - new_size))
                
                if [ $saved -gt 0 ]; then
                    echo "    优化: $(basename "$file") 节省 $((saved / 1024))KB"
                fi
            else
                echo "    跳过: $(basename "$file") (未安装jpegoptim)"
            fi
        fi
    done
    
    # 返回到pod_clean目录
    cd - > /dev/null
    
    log_success "优化完成: $pod_name"
    echo "  备份目录: $backup_dir"
}

# 主函数
main() {
    show_banner
    
    # 发现Pod库
    local pods_str=$(discover_pods)
    IFS=' ' read -ra PODS <<< "$pods_str"
    
    if [ ${#PODS[@]} -eq 0 ]; then
        log_error "未发现任何Pod库"
        exit 1
    fi
    
    log_success "发现 ${#PODS[@]} 个Pod库"
    
    case "${1:-}" in
        --list|-l)
            log_header "Pod库列表"
            for i in "${!PODS[@]}"; do
                echo ""
                echo "=== $((i+1)). $(basename "${PODS[$i]}") ==="
                analyze_pod "${PODS[$i]}"
            done
            ;;
        --optimize|-o)
            if [ -z "$2" ]; then
                log_error "请指定Pod库名称"
                exit 1
            fi
            
            local target="$2"
            local found=false
            
            for pod in "${PODS[@]}"; do
                if [[ "$(basename "$pod")" == "$target" ]]; then
                    if analyze_pod "$pod"; then
                        echo ""
                        echo -n "确认优化此Pod库? [y/N]: "
                        read -r confirm
                        if [[ "$confirm" =~ ^[Yy]$ ]]; then
                            optimize_pod "$pod"
                        else
                            log_info "取消优化"
                        fi
                    fi
                    found=true
                    break
                fi
            done
            
            if [ "$found" = false ]; then
                log_error "未找到Pod库: $target"
                exit 1
            fi
            ;;
        --all|-a)
            log_header "批量优化所有Pod库"
            
            local optimizable_pods=()
            for pod in "${PODS[@]}"; do
                if analyze_pod "$pod"; then
                    optimizable_pods+=("$pod")
                fi
                echo ""
            done
            
            if [ ${#optimizable_pods[@]} -eq 0 ]; then
                log_warning "没有适合优化的Pod库"
                exit 0
            fi
            
            echo "发现 ${#optimizable_pods[@]} 个适合优化的Pod库"
            echo -n "确认批量优化? [y/N]: "
            read -r confirm
            
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                for pod in "${optimizable_pods[@]}"; do
                    echo ""
                    optimize_pod "$pod"
                done
                log_success "批量优化完成！"
            else
                log_info "取消批量优化"
            fi
            ;;
        --help|-h|*)
            echo "简化版Pod库优化工具"
            echo ""
            echo "用法:"
            echo "  $0 --list              列出所有Pod库"
            echo "  $0 --optimize POD_NAME 优化指定Pod库"
            echo "  $0 --all               批量优化所有Pod库"
            echo "  $0 --help              显示帮助"
            echo ""
            echo "示例:"
            echo "  $0 --list"
            echo "  $0 --optimize zzimymain"
            echo "  $0 --all"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
