#!/bin/bash

# Pod库优化工具 - 选择性优化模块
# 功能: 提供交互式Pod库选择和优化功能
# 用法: source "$SCRIPT_DIR/modules/selective_optimizer.sh"
# 依赖: common.sh, database.sh, batch_optimizer.sh
# 版本: v1.0

# 选择性优化Pod库
selective_optimize_pods() {
    log_info "选择性Pod库优化..."
    echo ""

    # 选择优化类型
    echo "选择优化类型:"
    echo "1. 压缩图片 (减少文件大小，保持文件)"
    echo "2. 删除图片 (仅删除无引用图片)"
    echo "3. 混合优化 (先删除无引用，再压缩)"
    echo "4. 转换为HEIC (先转HEIC，再压缩剩余PNG/JPG)"
    echo -n "请选择 [1/2/3/4]: "
    read -r opt_type

    local optimization_mode="compress"
    case "$opt_type" in
        1) optimization_mode="compress" ;;
        2) optimization_mode="delete" ;;
        3) optimization_mode="mixed" ;;
        4) optimization_mode="heic" ;;
        *)
            log_error "无效选择，使用默认压缩模式"
            optimization_mode="compress"
            ;;
    esac

    # 列出所有可用的Pod库
    echo ""
    echo "可用的Pod库:"
    local pod_list=()
    local index=1

    # 使用临时文件避免子shell问题
    local temp_pods=$(mktemp)
    find .. -maxdepth 2 -name "*.podspec" > "$temp_pods"
    
    while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录
        if ! is_valid_pod_directory "$name"; then
            continue
        fi

        # 检查优化状态
        local status="未优化"
        if is_pod_optimized "$name"; then
            status="已优化"
        fi

        # 统计图片文件 - 使用深度扫描
        cd "$dir"
        local png_count=$(scan_pod_images "." "png" | wc -l)
        local jpg_count=$(scan_pod_images "." "jpg" | wc -l)
        local gif_count=$(scan_pod_images "." "gif" | wc -l)
        local total_images=$((png_count + jpg_count + gif_count))
        cd - > /dev/null

        echo "$index. $name (PNG: $png_count, JPG: $jpg_count, GIF: $gif_count, 总计: $total_images) [$status]"
        pod_list+=("$name:$dir")
        index=$((index + 1))
    done < "$temp_pods"
    
    rm -f "$temp_pods"

    if [ ${#pod_list[@]} -eq 0 ]; then
        log_warning "没有找到可优化的Pod库"
        return
    fi

    echo ""
    echo "请选择要优化的Pod库 (输入序号，多个用空格分隔，或输入 'all' 优化所有):"
    read -r selection

    # 创建备份目录到统一的 backups 目录下
    mkdir -p "$BACKUP_ROOT"
    local backup_dir="$BACKUP_ROOT/selective_optimization_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    if [ "$selection" = "all" ]; then
        log_info "优化所有Pod库 (模式: $optimization_mode)..."
        for pod_entry in "${pod_list[@]}"; do
            local pod_name="${pod_entry%%:*}"
            local pod_dir="${pod_entry##*:}"
            local pod_backup="$backup_dir/${pod_name}"
            mkdir -p "$pod_backup"

            if [ "$optimization_mode" = "mixed" ]; then
                # 混合模式：先删除，再压缩
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "delete"
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "compress"
            elif [ "$optimization_mode" = "heic" ]; then
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "heic"
            else
                optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "$optimization_mode"
            fi
            echo ""
        done
    else
        # 解析选择的序号
        for num in $selection; do
            if [[ "$num" =~ ^[0-9]+$ ]] && [ "$num" -ge 1 ] && [ "$num" -le ${#pod_list[@]} ]; then
                local selected_pod="${pod_list[$((num - 1))]}"
                local pod_name="${selected_pod%%:*}"
                local pod_dir="${selected_pod##*:}"
                local pod_backup="$backup_dir/${pod_name}"
                mkdir -p "$pod_backup"

                if [ "$optimization_mode" = "mixed" ]; then
                    # 混合模式：先删除，再压缩
                    optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "delete"
                    optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "compress"
                else
                    optimize_pod "$pod_name" "$pod_dir" "$pod_backup" "$optimization_mode"
                fi
                echo ""
            else
                log_error "无效选择: $num"
            fi
        done
    fi

    log_success "选择性优化完成，备份目录: $backup_dir"
}
