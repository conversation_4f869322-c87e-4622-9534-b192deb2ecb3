# Pod库优化工具

一个用于优化iOS Pod库中图片资源的工具集合，支持图片压缩、重复文件检测、无引用资源清理、质量对比分析等功能。

## 功能特性

- 🖼️ **图片压缩优化**: 使用pngquant和jpegoptim对PNG/JPG图片进行无损或有损压缩
- 🌈 **HEIC 转换优化**: 智能将可安全转换的 PNG/JPG 转为 HEIC，并统计节省体积（含数据库记录与报告展示）
- 🔍 **重复文件检测**: 基于MD5哈希检测完全相同的图片文件
- 🧹 **无引用资源清理**: 智能检测代码中未引用的图片资源
- ⚡ **并发优化**: 支持16路并发处理，大幅提升图片引用检测速度
- 🚀 **高速搜索**: 集成ripgrep工具，搜索性能比grep提升3-10倍
- 📊 **质量对比分析**: PSNR/SSIM质量指标分析，生成详细对比报告
- 💾 **安全备份**: 所有操作前自动备份原始文件
- 🎯 **增量优化**: 智能跳过已优化文件，支持文件级增量处理
- 📈 **进度跟踪**: 实时显示处理进度和结果
- 🗄️ **数据库记录**: SQLite数据库记录优化历史和文件状态
- 🔄 **Git集成**: 支持Git贮藏管理，便于版本控制
- 🧩 **资源引用同步**: HEIC 转换后自动更新同目录 `Contents.json` 的 `filename` 字段，避免 Asset Catalog 引用失效
- 🧪 **命令行测试**: 支持-t参数直接测试各种优化类型

## 快速开始

### 1. 一键安装依赖

```bash
# 自动检测系统并安装所有依赖
./pod_clean/init.sh

# 或手动安装（macOS 示例）
brew install pngquant jpegoptim imagemagick libheif jq sqlite python3 ripgrep
pip3 install opencv-python numpy scikit-image Pillow
```

### 2. 运行优化

```bash
# 进入工具目录
cd pod_clean

# 交互式运行
./pod_optimizer.sh

# 命令行测试（推荐用于快速验证）
./pod_optimizer.sh -t delete     # 测试删除无引用图片
./pod_optimizer.sh -t compress   # 测试图片压缩
./pod_optimizer.sh -t heic       # 测试HEIC转换
./pod_optimizer.sh -t mixed      # 测试混合优化

# 推荐：启用 HEIC 转换优化
# 在交互菜单中选择 "4. 转换为HEIC (先转HEIC，再压缩剩余PNG/JPG)"
```

### 主入口脚本
**`pod_optimizer.sh`** ⭐ 统一入口工具
```bash
# 交互式菜单
./pod_optimizer.sh

# 全部优化
./pod_optimizer.sh -b

# 选择性优化
./pod_optimizer.sh -s

# Git贮藏管理
./pod_optimizer.sh --stash



# 查看优化历史
./pod_optimizer.sh -H

# 生成质量报告
./pod_optimizer.sh -q

# 重置优化记录
./pod_optimizer.sh --reset

# 新增：HEIC 转换
# 在菜单中选择：4. 转换为HEIC (先转HEIC，再压缩剩余PNG/JPG)
```

## 工具说明

### 主要脚本

- `pod_optimizer.sh` - 主优化工具，提供交互式菜单和增量优化
- `init.sh` - 一键依赖安装脚本，支持macOS/Ubuntu/CentOS
- `scripts/generate_quality_report.sh` - 图片质量对比报告生成器
- `scripts/image_quality_comparison.py` - Python质量分析工具

### 辅助脚本

- `scripts/complete_pod_optimizer.sh` - 完整优化脚本（兼容旧版本）
- `scripts/clean_unused_resources.sh` - 清理无引用资源
- `scripts/basic_pod_list.sh` - 列出所有可用的Pod库

## 使用方法

### 交互式模式

运行主程序后，选择相应的功能：

```bash
./pod_optimizer.sh
```

菜单选项：
1. **全部优化** - 处理所有Pod库，内部增量优化
2. **选择性优化** - 手动选择要优化的Pod库
3. **Git贮藏管理** - 管理优化修改的Git贮藏
4. **查看优化历史** - 查看数据库中的优化记录
5. **生成质量对比报告** - 分析优化效果和图片质量
6. **查看删除进度** - 查看图片删除的进度状态
7. **清理删除记录** - 清理已完成的删除记录
8. **重置优化记录** - 清除数据库记录，允许重新优化

命令行选项：
- **-t delete** - 测试删除无引用图片（使用并发优化）
- **-t compress** - 测试图片压缩
- **-t heic** - 测试HEIC转换优化
- **-t mixed** - 测试混合优化（删除+压缩）

Tip: 也可在“全部优化/选择性优化”流程中直接选择 HEIC 作为优化类型。

可用环境变量：
- `HEIC_QUALITY`：HEIC 输出质量（0-100，默认 70）。

### 质量对比报告

```bash
# 生成压缩优化质量报告
./scripts/generate_quality_report.sh -c

# 生成删除优化质量报告
./scripts/generate_quality_report.sh -d

# 生成所有类型报告
./scripts/generate_quality_report.sh -a
```

## 📁 目录结构

```
pod_clean/
├── pod_optimizer.sh           ⭐ 主入口脚本
├── scripts/                   📁 子脚本目录
│   ├── complete_pod_optimizer.sh
│   ├── clean_unused_resources.sh
│   ├── generate_report.sh
│   └── ...
├── db/                        📁 数据库目录
│   └── optimization.db       🗄️ SQLite优化记录
├── reports/                   📁 报告目录 (自动生成)
└── backups/                   📁 备份目录 (自动生成)
```

## 🚀 快速开始

### 1. 完整优化 (推荐)
```bash
cd pod_clean
./complete_pod_optimizer.sh
# 选择 'a' 进行完整批量优化
```

### 2. 简单优化
```bash
cd pod_clean
./basic_pod_list.sh
# 输入 "all" 优化所有Pod库
```



## 🔧 技术特性

### 分级优化策略
- **大型PNG (>50KB)**: pngquant 65-80%质量
- **中型PNG (20-50KB)**: pngquant 70-85%质量
- **小型PNG (10-20KB)**: pngquant 75-90%质量
- **JPG文件**: jpegoptim 80%质量 + 元数据清理

### HEIC 转换策略
- **处理顺序**：先转 HEIC，再对剩余的 PNG/JPG 执行压缩（整体节省更优）。
- **安全规则**：
  - JPG/JPEG：尝试转换为 HEIC。
  - PNG：仅在无 Alpha 通道时转换为 HEIC；含 Alpha 的 PNG 跳过，避免破坏透明度。
- **内容检测**：macOS 使用 `sips` 检测 Alpha；通用环境优先 `magick identify`。
- **转换实现**：优先 `magick`（ImageMagick），其次 `sips`（macOS），最后 `heif-enc`（libheif 示例工具）。
- **收益判定**：仅当 HEIC 比原图更小且成功生成时，才用 HEIC 替换原图并删除原图。
- **引用同步**：若同目录存在 `Contents.json`，自动更新对应 `filename` 至 `.heic`（优先 `jq`，降级 `sed`）。

### 未引用资源检测策略
- **全面文件扫描**：覆盖代码文件、配置文件、文档等多种格式
  - iOS/移动端：`*.m, *.h, *.swift, *.mm, *.xib, *.storyboard, *.plist, *.strings`
  - 前端文件：`*.js, *.ts, *.jsx, *.tsx, *.vue, *.css`
  - 配置与数据：`*.json, *.txt, *.xml, *.yml, *.yaml, *.toml, *.ini, *.cfg, *.conf`
  - 其他语言：`*.dart, *.kt, *.java, *.cs, *.cpp, *.c, *.rb, *.py, *.php, *.go, *.rs, *.scala`
  - 脚本与文档：`*.sh, *.bat, *.ps1, *.md`
- **智能匹配模式**：
  - 完整文件名匹配（如 `<EMAIL>`）
  - 去扩展名匹配（如 `emo_027@3x`）
  - iOS 基础名称匹配（如 `all_search_delete` 匹配 `<EMAIL>`）
  - 去状态后缀匹配（如 `button_normal`, `button_selected` → `button`）
- **典型应用场景**：
  - `EmoticonValueList.plist` 等清单型精确引用
  - `statistics_cloud_list.txt` 等文本列表引用
  - `imy_imageForKey:@"all_search_delete"` 等代码中的不带扩展名引用
  - Asset Catalog 引用（自动排除 `Contents.json`，检测 `.imageset` 名称引用）
- **智能排除**：
  - 自动排除 `Contents.json` 文件的扫描，避免Asset Catalog内部引用干扰
  - 动画序列帧检测（如 `ani_good1`, `ani_good2`, ...），自动识别并跳过删除
- **断点续传**：删除操作记录到数据库，支持中断后从断点继续，避免重复删除
- **并发优化**：支持最高16路并发，根据系统核心数自动调整
- **工具自适应**：优先使用GNU parallel > xargs -P > 串行，自动降级
- **高速搜索**：优先使用ripgrep，不可用时回退到grep
- **调试支持**：运行时显示引用发现详情，便于验证检测准确性

### 安全保障
- ✅ 自动备份所有修改文件
- ✅ 时间戳命名防止冲突
- ✅ 支持完全回滚
- ✅ Git忽略配置

### 智能功能
- 🔍 自动发现Pod库
- 📊 实时统计和报告
- 🎯 分级处理策略
- 📄 自动生成.md报告

## 📄 报告系统

### 自动报告生成
所有优化工具都会自动生成以日期时间命名的.md报告：
- `Pod库优化报告_YYYYMMDD_HHMMSS.md`
- 包含详细统计、优化结果、备份信息
- 支持回滚指导和后续建议

### 手动报告生成
```bash
./generate_report.sh  # 基于现有备份生成报告
```

## 🛡️ 安全和回滚

### 备份结构
```
pod_clean/
├── complete_optimization_backup_YYYYMMDD_HHMMSS/
│   ├── BBJBabyHome/
│   ├── BBJMainProject/
│   ├── ChatAI/
│   └── ...
```

### 回滚方法
```bash
# 回滚单个Pod库
cd ../zzimymain
cp ../pod_clean/backups/complete_optimization_backup_*/zzimymain/* [资源目录]/

# 批量回滚
for pod in complete_optimization_backup_*/*/; do
    pod_name=$(basename "$pod")
    echo "回滚 $pod_name..."
    cd "../$pod_name"
    cp "../pod_clean/backups/$pod"* [对应目录]/
    cd - > /dev/null
done
```

## 📈 优化效果

### 处理规模对比
- **第一次优化**: 30个文件 (仅大文件)
- **完整优化**: 897个文件 (分级处理)
- **HEIC 转换**: 新增现代图片格式支持
- **引用检测**: 覆盖 20+ 种文件格式，避免误删
- **提升倍数**: 30倍+

### 预期效果
- **空间节省**: 
  - 传统压缩：5-15MB (保守估计)
  - HEIC 转换：额外 10-30% 体积减少
  - 无引用清理：根据项目情况可节省数MB
- **启动性能**: 提升5-10%
- **内存优化**: 减少图片加载内存占用
- **兼容性**: 现代设备支持 HEIC 格式，旧设备保留 PNG/JPG

## 📋 后续计划

### 立即可执行
1. **功能测试**: 验证应用功能正常
2. **性能测试**: 对比优化前后效果
3. **重复资源清理**: 使用智能工具
4. **未使用资源清理**: 清理确认无用文件

### 中期规划
1. **WebP转换**: 现代图片格式
2. **按需加载**: 动态资源加载
3. **监控系统**: 包体积变化监控
4. **CI/CD集成**: 自动化优化

## 🎯 最佳实践

### 优化流程
1. **备份项目** (虽然工具会自动备份)
2. **运行完整优化** (`complete_pod_optimizer.sh`)
3. **查看生成报告** (自动生成的.md文件)
4. **功能测试** (确保应用正常)
5. **性能测试** (对比优化效果)

### 维护建议
- **定期优化**: 每月执行一次
- **监控包体积**: 关注新增资源
- **更新工具**: 定期更新优化策略
- **团队规范**: 建立资源使用规范

## 🔍 故障排除

### 常见问题
**Q: 工具提示缺少pngquant/jpegoptim？**
```bash
# macOS
brew install pngquant jpegoptim imagemagick libheif jq

# Ubuntu/Debian  
sudo apt-get install pngquant jpegoptim imagemagick libheif-examples libheif1 jq
```

**Q: 优化后应用异常？**
```bash
# 从备份恢复
cp backup_*/[Pod库名]/* [对应目录]/
```

**Q: 图片被误删但确实有引用？**
- 检查引用文件是否在支持的扫描范围内（见未引用资源检测策略）
- 运行时查看"引用发现"调试信息
- 如果引用方式特殊，可手动将文件从备份中恢复

**Q: HEIC 转换后应用无法显示图片？**
- 检查目标设备是否支持 HEIC 格式（iOS 11+ / macOS 10.13+）
- 确认 `Contents.json` 是否正确更新
- 从备份恢复原始 PNG/JPG 格式

**Q: 统计数据不准确？**
- 使用最新的`complete_pod_optimizer.sh`
- 检查备份目录中的实际文件数量

## 📞 技术支持

### 工具更新
- 持续改进优化算法
- 新增功能和策略
- 修复发现的问题

### 反馈渠道
- 使用问题和建议
- 优化效果反馈
- 新功能需求

---

**工具版本**: v6.0  
**最后更新**: 2025-08-14  
**维护状态**: 活跃开发中  

🎉 **已为您的Pod库生态系统建立了完整的优化解决方案！**
