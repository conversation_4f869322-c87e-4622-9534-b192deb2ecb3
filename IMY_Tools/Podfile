platform :ios,8.0
inhibit_all_warnings!

target 'IMY_Tools' do

source 'https://github.com/CocoaPods/Specs.git'
pod 'StandardPaths'
pod 'SZTextView'
pod 'MXParallaxHeader',:git => '*********************:iOS/MXParallaxHeader.git'

source 'https://gitlab.meiyou.com/iOS/imyspecs.git'
pod 'IMYFoundation',:git => '*********************:iOS/IMYFoundation.git'
pod 'IMYVendor', :git => "*********************:iOS/IMYVendor", :branch => 'dev'
pod 'IMYPublic',:git => '*********************:iOS/IMYPublic', :branch => 'dev'
pod 'IMY_ViewKit',:git => '*********************:iOS/IMY_ViewKit.git', :branch => 'dev'
pod 'IMYMWPhotoBrowser',:git => "*********************:iOS/IMYMWPhotoBrowser"
pod 'libwebp',:git => "*********************:iOS/libwebp.git"
pod 'TaeSDK',:git=>'*********************:iOS/TaeSDK.git', :branch => 'feature/tae_sdk_3.1.1.206'
pod 'IMYTCP',:git => "*********************:iOS/IMYTCP", :branch => "dev"
pod 'IMYUserTracer',:git=>'*********************:iOS/IMYUserTracer',:branch => 'dev'
pod 'SDWebImage',:git=>'*********************:iOS/SDWebImage.git'
pod 'IMYYQBasicServices',:git=>'*********************:iOS/IMYYQBasicServices.git',:branch => 'dev'


#mark -_-恶心的广告，下面的依赖全部都是广告的
pod 'IMYAdvertisement',:git => '*********************:iOS/IMYAdvertisement.git', :branch => 'dev'
pod 'IMYVideoPlayer',:git => '*********************:iOS/IMYVideoPlayer', :branch => 'dev'
pod 'IMYReactNative' ,:git => "*********************:iOS/IMYReactNative", :branch => "dev_0.55_stable"
pod 'IMYAccount',:git => '*********************:iOS/IMYAccount.git', :branch => 'dev'
pod 'IMYLaunchController',:git => '*********************:iOS/IMYLaunchController.git', :branch => "dev"
pod 'IMYYoubi',:git => '*********************:iOS/IMY_Youbi.git', :branch => 'jingqi_6.5.3'
pod 'IMYTTQ',:git => '*********************:iOS/IMYTTQ.git', :branch => 'feature/7.4.6'
pod 'IMY_EBusiness',:git => '*********************:iOS/IMY_EBusiness.git', :branch => 'feature/jingqi_7.4.2'
pod 'RTLabel','1.0.1'

end


post_install do |installer|
    copy_pods_resources_path = "Pods/Target Support Files/Pods-IMY_Tools/Pods-IMY_Tools-resources.sh"
    string_to_replace = '--compile "${BUILT_PRODUCTS_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}"'
    assets_compile_with_app_icon_arguments = '--compile "${BUILT_PRODUCTS_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}" --app-icon "${ASSETCATALOG_COMPILER_APPICON_NAME}" --output-partial-info-plist "${BUILD_DIR}/assetcatalog_generated_info.plist"'
    text = File.read(copy_pods_resources_path)
    new_contents = text.gsub(string_to_replace, assets_compile_with_app_icon_arguments)
    File.open(copy_pods_resources_path, "w") {|file| file.puts new_contents }
end
