//
//  IMYVaccinumUITests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/11/10.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>

@interface IMYVaccinumUITests : XCTestCase

@end

@implementation IMYVaccinumUITests

- (void)setUp {
    [super setUp];
    
    // Put setup code here. This method is called before the invocation of each test method in the class.
    
    // In UI tests it is usually best to stop immediately when a failure occurs.
    self.continueAfterFailure = NO;
    // UI tests must launch the application that they test. Doing this in setup will make sure it happens for each test method.
    [[[XCUIApplication alloc] init] launch];

    // In UI tests it’s important to set the initial state - such as interface orientation - required for your tests before they run. The setUp method is a good place to do this.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // Use recording to get started writing UI tests.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

/**
 用例名称：接种xx疫苗（五联疫苗 第2针）
 前置条件：该疫苗未接种
 步骤：
 1.选中小工具列表的“宝宝疫苗”
 2.找到该疫苗，然后勾选
 3.进入疫苗详情页，取消勾选
 4.返回疫苗列表页，查看该疫苗是否被取消
 5.返回
 */
- (void)testCheckAndCancel {
    
    XCUIApplication *app = [[XCUIApplication alloc] init];
    
    //检测 title
    XCUIElement *titleElement = app.navigationBars[@"小工具"].staticTexts[@"小工具"];
    XCTAssert(titleElement.exists, @"title 错误");
    
    //点击 “宝宝疫苗”
    XCUIElementQuery *tablesQuery = app.tables;
    [tablesQuery.staticTexts[@"宝宝疫苗"] tap];
    
    //检测 title
    titleElement = app.navigationBars[@"宝宝疫苗"].staticTexts[@"宝宝疫苗"];
    XCTAssert(titleElement.exists, @"title 错误");
    
    //向上滑动 tableView
    [tablesQuery.element swipeUp];
    
    //点击表格第10个疫苗(五联疫苗 第2针)
    XCUIElement *cellElement = [[tablesQuery childrenMatchingType:XCUIElementTypeCell] elementBoundByIndex:10];
    XCUIElement *buttonElement = [cellElement childrenMatchingType:XCUIElementTypeButton].element;
    
    //疫苗勾选前后，都要检查下状态
    XCTAssert([buttonElement.value isEqualToString:@"unmark"], @"当前疫苗已打");
    [buttonElement tap];
    XCTAssert([buttonElement.value isEqualToString:@"mark"], @"疫苗勾选失败");
    
    //进疫苗详情页
    [cellElement tap];
    
    //检测 title
    titleElement = app.navigationBars[@"疫苗详情"].staticTexts[@"疫苗详情"];
    XCTAssert(titleElement.exists, @"title 错误");
    
    //检测 cell 数量
    XCUIElementQuery *cellQuery = [tablesQuery childrenMatchingType:XCUIElementTypeCell];
    NSAssert(cellQuery.count == 7, @"已打疫苗，cell数量应该为7");
    
    //取消疫苗勾选
    [[[[[XCUIApplication alloc] init].tables.cells elementBoundByIndex:0] childrenMatchingType:XCUIElementTypeButton].element tap];
    
    //检测 cell 数量
    cellQuery = [tablesQuery childrenMatchingType:XCUIElementTypeCell];
    NSAssert(cellQuery.count == 8, @"未打疫苗，cell数量应该为8");
    
    //返回
    [[app.navigationBars[@"疫苗详情"].otherElements childrenMatchingType:XCUIElementTypeButton].element tap];
    
    //在详情页取消的疫苗，在列表页，也要取消
    XCTAssert([buttonElement.value isEqualToString:@"unmark"], @"疫苗取消失败");
    
    //返回
    [[app.navigationBars[@"宝宝疫苗"].otherElements childrenMatchingType:XCUIElementTypeButton].element tap];
    
}

@end
