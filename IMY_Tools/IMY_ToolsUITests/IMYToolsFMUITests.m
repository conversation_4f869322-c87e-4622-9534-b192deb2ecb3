//
//  IMYToolsFMUITests.m
//  IMY_Tools
//
//  Created by hsm on 16/12/2.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>

@interface IMYToolsFMUITests : XCTestCase
@property (nonatomic, strong) XCUIApplication *app;
@end

@implementation IMYToolsFMUITests

- (void)setUp {
    [super setUp];
    
    self.continueAfterFailure = NO;
    [[[XCUIApplication alloc] init] launch];
    
    self.app = [[XCUIApplication alloc] init];
    [self.app.tables.staticTexts[@"数胎动"] tap];
    
    //等待PUSH 动画完成
    sleep(1);
    
    [self clearData];
}

- (void)tearDown {
    [super tearDown];
}

//测试没有数胎动时，UI
- (void)testNotCountingInitUI {
    
    XCTAssert(self.app.staticTexts[@"每天早、中、晚数一数宝宝的胎动吧~ 陪伴宝宝健康成长！"].exists, @"初始化-未数胎动，提醒文本不正确");
    XCTAssertFalse(self.app.staticTexts[@"5分钟内连续活动只算作1次有效胎动"].exists, @"初始化-5分钟提醒不应该出现");
    XCTAssertFalse(self.app.buttons[@"取消"].exists,@"初始化-cancel button不应该出现");
    XCTAssert(self.app.staticTexts[@"0次"], @"初始化-胎动次数不正确");
    XCTAssert(self.app.staticTexts[@"60:00"], @"初始化-剩余时间不正确");
    
    XCUIElement *bottonView = [self bottonView];
    XCTAssert(bottonView.exists,@"初始化-bottomView 不存在");
    XCTAssert(bottonView.frame.origin.y == [self screenHeight] - bottonView.frame.size.height,@"初始化-bottom 设置错误");
}

//测试正在数胎动时，UI
- (void)testCountingInitUI {
    
    //点击开始
    [self startCounting];
    
    XCTAssert(self.app.buttons[@"取消"].exists,@"数胎动中-cancel button不应该出现");
    XCTAssert(self.app.staticTexts[@"选择合理的姿势能够帮助你更好的体验宝宝的胎动哦~左侧躺下或是坐下，双手放在肚子两侧，放松身心静静地数。"].exists, @"数胎动中-提醒文本不正确");
    
    //等待动画结束
    sleep(2);
    
    XCUIElement *bottonView = [self bottonView];
    XCTAssert(bottonView.exists,@"初始化-bottomView 不存在");
    XCTAssert(bottonView.frame.origin.y >= [self screenHeight] - 35,@"数胎动中-bottom 设置错误");
}

//测试取消数胎动是，UI
- (void)testCancelUI {
    
    [self startCounting];
    sleep(2);
    
    //cancel record
    [self.app.buttons[@"取消"] tap];
    [self.app.buttons[@"确定"] tap];
    
    sleep(2);
    
    XCTAssert(self.app.staticTexts[@"每天早、中、晚数一数宝宝的胎动吧~ 陪伴宝宝健康成长！"].exists, @"cancel-未数胎动，提醒文本不正确");
    XCTAssertFalse(self.app.buttons[@"取消"].exists,@"cancel-cancel button不应该出现");
    
    XCUIElement *bottonView = [self bottonView];
    XCTAssert(bottonView.frame.origin.y == [self screenHeight] - bottonView.frame.size.height,@"初始化-bottom 设置错误");
}

//测试tap 和 drag 是否配合正常
- (void)testTapDrag {
    
    XCUIElement *pulldownButton = [self pulldownButton];
    
    [pulldownButton tap];
    sleep(2);
    XCTAssert([self isDownState],@"tap drag 配合错误-->应该是isDownState");
    
    [pulldownButton tap];
    
    sleep(2);
    XCTAssert([self isUpstate],@"tap drag 配合错误-->应该是isUpstate");
    
    sleep(2);
    [pulldownButton pressForDuration:0.2 thenDragToElement:self.app.tables.element];
    
    sleep(1);
    XCTAssert([self isDownState],@"tap drag 配合错误-->应该是isDownState");
    
    [pulldownButton pressForDuration:0.2 thenDragToElement:[self startButton]];
    
    sleep(2);
    
    XCTAssert([self isUpstate],@"tap drag 配合错误-->应该是isUpstate");
    
    [pulldownButton tap];
    
    sleep(1);
    XCTAssert([self isDownState],@"tap drag 配合错误-->应该是isDownState");
    
}



#pragma -mark element --
- (XCUIElement *)startButton {
    
    XCUIElement *element = [[[[self.app.otherElements containingType:XCUIElementTypeNavigationBar identifier:@"数胎动"] childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element;
    
    XCUIElement *button = [[[[[element childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:0] childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:1] childrenMatchingType:XCUIElementTypeButton].element;
    
    return button;
}

- (XCUIElement *)bottonView {
    
    return  [[[[[[self.app.otherElements containingType:XCUIElementTypeNavigationBar identifier:@"数胎动"] childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:1];
}

- (XCUIElement *)pulldownButton {
    
    return [[[[[[[[self.app.otherElements containingType:XCUIElementTypeNavigationBar identifier:@"数胎动"] childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:1] childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:0];
}
#pragma -mark private --
- (void)startCounting {
    
    XCUIElement *button = [self startButton];
    [button tap];
}

- (void)clearData {
    
    if(self.app.buttons[@"取消"].exists) {
        [self.app.buttons[@"取消"] tap];
        [self.app.buttons[@"确定"] tap];
        sleep(1.5);
    }
}

- (BOOL)isUpstate {
    XCUIElement *bottonView = [self bottonView];
    return bottonView.frame.origin.y == [self screenHeight] - bottonView.frame.size.height;
}

- (BOOL)isDownState {
    XCUIElement *bottonView = [self bottonView];
    return bottonView.frame.origin.y >= [self screenHeight] - 35;
}

- (CGFloat)screenWidth {
    
    XCUIElementQuery *query = self.app.windows;
    XCUIElement *window = [query elementBoundByIndex:0];
    return window.frame.size.width;
}

- (CGFloat)screenHeight {
    
    XCUIElementQuery *query = self.app.windows;
    XCUIElement *window = [query elementBoundByIndex:0];
    return window.frame.size.height;
}

@end
