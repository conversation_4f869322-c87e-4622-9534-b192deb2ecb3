//
//  IMYToolsBUUITests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/11/23.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>

@interface IMYToolsBUUITests : XCTestCase

@end

@implementation IMYToolsBUUITests

- (void)setUp {
    [super setUp];
    
    // Put setup code here. This method is called before the invocation of each test method in the class.
    
    // In UI tests it is usually best to stop immediately when a failure occurs.
    self.continueAfterFailure = NO;
    // UI tests must launch the application that they test. Doing this in setup will make sure it happens for each test method.
    [[[XCUIApplication alloc] init] launch];

    // In UI tests it’s important to set the initial state - such as interface orientation - required for your tests before they run. The setUp method is a good place to do this.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // Use recording to get started writing UI tests.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}


/**
 用例名称：测试“最大羊水深度(DVP)”
 前置条件：无
 步骤：
 1.进入“B超单解读”，并选中第37周
 2.选中“最大羊水深度”，并进入详情页
 3.设置偏大值
 4.设置偏小值
 5.设置正常值
 */
- (void)testDVP {
    
    //进入“B超单解读”，并选中第37周
    XCUIApplication *app = [[XCUIApplication alloc] init];
    [app.tables.staticTexts[@"B超单解读"] tap];
    XCUIElement *titleElement = app.navigationBars[@"B超单解读"].staticTexts[@"B超单解读"];
    XCTAssert(titleElement.exists, @"title 错误");
    [app.staticTexts[@"37周"] tap];
    
    //选中“最大羊水深度”，并进入详情页
    [app.scrollViews.otherElements.tables.staticTexts[@"最大羊水深度(DVP)"] tap];
    titleElement = app.navigationBars[@"最大羊水深度(DVP)"].staticTexts[@"最大羊水深度(DVP)"];
    XCTAssert(titleElement.exists, @"title 错误");
    
    //设置偏大值
    XCUIElement *textField = [[app.tables.cells containingType:XCUIElementTypeStaticText identifier:@"最大羊水深度"] childrenMatchingType:XCUIElementTypeTextField].element;
    [textField tap];
    XCUIElement *yypickerviewTableview0Table = app.tables[@"YYPickerView_TableView0"];
    [yypickerviewTableview0Table swipeUp];
    XCUIElement *button = app.buttons[@"确定"];
    [button tap];

    XCUIElement *referenceCell = [app.tables.cells elementBoundByIndex:1];
    XCTAssert(referenceCell.exists, @"referenceCell 错误");
    XCTAssert([referenceCell.value isEqualToString:@"输入值异常"], @"用户输入值应该为异常");
    
    //设置偏小值
    [textField tap];
    [yypickerviewTableview0Table swipeDown];
    [button tap];
    XCTAssert([referenceCell.value isEqualToString:@"输入值异常"], @"用户输入值应该为异常");
    
    //设置正常值
    [textField tap];
    [yypickerviewTableview0Table.staticTexts[@"3"] tap];
    [yypickerviewTableview0Table.staticTexts[@"5"] tap];
    [button tap];
    XCTAssert([referenceCell.value isEqualToString:@""], @"用户输入值应该为正常");
}

/**
 用例名称：测试是否可以输入
 前置条件：无
 步骤：
 1.进入“B超单解读”，并选中第37周
 2.选中“腹围”，并进入详情页，查看是否能输入
 3.选中“胎动”，并进入详情页，查看是否能输入
 */
- (void)testInputEnabled {
    
    //进入“B超单解读”，并选中第37周
    XCUIApplication *app = [[XCUIApplication alloc] init];
    [app.tables.staticTexts[@"B超单解读"] tap];
    XCUIElement *titleElement = app.navigationBars[@"B超单解读"].staticTexts[@"B超单解读"];
    XCTAssert(titleElement.exists, @"title 错误");
    [app.staticTexts[@"37周"] tap];
    
    //选中“腹围”，并进入详情页，查看是否能输入
    [app.scrollViews.otherElements.tables.staticTexts[@"腹围(AC)"] tap];
    XCUIElement *inputCellElement = [[XCUIApplication alloc] init].tables.staticTexts[@"腹围"];
    XCTAssert(inputCellElement.exists, @"当前指标可修改");
    [[app.navigationBars[@"腹围(AC)"].otherElements childrenMatchingType:XCUIElementTypeButton].element tap];
    
    //选中“胎动”，并进入详情页，查看是否能输入
    XCUIElement *table = app.tables.element;
    [table swipeUp];
    [app.scrollViews.otherElements.tables.buttons[@"胎动"] tap];
    inputCellElement = [[XCUIApplication alloc] init].tables.staticTexts[@"胎动"];
    XCTAssert(!inputCellElement.exists, @"当前指标不可修改");
    [[app.navigationBars[@"胎动(FM)"].otherElements childrenMatchingType:XCUIElementTypeButton].element tap];
    
}

/**
 用例名称：测试顶部条栏与表格的联动
 前置条件：无
 步骤：
 1.进入“B超单解读”，并选中第36周
 2.表格往左滑，往右滑，往右滑，往左滑，检查顶部条是否跟着移动至对应的周数
 3.在顶部条栏，点击35周，37周，36周，检查表格是否跟着移动至对应的周数
 */
- (void)testLinkage {
    
    //进入“B超单解读”
    [[[XCUIApplication alloc] init].tables.staticTexts[@"B超单解读"] tap];
    
    XCUIApplication *app = [[XCUIApplication alloc] init];
    XCUIElement *scrollView = app.scrollViews[@"IMYToolsGCTimesMenu"];
    XCUIElement *table = app.tables.element;
    [app.staticTexts[@"36周"] tap];
    //表格往左滑，查看顶部条栏是否跟着滚动至下一周
    XCTAssert([scrollView.value isEqualToString:@"36周"]);
    [table swipeLeft];
    sleep(1);
    XCTAssert([scrollView.value isEqualToString:@"37周"]);
    
    //表格往右滑，查看顶部条栏是否跟着滚动至上一周
    [table swipeRight];
    sleep(1);
    XCTAssert([scrollView.value isEqualToString:@"36周"]);
    
    //表格往右滑，查看顶部条栏是否跟着滚动至上一周
    [table swipeRight];
    sleep(1);
    XCTAssert([scrollView.value isEqualToString:@"35周"]);
    
    //表格往左滑，查看顶部条栏是否跟着滚动至下一周
    [table swipeLeft];
    sleep(1);
    XCTAssert([scrollView.value isEqualToString:@"36周"]);
    
    
    //点击顶部条栏，35周，查看表格是否跟着滚动至对应的周数
    XCTAssert([table.value isEqualToString:@"36周"]);
    [app.staticTexts[@"35周"] tap];
    sleep(1);
    XCTAssert([table.value isEqualToString:@"35周"]);
    
    //点击顶部条栏，37周，查看表格是否跟着滚动至对应的周数
    [app.staticTexts[@"37周"] tap];
    sleep(1);
    XCTAssert([table.value isEqualToString:@"37周"]);
    
    //点击顶部条栏，36周，查看表格是否跟着滚动至对应的周数
    [app.staticTexts[@"36周"] tap];
    sleep(1);
    XCTAssert([table.value isEqualToString:@"36周"]);
}

@end
