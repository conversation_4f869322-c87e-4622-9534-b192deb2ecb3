//
//  IMYToolsContractionUITest.m
//  IMY_Tools
//
//  Created by hsm on 16/12/13.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>

@interface IMYToolsContractionUITest : XCTestCase

@property (nonatomic, strong) XCUIApplication *app;
@property (nonatomic, assign) BOOL    originalIsFirst;

@end

@implementation IMYToolsContractionUITest

- (void)setUp {
    [super setUp];

    self.continueAfterFailure = NO;
    
    self.app = [[XCUIApplication  alloc] init];
    [self.app launch];
    
    [self.app.tables.staticTexts[@"数宫缩"] tap];
    
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}


//测试:正在数宫缩时，按返回键的逻辑是否会弹出提醒框
- (void)testBackWhenCounting {
    
    [[self startCountElement] tap];
    
    //back
    [[[[self.app.navigationBars[@"数宫缩"] childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:0] childrenMatchingType:XCUIElementTypeButton].element tap];
    
    
    XCUIElement *alert = self.app.alerts[@"提示"];
    
    XCTAssert(alert.exists, @"正在数宫缩，返回，应该要弹框提醒");
    [alert.buttons[@"取消"] tap];
    
    
    [[[[self.app.navigationBars[@"数宫缩"] childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:0] childrenMatchingType:XCUIElementTypeButton].element tap];
    
    alert = self.app.alerts[@"提示"];
    [alert.buttons[@"确定"] tap];

    XCTAssertFalse(self.app.navigationBars[@"数宫缩"].exists,@"应该返回上一级界面");
    
}

- (XCUIElement *)startCountElement {
    
    return [[[[[[[[[self.app.otherElements containingType:XCUIElementTypeNavigationBar identifier:@"数宫缩"] childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther].element childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:0] childrenMatchingType:XCUIElementTypeOther] elementBoundByIndex:1] childrenMatchingType:XCUIElementTypeButton].element;
}




@end
