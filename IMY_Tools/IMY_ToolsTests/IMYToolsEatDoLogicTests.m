//
//  IMYToolsEatDoLogicTests.m
//  IMY_Tools
//
//  Created by hsm on 16/8/2.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolsEatDoFoodListVC.h"

@interface IMYToolsEatDoLogicTests : XCTestCase

@property (nonatomic, strong) IMYToolsEatDoFoodListVC *eatListVC;

@end

@implementation IMYToolsEatDoLogicTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
    
    //alloc one example
    self.eatListVC = [[IMYToolsEatDoFoodListVC alloc] initWithTitle:@"水果" AndCid:32];
    //trigger life cycle
    [self.eatListVC beginAppearanceTransition:YES animated:NO];
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

//初始化是否正常
- (void)testInitData {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"imytools_caneat_logic"];
    
    __weak IMYToolsEatDoLogicTests *wself = self;
    
    //wait 5s for life cycle
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        
        //网络请求参数验证
        XCTAssertTrue([wself.eatListVC.paramList[@"crowd"] integerValue] == 0,@"crowd 应该等于 0");
        XCTAssertTrue([wself.eatListVC.paramList[@"matters"] integerValue] == 0,@"matters 应该等于 0");
        XCTAssertTrue([wself.eatListVC.paramList[@"title"] integerValue] == 0,@"title 应该等于 0");
        XCTAssertTrue([wself.eatListVC.paramList[@"size"] integerValue] == 20,@"size 应该等于 20");
        XCTAssertTrue([wself.eatListVC.path isEqualToString:@"taboo/search"],@"self.path should be \"taboo/search\"");
        
        //本地缓存路径验证
        XCTAssertTrue([wself.eatListVC.cacheName rangeOfString:@"ban_food_32_0_0"].location != NSNotFound,@"cacheName should be \"ban_food_32_0_0\"");
        
        [exp fulfill];
    });
    
    [self waitForExpectationsWithTimeout:10 handler:^(NSError * _Nullable error) {
        
        if(error) {
            XCTAssert(NO,@"time out");
        }
    }];
}
//筛选是否正常
- (void)testEatFilterHandle {
    
}


@end
