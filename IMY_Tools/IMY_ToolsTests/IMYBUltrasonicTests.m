//
//  IMYBUltrasonicTests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/6/13.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolNetworking.h"
#import "IMYToolsBUltrasonicModels.h"
#import "IMYToolsBUltrasonicExplainViewModel.h"

@interface IMYBUltrasonicTests : XCTestCase
@property (nonatomic, strong) IMYToolsBUltrasonicExplainViewModel *viewModel;
@end

@implementation IMYBUltrasonicTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
    self.viewModel = [[IMYToolsBUltrasonicExplainViewModel alloc] init];
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

//B超项目列表
- (void)testGetList {
    XCTestExpectation *expectation = [self expectationWithDescription:@"TestGetList"];
    self.viewModel.week = 40;
    [self.viewModel getBScanList:^(NSArray *list) {
        if (list.count == 0) {
            XCTFail(@"解析失败 或 数据为空");
        }
        [expectation fulfill];
    } error:^(NSError *error) {
        NSLog(@"error:%@", error);
        XCTFail(@"Fail");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

//修改了B超项的数值
- (void)testPostItem {
    XCTestExpectation *expectation = [self expectationWithDescription:@"testPostItem"];
    self.viewModel.week = 40;
    //肱骨长(HL)
    [self.viewModel postKey:@"hl" value:@"6.68" sucBlock:^{
        [expectation fulfill];
    } error:^(NSError *error) {
        NSLog(@"error:%@", error);
        XCTFail(@"Fail");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

@end
