//
//  IMY_ToolsTests.m
//  IMY_ToolsTests
//
//  Created by ffb on 15/7/30.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XCTest/XCTest.h>
#import "IMYToolNetworking.h"
#import "IMYToolModel.h"

@interface IMY_ToolsTests : XCTestCase

@end

@implementation IMY_ToolsTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    XCTAssert(YES, @"Pass");
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

//怀孕身份，生男生女
- (void)testBoyOrGirlForPregnancy {
    XCTestExpectation *expectation = [self expectationWithDescription:@"TestBoyOrGirlForPregnancy"];
    
    NSDictionary *param = @{@"catid":@(1),@"mode":@(1)};
    [IMYToolNetworking getWithHost:tools_seeyouyima_com path:@"tools" parameters:param successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        NSArray* array = [responseObject toModels:[IMYToolModel class]];
        if(array.count == 0) {
            XCTFail(@"Fail");
        }
        [expectation fulfill];
    } failedBlock:^(id error) {
        NSLog(@"error:%@", error);
        XCTFail(@"Fail");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

//备孕身份，生男生女
- (void)testBoyOrGirlForPregnancyForPregnant {
    XCTestExpectation *expectation = [self expectationWithDescription:@"TestBoyOrGirlForPregnancyForPregnant"];
    
    NSDictionary *param = @{@"catid":@(7),@"mode":@(2)};
    [IMYToolNetworking getWithHost:tools_seeyouyima_com path:@"tools" parameters:param successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        NSArray* array = [responseObject toModels:[IMYToolModel class]];
        if(array.count == 0) {
            XCTFail(@"Fail");
        }
        [expectation fulfill];
    } failedBlock:^(id error) {
        NSLog(@"error:%@", error);
        XCTFail(@"Fail");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

//其它身份，生男生女（似乎不会有这种需求，蛮写一下）
- (void)testBoyOrGirlForPregnancyOther {
    XCTestExpectation *expectation = [self expectationWithDescription:@"TestBoyOrGirlForPregnancyForLama"];
    
    NSDictionary *param = @{@"catid":@(1),@"mode":@(0)};
    [IMYToolNetworking getWithHost:tools_seeyouyima_com path:@"tools" parameters:param successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        NSArray* array = [responseObject toModels:[IMYToolModel class]];
        if(array.count == 0) {
            XCTFail(@"Fail");
        }
        [expectation fulfill];
    } failedBlock:^(id error) {
        NSLog(@"error:%@", error);
        XCTFail(@"Fail");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

@end
