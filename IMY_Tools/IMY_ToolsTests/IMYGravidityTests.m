//
//  IMYGravidityTests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/6/12.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolNetworking.h"
#import "SYGravidityCheckModel.h"

@interface IMYGravidityTests : XCTestCase

@end

@implementation IMYGravidityTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

//下载用户的产检数据
- (void)testFromServiceUserList {
    XCTestExpectation *expectation = [self expectationWithDescription:@"TestFromServiceUserList"];
    [IMYToolNetworking getWithHost:data_tataquan_com path:@"gravidity_check" parameters:@{@"type": @"user_list"} successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        NSArray *resultArray = [responseObject toModels:[SYGravidityCheckModel class]];
        if (resultArray.count == 0) {
            XCTFail(@"fail");
        }
        [expectation fulfill];
    } failedBlock:^(id error) {
        XCTFail(@"fail");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

//上传数据至服务端
- (void)testUploadToService {
    XCTestExpectation *expectation = [self expectationWithDescription:@"TestUploadToService"];
    NSArray *imageArray = @[@"http://sc.seeyouyima.com/tools-iOS-4338997-ed48a6c1f734b632d974c65d5f1d717b-298-339.jpg", @"http://sc.seeyouyima.com/tools-iOS-4338997-8fb4ed39379deed2fb5e384c7eb2624a-330-440.jpg"];
    NSDictionary *valueDic = @{@"add_time": @(0), @"check_photos": imageArray, @"doctor_suggest": @"多喝水", @"gravidity_check_time": @"1463500800", @"is_mark": @(0), @"notice_time": @(1463450400)};
    NSDictionary *param = @{@"11": valueDic};
    
    [IMYToolNetworking postWithHost:data_tataquan_com path:@"gravidity_check" parameters:param successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        [expectation fulfill];
    } failedBlock:^(id error) {
        XCTFail(@"fail");
        [expectation fulfill];
    }];
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

@end
