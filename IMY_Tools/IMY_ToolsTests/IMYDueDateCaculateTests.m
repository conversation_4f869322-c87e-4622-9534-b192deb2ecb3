//
//  IMYDueDateCaculateTests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/9/9.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import <IMY_ViewKit.h>
#import "IMYDueDateCaculateViewModel.h"

@interface IMYDueDateCaculateTests : XCTestCase
@property (nonatomic, strong) IMYDueDateCaculateViewModel *viewModel;
@end

@implementation IMYDueDateCaculateTests

- (void)setUp {
    [super setUp];
    self.viewModel = [[IMYDueDateCaculateViewModel alloc] init];
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

//检查末次经期的合法性
- (void)testLastMenses {
    NSDate *date = [[NSDate imy_today] dateBySubtractingDays:300];
    [[self.viewModel checkLastMenses:date] subscribeNext:^(RACTuple *tuple) {
        BOOL valid = [tuple.first boolValue];
        XCTAssert(!valid);
    }];
    
    date = [[NSDate imy_today] dateByAddingDays:2];
    [[self.viewModel checkLastMenses:date] subscribeNext:^(RACTuple *tuple) {
        BOOL valid = [tuple.first boolValue];
        XCTAssert(!valid);
    }];
    
    date = [[NSDate imy_today] dateBySubtractingDays:100];
    [[self.viewModel checkLastMenses:date] subscribeNext:^(RACTuple *tuple) {
        BOOL valid = [tuple.first boolValue];
        XCTAssert(valid);
    }];
}

//计算预产期
- (void)testCaculateDueDate {
    [[self.viewModel caculateDueDateWithLastMensesDate:[NSDate imy_today] circleDay:28] subscribeNext:^(id x) {
        NSDate *date = x;
        XCTAssert([date isInFuture], @"计算预产期出错");
    }];
    
}

@end
