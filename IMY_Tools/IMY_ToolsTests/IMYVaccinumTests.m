//
//  IMYVaccinumTests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/6/12.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "IMYToolNetworking.h"
#import <XCTest/XCTest.h>
#import "IMYToolsVaccinumModel.h"
#import "IMYToolsVaccinumManager.h"
#import "IMYToolsVaccinumModel.h"

@interface IMYVaccinumTests : XCTestCase

@end

@implementation IMYVaccinumTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

/// 获取疫苗列表
- (void)testBabyVaccinumFullList {
    XCTestExpectation *exp = [self expectationWithDescription:@"TestVaccinumFullList"];
    [[IMYToolsVaccinumManager sharedManager] getVaccinumList:^(BOOL success, NSArray *data) {
        if (success && data.count > 0) {
            
        } else {
            XCTAssert(NO, @"testBabyVaccinumFullList fail");
        }
        [exp fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

/// 获取用户的疫苗列表
- (void)testBabyVaccinumUserList {
    XCTestExpectation *exp = [self expectationWithDescription:@"TestVaccinumUserList"];
    [[IMYToolsVaccinumManager sharedManager] getVaccinumUserList:^(BOOL success, NSArray *data) {
        if (success && data.count > 0) {
            
        } else {
            XCTAssert(NO, @"testBabyVaccinumUserList fail");
        }
        [exp fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

/// 获取疫苗详情
- (void)testBabyVaccinumDesc {
    XCTestExpectation *exp = [self expectationWithDescription:@"TestVaccinumDesc"];
    [[IMYToolsVaccinumManager sharedManager] getVaccinumDetailWithID:1 handler:^(BOOL success, IMYToolsVaccinumModel *model) {
        if (success && model) {
            
        } else {
            XCTAssert(NO, @"testBabyVaccinumDesc fail");
        }
        [exp fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

/// 更新用户数据
- (void)testBabyVAccinumUpdate {
    XCTestExpectation *exp = [self expectationWithDescription:@"TestVaccinumUpload"];
    //流感疫苗
    IMYToolsVaccinumModel *model = [[IMYToolsVaccinumModel alloc] init];
    model.vid = 20;
    model.is_mark = YES;
    model.vaccinate_time = 1482336000;// 2016-10-22
    
    [[IMYToolsVaccinumManager sharedManager] updateVaccinums:@[model] handler:^(BOOL success) {
        if (success) {
            
        } else {
            XCTAssert(NO, @"testBabyVaccinumUpload fail");
        }
        [exp fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

@end
