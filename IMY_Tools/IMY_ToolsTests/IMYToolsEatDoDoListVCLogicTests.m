//
//  IMYToolsEatDoDoListVCLogicTests.m
//  IMY_Tools
//
//  Created by hsm on 16/8/4.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolsEatDoDoListVC.h"
#import "IMYToolsDoListVM.h"

@interface IMYToolsEatDoDoListVCLogicTests : XCTestCase

@property (nonatomic, strong) IMYToolsEatDoDoListVC *doVC;

@end

@implementation IMYToolsEatDoDoListVCLogicTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
    
    self.doVC = [[IMYToolsEatDoDoListVC alloc] initWithTitle:@"家具生活" AndCid:23];
    [self.doVC beginAppearanceTransition:YES animated:NO];
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

- (void)testInitData {

    XCTestExpectation *exp = [self expectationWithDescription:@"IMYTools_eatDo_doListVC_logicTest"];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    
        NSArray *VMs = [self.doVC valueForKey:@"VMs"];
        
        XCTAssertNotNil(VMs, @"VMS must be not nil");
    
        for (NSInteger i=0; i<4; i++) {
            IMYToolsDoListVM *vm = VMs[i];
            
            NSInteger forbit = vm.foribit;
            NSDictionary *dic = [vm valueForKey:@"paramList"];
            NSString *cacheName = [vm valueForKey:@"cacheName"];

            
            XCTAssertTrue(forbit == i, @"forbit must be %zd",i);
            XCTAssertTrue([dic[@"title"] integerValue] == 0,@"title should be 0");
            XCTAssertTrue([dic[@"size"] integerValue] == 20,@"size should be 20");
            XCTAssertTrue([dic[@"size"] integerValue] == 20,@"size should be 20");
            XCTAssertTrue([dic[@"allow"] integerValue] == i,@"allow should be 0");
            
            NSString *realCacheName = [NSString stringWithFormat:@"ban_do_23_%zd",i];
            
            
            XCTAssertTrue([cacheName isEqualToString:realCacheName],@"cacheName should be ban_do_23_%zd",i);
        }
        
        [exp fulfill];
        
    });;
    
    [self waitForExpectationsWithTimeout:10 handler:^(NSError * _Nullable error) {
      
        if(error) {
            XCTAssert(NO,@"time out");
        }
        
    }];


}

@end
