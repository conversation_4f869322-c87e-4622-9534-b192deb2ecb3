//
//  IMYToolsEatDoNetSeriveTests.m
//  IMY_Tools
//
//  Created by hsm on 16/6/6.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolsInner.h"
#import "IMYToolsEatDoBanFoodModel.h"
#import "IMYToolsEatDoBanActModel.h"
#import "IMYToolsEatDoEatAndDoModel.h"
#import "IMYToolsEatDoBanFoodInfoModel.h"
#import "IMYToolsEatDoActInfoModel.h"


@interface IMYToolsEatDoNetSeriveTests : XCTestCase

@end

@implementation IMYToolsEatDoNetSeriveTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}


- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

#pragma -mark catagory

- (void)testEatCatagory{
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-eatCatagory"];
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc]init];
    NSString *path = @"taboo_category_list";
    
    [IMYToolNetworking  getWithHost:tools_seeyouyima_com
                               path:path
                         parameters:params successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
                             
                             NSArray *categories = [responseObject toModels:[IMYToolsEatDoEatAndDoModel class] forKey:@"category_list"];
                             
                             NSArray *recommonds = [responseObject toModels:[IMYToolsEatDoBanFoodModel class] forKey:@"recommend_list"];
                             
                             if ([categories isKindOfClass:[NSArray class]]
                                 && [recommonds isKindOfClass:[NSArray class]]) {
                                 
                             } else {
                                 XCTAssert(NO,@"应该返回分类数组和推荐阅读数组");
                             }
                             [exp fulfill];
                             
                         } failedBlock:^(id error) {
                             XCTAssert(NO,@"request fail:%@",error);
                             [exp fulfill];
                         }];
    
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
       
        if (error) {
            XCTAssert(NO,@"request time out:%@",error);
        }
    }];
    
}

- (void)testDoCatagory{
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-doCatagory"];
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc]init];
    NSString *path = @"behavior_category_list";
    
    [IMYToolNetworking  getWithHost:tools_seeyouyima_com
                               path:path
                         parameters:params successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
                             
                             NSArray *categories = [responseObject toModels:[IMYToolsEatDoEatAndDoModel class] forKey:@"category_list"];
                             
                             NSArray *recommonds = [responseObject toModels:[IMYToolsEatDoBanActModel class] forKey:@"recommend_list"];
                             
                             if ([categories isKindOfClass:[NSArray class]]
                                 && [recommonds isKindOfClass:[NSArray class]]) {
                                 
                             } else {
                                 XCTAssert(NO,@"应该返回分类数组和推荐阅读数组");
                             }
                             [exp fulfill];
                             
                         } failedBlock:^(id error) {
                             XCTAssert(NO,@"request fail:%@",error);
                             [exp fulfill];
                         }];
    
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        
        if (error) {
            XCTAssert(NO,@"request time out:%@",error);
        }
    }];
    
}

#pragma -mark list
- (void)testEatList {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-eatList"];
    
    NSMutableDictionary * paramList = [[NSMutableDictionary alloc]init];
    paramList[@"category_id"] = @(29); //水果
    paramList[@"crowd"] = @(0);
    paramList[@"matters"] = @(0);
    paramList[@"start"] = @(0);
    paramList[@"size"] = @(20);
    paramList[@"title"] = @(0);
    
    @weakify(self);
    [IMYToolNetworking postHost:tools_seeyouyima_com
                           path:@"taboo/search"
                     parameters:paramList
                   successBlock:^(IMYToolNetworkingOperation *operation, id responseObject, NSDictionary *info) {
        
        @strongify(self);
        NSArray * datalist = [responseObject toModels:[IMYToolsEatDoBanFoodModel class]];
                       if ([datalist isKindOfClass:[NSArray class]]) {
                           
                       } else {
                           XCTAssert(NO,@"应该返回分类 category_id= %@ 的数组",paramList[@"category_id"]);
                       }
                       [exp fulfill];

    } failedBlock:^(id error, NSDictionary *info) {
        XCTAssert(NO,@"request fail:%@",error);
        [exp fulfill];
    }];

    [self waitForExpectationsWithTimeout:60.0 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out:%@",error);
        }
    }];
}

- (void)testDoList {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-doList"];
    
    NSMutableDictionary * paramList = [[NSMutableDictionary alloc]init];
    paramList[@"category_id"] = @(2);//居家生活
    paramList[@"start"] = @(0);
    paramList[@"size"] = @(20);
    paramList[@"title"] = @(0);
    paramList[@"allow"] = @(0);
    
    @weakify(self);
    [IMYToolNetworking postHost:tools_seeyouyima_com
                           path:@"behavior_list"
                     parameters:paramList
                   successBlock:^(IMYToolNetworkingOperation *operation, id responseObject, NSDictionary *info) {
                       
                       @strongify(self);
                       NSArray * datalist = [responseObject toModels:[IMYToolsEatDoBanActModel class]];
                       if ([datalist isKindOfClass:[NSArray class]]) {
                           
                       } else {
                           XCTAssert(NO,@"应该返回分类 category_id= %@ 的数组",paramList[@"category_id"]);
                       }
                       [exp fulfill];
                   } failedBlock:^(id error, NSDictionary *info) {
                       XCTAssert(NO,@"request fail:%@",error);
                       [exp fulfill];
                   }];
    
    [self waitForExpectationsWithTimeout:60.0 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out:%@",error);
        }
    }];
}


#pragma -mark detail

- (void)testEatDetail {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-doList"];
    
    NSMutableDictionary * paramList = [[NSMutableDictionary alloc]init];
    paramList[@"id"] = @(411);//泥猴桃
    
    [IMYToolNetworking getWithHost:tools_seeyouyima_com
                              path:@"taboo/food"
                        parameters:paramList successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        
        IMYToolsEatDoBanFoodInfoModel *model = [operation.responseObject toModel:[IMYToolsEatDoBanFoodInfoModel class]];
        XCTAssertTrue([model isKindOfClass:[IMYToolsEatDoBanFoodInfoModel class]]);
        [exp fulfill];
                            
    } failedBlock:^(id error) {
        XCTAssert(NO,@"request fail:%@",error);
        [exp fulfill];

    }];
    
    [self waitForExpectationsWithTimeout:60.0 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out:%@",error);
        }
    }];
    
}

- (void)testDoDetail {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-doList"];
    
    NSMutableDictionary * paramList = [[NSMutableDictionary alloc]init];
    paramList[@"id"] = @(21);//KTV
    
    [IMYToolNetworking getWithHost:tools_seeyouyima_com
                              path:@"behavior_detail"
                        parameters:paramList
                      successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
                          IMYToolsEatDoActInfoModel *model = [responseObject toModel:[IMYToolsEatDoActInfoModel class]];
                          XCTAssertTrue(model.actId == 21);
                          [exp fulfill];
                          
                      } failedBlock:^(id error) {
                          XCTAssert(NO,@"request fail:%@",error);
                          [exp fulfill];
                      }];

    
    [self waitForExpectationsWithTimeout:60.0 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out:%@",error);
        }
    }];
    
}


#pragma -mark search
- (void)testEatSearch {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-doSearch"];
    
    NSMutableDictionary *paramList = [NSMutableDictionary dictionary];
    paramList[@"category_id"] = @(0);
    paramList[@"crowd"] = @(0);
    paramList[@"matters"] = @(0);
    paramList[@"size"] = @(20);
    paramList[@"start"] = @(0);
    paramList[@"title"] = @"咖啡";
    
    [IMYToolNetworking postHost:tools_seeyouyima_com path:@"taboo/search" parameters:paramList successBlock:^(IMYToolNetworkingOperation *operation, id responseObject, NSDictionary *info) {
        
        NSArray *datalist = [responseObject toModels:[IMYToolsEatDoBanActModel class]];
        XCTAssert(datalist.count > 0,@"至少有一条咖啡的搜索数据");
        [exp fulfill];
        
    } failedBlock:^(id error, NSDictionary *info) {
        
        XCTAssert(NO,@"request fail:%@",error);
        [exp fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out");
        }
    }];
    
}



- (void)testDoSearch {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-EatDo-doSearch"];
    
    NSMutableDictionary *paramList = [NSMutableDictionary dictionary];
    paramList[@"category_id"] = @(0);
    paramList[@"size"] = @(20);
    paramList[@"start"] = @(0);
    paramList[@"title"] = @"扫地";
    
    [IMYToolNetworking postHost:tools_seeyouyima_com path:@"behavior_list" parameters:paramList successBlock:^(IMYToolNetworkingOperation *operation, id responseObject, NSDictionary *info) {
        
        NSArray *datalist = [responseObject toModels:[IMYToolsEatDoBanActModel class]];
        XCTAssert(datalist.count > 0,@"至少有一条扫地的搜索数据");
        [exp fulfill];
        
    } failedBlock:^(id error, NSDictionary *info) {
        
        XCTAssert(NO,@"request fail:%@",error);
        [exp fulfill];
    }];

    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out");
        }
    }];
    
}



@end
