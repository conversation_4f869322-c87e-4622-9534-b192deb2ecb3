//
//  IMYToolsContractionTest.m
//  IMY_Tools
//
//  Created by hsm on 16/12/19.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolsContractionsDataManager.h"
#import "IMYToolsInner.h"
#import "IMYToolsContractionsCountingVC.h"

@interface IMYToolsContractionTest : XCTestCase

@property (nonatomic, strong) NSMutableArray *models;

@end

@implementation IMYToolsContractionTest

- (void)setUp {
    [super setUp];
    self.models = [NSMutableArray array];
}

- (void)tearDown {
    [super tearDown];
    
    //清空数据。
    for(IMYToolsContractionDataModel *model in self.models) {
        [model deleteToDB];
    }
    
}

- (void)testPerformanceExample {
    [self measureBlock:^{
    }];
}
//测试5分钟~10分钟的提醒
- (void)test5_10MinTip {
    
    //5min
    IMYToolsContractionsDataManager *manager = [IMYToolsContractionsDataManager sharedManager];
    IMYToolsContractionDataModel *model = [[IMYToolsContractionDataModel alloc] init];
    model.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-5 * 60];
    [manager addRecord:model];
    
    
    IMYToolsContractionsCountingVC *vc = [[IMYToolsContractionsCountingVC alloc] init];
    [vc beginAppearanceTransition:YES animated:NO];
    
    
    [vc performSelectorOnMainThread:@selector(onBeginTiming) withObject:nil waitUntilDone:YES];
    UILabel *label = [vc valueForKey:@"tipLabel"];
    XCTAssert([label.text isEqualToString:@"距离上次宫缩不到十分钟，建议您咨询一下您的妇产医生哦~"], @"距离上次5分钟，提醒错误");
    
    [manager removeRecord:model];
    
    
    
    
    //10min
    model.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-9.5 * 60];
    [manager addRecord:model];
    
    
    
    IMYToolsContractionsCountingVC *vc1 = [[IMYToolsContractionsCountingVC alloc] init];
    [vc1 beginAppearanceTransition:YES animated:NO];
    
    
    [vc1 performSelectorOnMainThread:@selector(onBeginTiming) withObject:nil waitUntilDone:YES];
    XCTAssert([label.text isEqualToString:@"距离上次宫缩不到十分钟，建议您咨询一下您的妇产医生哦~"], @"距离上次5分钟，提醒错误");
    
    [manager removeRecord:model];
    
}

//测试10分钟~提醒
- (void)test10_MinTip {
    
    IMYToolsContractionsDataManager *manager = [IMYToolsContractionsDataManager sharedManager];
    IMYToolsContractionDataModel *model = [[IMYToolsContractionDataModel alloc] init];
    model.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-10.1 * 60];
    [manager addRecord:model];
    
    
    IMYToolsContractionsCountingVC *vc = [[IMYToolsContractionsCountingVC alloc] init];
    [vc beginAppearanceTransition:YES animated:NO];
    
    [vc performSelectorOnMainThread:@selector(onBeginTiming) withObject:nil waitUntilDone:YES];
    UILabel *label = [vc valueForKey:@"tipLabel"];
    XCTAssert([label.text isEqualToString:@"疼痛剧烈时，请按以下方法调整呼吸：\n第一步，平卧，闭目，以鼻深吸气\n第二步，以口呼气，放松腹部\n第三步，以鼻吸气后，屏气10秒左右，然后以口长呼气。"], @"距离上次10分钟，提醒错误");
    
    [manager removeRecord:model];
    
}


//测试添加数据，数据排列和计算间隔时间是否正确
- (void)testAddRecord {
    IMYToolsContractionsDataManager *manager = [IMYToolsContractionsDataManager sharedManager];
    
    IMYToolsContractionDataModel *model0 = [[IMYToolsContractionDataModel alloc] init];
    model0.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-600];
    model0.duration = 100;
    [manager addRecord:model0];
    [self.models addObject:model0];
    
    
    IMYToolsContractionDataModel *model1 = [[IMYToolsContractionDataModel alloc] init];
    model1.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-400];
    model1.duration = 120;
    [manager addRecord:model1];
    [self.models addObject:model1];
    
    
    
    IMYToolsContractionDataModel *model2 = [[IMYToolsContractionDataModel alloc] init];
    model2.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-100];
    model2.duration = 80;
    [manager addRecord:model2];
    [self.models addObject:model2];
    
    
    IMYToolsContractionDataModel *dbModel3 = manager.linearedAllData[manager.linearedAllData.count - 3];
    IMYToolsContractionDataModel *dbModel2 = manager.linearedAllData[manager.linearedAllData.count - 2];
    IMYToolsContractionDataModel *dbModel1 = manager.linearedAllData[manager.linearedAllData.count - 1];
    
    XCTAssert(dbModel1.interval < 0, @"must: dbModel1.interval<0");
    XCTAssert(dbModel2.interval == 100, @"must: dbModel2.interval == 100");
    XCTAssert(dbModel3.interval == 180, @"must: dbModel1.interval == 180");
    
    [manager removeRecord:model0];
    [manager removeRecord:model1];
    [manager removeRecord:model2];
    
}

//测试中间插入数据，数据的排列和时间间隔是否正确
- (void)testInsertRecord {
    
    IMYToolsContractionsDataManager *manager = [IMYToolsContractionsDataManager sharedManager];
    
    IMYToolsContractionDataModel *model1 = [[IMYToolsContractionDataModel alloc] init];
    model1.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-600];
    model1.duration = 100;
    [manager addRecord:model1];
    [self.models addObject:model1];
    
    
    IMYToolsContractionDataModel *model2 = [[IMYToolsContractionDataModel alloc] init];
    model2.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-100];
    model2.duration = 80;
    [manager addRecord:model2];
    [self.models addObject:model2];
    
    
    
    IMYToolsContractionDataModel *model0 = [[IMYToolsContractionDataModel alloc] init];
    model0.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-300];
    model0.duration = 150;
    [manager addRecord:model0];
    [self.models addObject:model0];
    
    
    
    IMYToolsContractionDataModel *dbModel3 = manager.linearedAllData[manager.linearedAllData.count - 3];
    IMYToolsContractionDataModel *dbModel2 = manager.linearedAllData[manager.linearedAllData.count - 2];
    IMYToolsContractionDataModel *dbModel1 = manager.linearedAllData[manager.linearedAllData.count - 1];
    
    XCTAssert(dbModel1.interval < 0, @"must: dbModel1.interval<0");
    XCTAssert(dbModel2.interval == 200, @"must: dbModel2.interval == 200");
    //差一秒
    XCTAssert(dbModel3.interval == 49, @"must: dbModel1.interval == 49");
    
    [manager removeRecord:model0];
    [manager removeRecord:model1];
    [manager removeRecord:model2];
    
}

//测试中间删除数据，数据的排列和时间间隔是否正确
- (void)testRemoveRecord {
    IMYToolsContractionsDataManager *manager = [IMYToolsContractionsDataManager sharedManager];
    
    IMYToolsContractionDataModel *model0 = [[IMYToolsContractionDataModel alloc] init];
    model0.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-600];
    model0.duration = 100;
    [manager addRecord:model0];
    [self.models addObject:model0];
    
    
    
    IMYToolsContractionDataModel *model1 = [[IMYToolsContractionDataModel alloc] init];
    model1.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-400];
    model1.duration = 120;
    [manager addRecord:model1];
    [self.models addObject:model1];
    
    
    
    IMYToolsContractionDataModel *model2 = [[IMYToolsContractionDataModel alloc] init];
    model2.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-100];
    model2.duration = 80;
    [manager addRecord:model2];
    [self.models addObject:model2];
    
    
    
    [manager removeRecord:model1];
    
    
    IMYToolsContractionDataModel *dbModel2 = manager.linearedAllData[manager.linearedAllData.count - 2];
    IMYToolsContractionDataModel *dbModel1 = manager.linearedAllData[manager.linearedAllData.count - 1];
    
    XCTAssert(dbModel1.interval < 0, @"must: dbModel1.interval<0");
    XCTAssert(dbModel2.interval == 400, @"must: dbModel2.interval == 100");
    
    [manager removeRecord:model0];
    [manager removeRecord:model2];
    
}

//测试最早一个删除数据，数据的排列和时间间隔是否正确
- (void)testRemoveTopRecord {
    
    IMYToolsContractionsDataManager *manager = [IMYToolsContractionsDataManager sharedManager];
    
    IMYToolsContractionDataModel *model0 = [[IMYToolsContractionDataModel alloc] init];
    model0.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-600];
    model0.duration = 100;
    [manager addRecord:model0];
    [self.models addObject:model0];
    
    
    
    IMYToolsContractionDataModel *model1 = [[IMYToolsContractionDataModel alloc] init];
    model1.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-400];
    model1.duration = 120;
    [manager addRecord:model1];
    [self.models addObject:model1];
    
    
    
    IMYToolsContractionDataModel *model2 = [[IMYToolsContractionDataModel alloc] init];
    model2.beginTime = [[NSDate alloc] initWithTimeIntervalSinceNow:-100];
    model2.duration = 80;
    [manager addRecord:model2];
    [self.models addObject:model2];
    
    
    [manager removeRecord:model0];
    
    
    
    IMYToolsContractionDataModel *dbModel2 = manager.linearedAllData[manager.linearedAllData.count - 2];
    IMYToolsContractionDataModel *dbModel1 = manager.linearedAllData[manager.linearedAllData.count - 1];
    
    XCTAssert(dbModel1.interval < 0, @"must: dbModel1.interval<0");
    XCTAssert(dbModel2.interval == 180, @"must: dbModel2.interval == 100");
    
    [manager removeRecord:model1];
    [manager removeRecord:model2];
    
}


@end
