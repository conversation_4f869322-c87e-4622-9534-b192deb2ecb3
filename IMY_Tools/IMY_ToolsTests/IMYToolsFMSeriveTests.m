//
//  IMYToolsFMSeriveTests.m
//  IMY_Tools
//
//  Created by hsm on 16/6/6.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "IMYToolsFMSerive.h"
#import "IMYToolsFMCountDataManager.h"
#import "IMYToolsInner.h"

@interface IMYToolsFMSeriveTests : XCTestCase

@property (nonatomic, strong) NSMutableArray *addModels;    //测试中生成的数据
@property (nonatomic, strong) NSMutableArray *deleteIDs;
@property (nonatomic, assign) NSInteger      dateNumber;

@end

@implementation IMYToolsFMSeriveTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
    
    self.addModels = [NSMutableArray array];
    self.deleteIDs = [NSMutableArray array];
    //构造测试数据
    for (int i=0; i<2; i++) {
        IMYToolsFMCountModel *model = [[IMYToolsFMCountModel alloc]init];
        model.userId = [IMYPublicAppHelper shareAppHelper].userid;
        model.totalFMTimes = 20 + 3 * i;
        model.validFMTimes = 1 + 3 * i;
        model.beginTime = [NSDate dateWithTimeIntervalSinceNow: -8000 + (4000*i)];
        model.lastFMTime = [NSDate dateWithTimeIntervalSince1970:0];
        [self.addModels addObject:model];
    }
    
    //日期
    NSMutableString *datesString = [NSMutableString string];
    [datesString appendString:[[NSDate date] tools_getOnlyDateString]];
    [datesString replaceOccurrencesOfString:@"-" withString:@"" options:NSCaseInsensitiveSearch range:NSMakeRange(0, datesString.length)];
    
    self.dateNumber = [datesString integerValue];
    
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

#pragma -mark 网络接口返回数据格式测试
- (void)test21AddData {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-taidong-add"];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    NSMutableArray *resultArray = [NSMutableArray array];
    
    for (IMYToolsFMCountModel *model in self.addModels) {
        
        NSDictionary *dict = @{@"start_time":@(model.beginTime.timeIntervalSince1970),
                               @"fetal_times":@(model.validFMTimes),
                               @"click_times":@(model.totalFMTimes)};
        [resultArray addObject:dict];
    }
    
    [params setObject:resultArray forKey:@"add"];
    
    [IMYToolNetworking postWithHost:tools_seeyouyima_com
                               path:@"fetal_current"
                         parameters:params
              requestSerializerType:IMYToolNetworkingSerializerTypeJSON
             responseSerializerType:IMYToolNetworkingSerializerTypeData
                       successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
                           //此接口，成功不返回数据，
                           [exp fulfill];
                           
                       } failedBlock:^(id error) {
                           XCTAssert(NO,@"request fail:%@",error);
                           [exp fulfill];
                       }];
    
    [self waitForExpectationsWithTimeout:5 handler:^(NSError * _Nullable error) {
        
        if (error) {
            XCTAssert(NO,@"request time out：%@",error);
        }
    }];
}

- (void)test23FetchAllDayData {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-taidong-loadAllDatas"];
    
    [IMYToolNetworking getWithHost:tools_seeyouyima_com
                              path:@"fetal"
                        parameters:@{@"start":@0}
             requestSerializerType:IMYToolNetworkingSerializerTypeJSON responseSerializerType:IMYToolNetworkingSerializerTypeJSON
                      successBlock:^(IMYToolNetworkingOperation *operation, id responseObject){
                          
                          //此接口应该返回 IMYToolsFMCountDayModel的数组，并且只少有self.dateNumber天的数据。
                          
                          //解析IMYToolsFMCountDayModel
                          NSArray *models = [responseObject toModels:[IMYToolsFMCountDayModel class]];
                          if(models && models.count){
                              
                              //解析内部IMYToolsFMCountModel
                              BOOL has = NO;
                              for (IMYToolsFMCountDayModel *model in models) {
                                  
                                  if(model.dateNumber == self.dateNumber) {
                                      has = YES;
                                      break;
                                  }
                              }
                              
                              XCTAssert(has,@"至少要有%ld天的数据",self.dateNumber);
                          }
                          
                          [exp fulfill];
                          
                      } failedBlock:^(id error) {
                          XCTAssert(NO,@"request fail:%@",error);
                          [exp fulfill];
                      }];
    
    [self waitForExpectationsWithTimeout:5 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(NO,@"request time out");
        }
    }];
}


- (void)test24DeleteDatas {
    
    XCTestExpectation *exp = [self expectationWithDescription:@"tools-taidong-delete"];
    
    //获取delete id
    @weakify(self);
    [IMYToolNetworking getWithHost:tools_seeyouyima_com
                              path:@"fetal"
                        parameters:@{@"start":@0}
             requestSerializerType:IMYToolNetworkingSerializerTypeJSON responseSerializerType:IMYToolNetworkingSerializerTypeJSON
                      successBlock:^(IMYToolNetworkingOperation *operation, id responseObject){
                          @strongify(self);
                          //此接口应该返回 IMYToolsFMCountDayModel的数组，并且只少有self.dateNumber天的数据。
                          NSArray *models = [responseObject toModels:[IMYToolsFMCountDayModel class]];

                          if(models && models.count){
                              
                              //解析内部IMYToolsFMCountModel
                              for (IMYToolsFMCountDayModel *model in models) {
                                  
                                  if(model.dateNumber == self.dateNumber) {
                                      
                                      for (id obj in model.list) {
                                          
                                          IMYToolsFMCountModel *model = [obj toModel:[IMYToolsFMCountModel class]];
                                          [self.deleteIDs addObject:@(model.modelID)];
                                      }
                                      
                                      break;

                                  }
                              }
                              
                              
                              NSMutableDictionary *params = [NSMutableDictionary dictionary];
                              [params setObject:self.deleteIDs forKey:@"delete"];
                              
                              [IMYToolNetworking postWithHost:tools_seeyouyima_com
                                                         path:@"fetal_current"
                                                   parameters:params
                                        requestSerializerType:IMYToolNetworkingSerializerTypeJSON
                                       responseSerializerType:IMYToolNetworkingSerializerTypeData
                                                 successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
                                                     //此接口，成功不返回数据，
                                                     [exp fulfill];
                                                 } failedBlock:^(id error) {
                                                     XCTAssert(NO,@"request fail:%@",error);
                                                     [exp fulfill];
                                                 }];
                              
                          }
                          
                      } failedBlock:^(id error) {
                          XCTAssert(NO,@"request fail:%@",error);
                          [exp fulfill];
                      }];
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        
        if (error) {
            XCTAssert(NO,@"request time out：%@",error);
        }
    }];
    
}




@end
