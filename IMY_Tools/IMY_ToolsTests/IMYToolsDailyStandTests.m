//
//  IMYToolsDailyStandTests.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/6/8.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <XCTest/XCTest.h>
#import <IMY_ViewKit.h>
#import "IMYToolsDailyStandService.h"
#import "IMYToolNetworkingOperation.h"
#import "IMYToolsDailyStandModel.h"

@interface IMYToolsDailyStandTests : XCTestCase
@property (nonatomic, strong) IMYToolsDailyStandService *service;
@end

@implementation IMYToolsDailyStandTests

- (void)setUp {
    [super setUp];
    self.service = [IMYToolsDailyStandService new];
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

- (void)testGetVoteInfo {
    XCTestExpectation *expectation = [self expectationWithDescription:@"GetVoteInfoExpectation"];
    
    [[self.service reqDailyStandDataSignal] subscribeNext:^(id x) {
        IMYToolNetworkingOperation *operation = [IMYToolNetworkingOperation operationWithResponse:x];
        NSData *responseData = operation.responseObject;
        //每日投票信息
        IMYToolsDailyStandInfoModel *evInfoModel = [responseData toModel:[IMYToolsDailyStandInfoModel class] forKey:@"ev_info"];
        //投票项
        NSArray *optionsArray = [responseData toModels:[IMYToolsDailyStandOptionsModel class] forKey:@"ev_options"];
        //投票完成后的链接信息
        IMYToolsDailyStandLinkModel *linkModel = [responseData toModel:[IMYToolsDailyStandLinkModel class] forKey:@"ev_link"];
        if (!evInfoModel || optionsArray.count == 0 || !linkModel) {
            XCTFail(@"投票信息不完整");
        }
        [expectation fulfill];
    } error:^(NSError *error) {
        IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *message = imy_isBlankString(model.message) ? IMYString(@"咦？网络不见了，请检查网络连接") : model.message;
        NSLog(@"error:%@", message);
        XCTFail(@"API Get error");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

- (void)testPostVote {
    XCTestExpectation *expectation = [self expectationWithDescription:@"PostVoteInfoExpectation"];
    
    NSDictionary *params = @{@"ev_id": @(462), @"evo_id": @(1546)};
    [[self.service reqDailyStandSelectSignal:params] subscribeNext:^(id x) {
        IMYToolNetworkingOperation *operation = [IMYToolNetworkingOperation operationWithResponse:x];
        NSData *responseData = operation.responseObject;
        id result = [responseData imy_jsonObject];
        if ([result isKindOfClass:[NSDictionary class]] && [result[@"result"] boolValue]) {

        } else {
            XCTFail(@"投票失败");
        }
        [expectation fulfill];
    } error:^(NSError *error) {
        IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *message = imy_isBlankString(model.message) ? IMYString(@"咦？网络不见了，请检查网络连接") : model.message;
        NSLog(@"error:%@", message);
        XCTFail(@"API Get error");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:30 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTAssert(@"time out...");
        }
    }];
}

///每日测一测:怀孕
- (void)testEverydayVoteHuaiyun {
    XCTestExpectation *expectation = [self expectationWithDescription:@"EverydayVoteHuaiyun"];
    
    NSDictionary *params = @{@"mode" : @(1), @"day_of_gestation" : @(247)};
    [[self.service getDailyStandDataSignal:params] subscribeNext:^(id x) {
        IMYToolNetworkingOperation *operation = [IMYToolNetworkingOperation operationWithResponse:x];
        NSData *responseData = operation.responseObject;
        //每日投票信息
        IMYToolsDailyStandInfoModel *evInfoModel = [responseData toModel:[IMYToolsDailyStandInfoModel class] forKey:@"ev_info"];
        //投票项
        NSArray *optionsArray = [responseData toModels:[IMYToolsDailyStandOptionsModel class] forKey:@"ev_options"];
        //投票完成后的链接信息
        IMYToolsDailyStandLinkModel *linkModel = [responseData toModel:[IMYToolsDailyStandLinkModel class] forKey:@"ev_link"];
        if (!evInfoModel || optionsArray.count == 0 || !linkModel) {
            XCTFail(@"投票信息不完整");
        }
        [expectation fulfill];
    } error:^(NSError *error) {
        IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *message = imy_isBlankString(model.message) ? IMYString(@"咦？网络不见了，请检查网络连接") : model.message;
        NSLog(@"error:%@", message);
        XCTFail(@"API Get error");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTFail(@"timeout");
        }
    }];
}

///每日测一测:备孕
- (void)testEverydayVoteBeiyun {
    XCTestExpectation *expectation = [self expectationWithDescription:@"EverydayVoteBeiyun"];
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc]init];
    [params setObject:@(2) forKey:@"mode"];
    [params setObject:@(9) forKey:@"phase"];          //当前时期
    [params setObject:@(4) forKey:@"day_of_phase"];   //当前时期来了多少天
    
    [[self.service getDailyStandDataSignal:params] subscribeNext:^(id x) {
        IMYToolNetworkingOperation *operation = [IMYToolNetworkingOperation operationWithResponse:x];
        NSData *responseData = operation.responseObject;
        //每日投票信息
        IMYToolsDailyStandInfoModel *evInfoModel = [responseData toModel:[IMYToolsDailyStandInfoModel class] forKey:@"ev_info"];
        //投票项
        NSArray *optionsArray = [responseData toModels:[IMYToolsDailyStandOptionsModel class] forKey:@"ev_options"];
        //投票完成后的链接信息
        IMYToolsDailyStandLinkModel *linkModel = [responseData toModel:[IMYToolsDailyStandLinkModel class] forKey:@"ev_link"];
        if (!evInfoModel || optionsArray.count == 0 || !linkModel) {
            XCTFail(@"投票信息不完整");
        }
        [expectation fulfill];
    } error:^(NSError *error) {
        IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *message = imy_isBlankString(model.message) ? IMYString(@"咦？网络不见了，请检查网络连接") : model.message;
        NSLog(@"error:%@", message);
        XCTFail(@"API Get error");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTFail(@"timeout");
        }
    }];
}

///每日测一测:辣妈
- (void)testEverydayVoteLama {
    XCTestExpectation *expectation = [self expectationWithDescription:@"EverydayVoteLama"];
    
    NSDictionary *params = @{@"mode" : @(3), @"day_of_babybirth": @(200)};
    [[self.service getDailyStandDataSignal:params] subscribeNext:^(id x) {
        IMYToolNetworkingOperation *operation = [IMYToolNetworkingOperation operationWithResponse:x];
        NSData *responseData = operation.responseObject;
        //每日投票信息
        IMYToolsDailyStandInfoModel *evInfoModel = [responseData toModel:[IMYToolsDailyStandInfoModel class] forKey:@"ev_info"];
        //投票项
        NSArray *optionsArray = [responseData toModels:[IMYToolsDailyStandOptionsModel class] forKey:@"ev_options"];
        //投票完成后的链接信息
        IMYToolsDailyStandLinkModel *linkModel = [responseData toModel:[IMYToolsDailyStandLinkModel class] forKey:@"ev_link"];
        if (!evInfoModel || optionsArray.count == 0 || !linkModel) {
            XCTFail(@"投票信息不完整");
        }
        [expectation fulfill];
    } error:^(NSError *error) {
        IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
        NSString *message = imy_isBlankString(model.message) ? IMYString(@"咦？网络不见了，请检查网络连接") : model.message;
        NSLog(@"error:%@", message);
        XCTFail(@"API Get error");
        [expectation fulfill];
    }];
    
    [self waitForExpectationsWithTimeout:60 handler:^(NSError * _Nullable error) {
        if (error) {
            XCTFail(@"timeout");
        }
    }];
}

@end
