# MARK: converted automatically by spec.py. @hgy

Pod::Spec.new do |s|
	s.name = 'IMYTools'
	s.version = '8.95.012'
	s.description = 'IMYTools'
	s.license = 'MIT'
	s.summary = 'IMYTools'
	s.homepage = 'http://git.meiyou.im/iOS/IMY_Tools'
	s.authors = { 'xyz' => '<EMAIL>' }
	s.source = { :git => '*********************:iOS/IMY_Tools.git', :branch => 'release-jingqi-8.95.0' }
	s.requires_arc = true
	s.ios.deployment_target = '11.0'
  
	s.vendored_libraries = 'IMY_Tools/Source/**/*.a'
	s.resources = 'IMY_Tools/Source/**/*.{json,png,jpg,gif,xib,TTF,db,txt,plist}','IMY_Tools/Bundles/*.{bundle,xcassets}','IMY_Tools/Bundles/**/*.pag'
	s.source_files = 'IMY_Tools/Source/**/*.{h,m,c,mm,cpp}'

	s.dependency 'IMYAdvertisement'
	s.dependency 'IMYBaseKit'
	s.dependency 'IMYVideoPlayer'
	s.dependency 'StandardPaths','1.6.6'
	s.dependency 'IMYMWPhotoBrowser'
	s.dependency 'IMYYQBasicServices'
	s.dependency 'BabyBluetooth'
	s.dependency 'IMYNCNNFrameworks'
  s.dependency 'libpag-enterprise'
  s.dependency 'IMYSwift'
end


