//
//  IMYToolsPaginatorView.h
//  IMYToolsPaginator
//
//  Created by <PERSON> on 9/21/11.
//  Copyright (c) 2011 Synthetic. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

typedef enum {
	IMYToolsPageViewAnimationNone,
	IMYToolsPageViewAnimationTop,
	IMYToolsPageViewAnimationBottom
} IMYToolsPageViewAnimation;

typedef enum {
	IMYToolsPageViewPaginationDirectionHorizontal,
	IMYToolsPageViewPaginationDirectionVertical
} IMYToolsPageViewPaginationDirection;

@protocol IMYToolsPaginatorViewDataSource;
@protocol IMYToolsPaginatorViewDelegate;
@class IMYToolsPageView;
@class IMYToolsPageControl;

/**
 This class manages a paging UIScrollView and a UIPageControl.
 
 Use the `currentPage` to get the current page. You **must** supply a `dataSource` with all of the required methods.
 */
@interface IMYToolsPaginatorView : UIView

// Configuring
#if defined(__IPHONE_5_0) && __IPHONE_OS_VERSION_MIN_REQUIRED >= __IPHONE_5_0
@property (nonatomic, weak) id<IMYToolsPaginatorViewDataSource> dataSource;
@property (nonatomic, weak) id<IMYToolsPaginatorViewDelegate> delegate;
#else
@property (nonatomic, unsafe_unretained) id<IMYToolsPaginatorViewDataSource> dataSource;
@property (nonatomic, unsafe_unretained) id<IMYToolsPaginatorViewDelegate> delegate;
#endif

// UI
@property (nonatomic, strong, readonly) UIScrollView *scrollView;
@property (nonatomic, strong, readonly) IMYToolsPageControl *pageControl;

@property (nonatomic, assign) NSInteger currentPageIndex;
@property (nonatomic, assign, readonly) NSInteger numberOfPages;
@property (nonatomic, assign) CGFloat pageGapWidth;
@property (nonatomic, assign) NSInteger numberOfPagesToPreload;
@property (nonatomic, assign) CGRect swipeableRect;
@property (nonatomic, assign) IMYToolsPageViewPaginationDirection paginationDirection;

- (void)reloadData;
- (void)reloadDataRemovingCurrentPage:(BOOL)removeCurrentPage;
- (void)setCurrentPageIndex:(NSInteger)targetPage animated:(BOOL)animated;
- (CGRect)frameForPageAtIndex:(NSInteger)page;
- (IMYToolsPageView *)pageForIndex:(NSInteger)page;
- (IMYToolsPageView *)dequeueReusablePageWithIdentifier:(NSString *)identifier;
- (IMYToolsPageView *)currentPage;

@end


@protocol IMYToolsPaginatorViewDataSource <NSObject>

@required

- (NSInteger)numberOfPagesForPaginatorView:(IMYToolsPaginatorView *)paginatorView;
- (IMYToolsPageView *)paginatorView:(IMYToolsPaginatorView *)paginatorView viewForPageAtIndex:(NSInteger)pageIndex;

@end


@protocol IMYToolsPaginatorViewDelegate <NSObject>

@optional

- (void)paginatorViewDidScroll:(IMYToolsPaginatorView *)paginatorView;
- (void)paginatorViewDidBeginPaging:(IMYToolsPaginatorView *)paginatorView;
- (void)paginatorView:(IMYToolsPaginatorView *)paginatorView willDisplayView:(UIView *)view atIndex:(NSInteger)pageIndex;
- (void)paginatorView:(IMYToolsPaginatorView *)paginatorView didScrollToPageAtIndex:(NSInteger)pageIndex;

@end
