//
//  IMYToolsPageView.m
//  IMYToolsPaginator
//
//  Created by <PERSON> on 3/8/12.
//  Copyright (c) 2012 Synthetic. All rights reserved.
//

#import "IMYToolsPageView.h"

@implementation IMYToolsPageView

@synthesize reuseIdentifier = _reuseIdentifier;

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.reuseIdentifier = NSStringFromClass([self class]);
    }
    return self;
}

- (id)initWithCoder:(NSCoder *)aDecoder {
	if ((self = [super initWithCoder:aDecoder])) {
		// If you are using a nib for your page views, the reuse identifier is set to its class name (though this isn't the best, it is relatively simple and only applies if using nibs)
		self.reuseIdentifier = NSStringFromClass([self class]);
	}
	return self;
}

- (id)initWithReuseIdentifier:(NSString *)reuseIdentifier {
	if ((self = [super initWithFrame:CGRectZero])) {
		self.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
		self.reuseIdentifier = reuseIdentifier;
	}
	return self;
}


- (void)prepareForReuse {
	// Subclasses may override this
}

@end
