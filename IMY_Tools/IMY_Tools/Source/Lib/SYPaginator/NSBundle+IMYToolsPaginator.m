//
//  NSBundle+IMYToolsPaginator.m
//  IMYToolsPaginator
//
//  Created by <PERSON> on 3/20/12.
//  Copyright (c) 2012 Synthetic. All rights reserved.
//

#import "NSBundle+IMYToolsPaginator.h"

@implementation NSBundle (IMYToolsPaginator)

+ (NSBundle *)paginatorBundle {
	static NSBundle *paginatorBundle = nil;
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		NSString *bundlePath = [[[NSBundle mainBundle] resourcePath] stringByAppendingPathComponent:@"IMYToolsPaginatorResources.bundle"];
		paginatorBundle = [[NSBundle alloc] initWithPath:bundlePath];
	});
	return paginatorBundle;
}

@end
