
//
//  IMYToolsCoolViewController.m
//  IMYYQHome
//
//  Created by why on 2017/10/31.
//  Copyright © 2017年 Linggan. All rights reserved.
//

#import "IMYToolsCoolViewController.h"
#import <Masonry/Masonry.h>
#import <IMYBaseKit/IMYVKCoolViewController.h>
#import <IMYBaseKit/UIView+IMYViewKit.h>

@interface IMYToolsCoolViewController () <UIScrollViewDelegate>

@property (nonatomic, strong) UIImageView *navBar;
@property (nonatomic, strong) UILabel *navBarTitle;
@property (nonatomic, strong) UIImageView *navBarLine;

@property (nonatomic, strong) UIButton *navLeftButton;
@property (nonatomic, strong) UIButton *navRightButton;

@property (nonatomic, strong) UIImageView *navLeftButtonDot;
@property (nonatomic, strong) UIImageView *navRightButtonDot;

@property(weak, nonatomic) UINavigationController *holdNavigationController;
@property(weak, nonatomic) UIViewController *beforeVC;

@end

@implementation IMYToolsCoolViewController


- (void)setupNavigationBarByScrollView:(UIScrollView *)scrollView {
    if (IOS7) {
        
        self.navigationBarHidden = YES;
        
        self.maxOffsetForNavAlpha = 200;
        
        self.navBar = [[UIImageView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        [self.view addSubview:self.navBar];
        
        ///导航栏分割线
        UIImageView *line = [UIImageView new];
        [line imy_setBackgroundColorForKey:kCK_Black_J];
        line.alpha = 0;
        [self.navBar addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(self.navBar);
            make.height.mas_equalTo(0.5);
        }];
        self.navBarLine = line;
        @weakify(self);
        [[[RACObserve(self.navBar, alpha) takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            self.navBarLine.alpha = self.navBar.alpha;
        }];
        
        self.navBarTitle = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH-80*2, SCREEN_NAVIGATIONBAR_HEIGHT)];
        self.navBarTitle.imy_centerX = self.view.imy_centerX;
        self.navBarTitle.font = [UIFont boldSystemFontOfSize:20];
        [self.navBarTitle imy_setTextColorForKey:kIMYCoolNavTitleColorKey];
        self.navBarTitle.backgroundColor = [UIColor clearColor];
        self.navBarTitle.textAlignment = NSTextAlignmentCenter;
        
        ///监听 self.title
        RAC(self, navBarTitle.text) = RACObserve(self, navigationItem.title);
        [self.view addSubview:self.navBarTitle];
        
        if ([self.navigationController.viewControllers count] > 1) {
            self.navLeftButtonDot = [[UIImageView alloc] initWithFrame:CGRectMake(8, 4, 36, 36)];
            [self.view addSubview:_navLeftButtonDot];
            
            self.navLeftButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 80, SCREEN_NAVIGATIONBAR_HEIGHT)];
            self.navLeftButton.imageEdgeInsets = UIEdgeInsetsMake(0, -14, 0, 14);
            self.navLeftButton.titleLabel.font = [UIFont systemFontOfSize:16];
            self.navLeftButton.titleLabel.numberOfLines = 1;
            self.navLeftButton.titleLabel.lineBreakMode = NSLineBreakByClipping;
            [self.navLeftButton addTarget:self
                                   action:@selector(imy_topLeftButtonTouchupInside)
                         forControlEvents:UIControlEventTouchUpInside];
            [self.view addSubview:self.navLeftButton];
            RAC(self, navLeftButtonDot.center) = RACObserve(self, navLeftButton.center);
        }
        
        self.navRightButtonDot =
        [[UIImageView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 36 - 4, 4, 36, 36)];
        [self.view addSubview:_navRightButtonDot];
        
        self.navRightButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 80, SCREEN_NAVIGATIONBAR_HEIGHT)];
        self.navRightButton.titleLabel.font = [UIFont systemFontOfSize:16];
        self.navRightButton.titleLabel.numberOfLines = 1;
        self.navRightButton.imageEdgeInsets = UIEdgeInsetsMake(0, 14, 0, -14);
        self.navRightButton.titleLabel.lineBreakMode = NSLineBreakByClipping;
        [self.view addSubview:_navRightButton];
        
        _navRightButton.imy_right = SCREEN_WIDTH;
        _navRightButtonDot.hidden = _navRightButton.hidden = YES;
        RAC(self, navRightButtonDot.center) = RACObserve(self, navRightButton.center);
        
        self.coolScrollView = scrollView;
    }
}

- (void)imy_themeChanged {
    [self.navLeftButton imy_setImage:@"all_topback.png"
                        state:UIControlStateNormal];
    
    UIImage *dotImage = [UIImage imy_imageForKey:@"top_b2c_floatbg.png"];
    [self.navLeftButtonDot imy_setImage:dotImage];
    [self.navRightButtonDot imy_setImage:dotImage];
    
    if (kIMYCoolNavBarImageKey) {
        self.navBar.backgroundColor = [UIColor clearColor];
        self.navBar.image = [UIImage imy_navigationBarImageForKey:kIMYCoolNavBarImageKey];
    } else if (kIMYCoolNavBarColorKey) {
        self.navBar.image = nil;
        [self.navBar imy_setBackgroundColorForKey:kIMYCoolNavBarColorKey];
    }else {
        self.navBar.image = nil;
        [self.navBar imy_setBackgroundColorForKey:kIMY_Nav_BG];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (IOS7) {
        //        if (!self.presentedViewController) {
        //            [self.navigationController setNavigationBarHidden:YES animated:YES];
        //        } else {
        //            [self.navigationController setNavigationBarHidden:YES animated:NO];
        //        }
        @weakify(self);
        imy_asyncMainBlock(0.2, ^{
            @strongify(self);
            [self changeToCoolNavigation:YES];
        });
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    //    UINavigationController *nav = self.navigationController;
    if (self.presentedViewController) {
        return;
    }
    if (self.keepHideNavBar) {
        //        [nav setNavigationBarHidden:YES animated:NO];
    }else if (IOS7 && !self.presentedViewController) {
        if (self.imy_isPush && [self imy_afterViewController] && ![[self imy_afterViewController] isKindOfClass:[IMYVKCoolViewController class]]) {
            //            [nav setNavigationBarHidden:NO animated:YES];
            [self changeToCoolNavigation:NO];
        } else if (self.imy_isPop && ![self.beforeVC isKindOfClass:[IMYVKCoolViewController class]]) {
            //            [nav setNavigationBarHidden:NO animated:YES];
            [self changeToCoolNavigation:NO];
        }
    }
    [super viewWillDisappear:animated];
    
}

- (void)viewDidDisappear:(BOOL)animated {
    if (self.presentedViewController) {
        return;
    }
    if (self.keepHideNavBar) {
        
    }else if (IOS7 && self.presentedViewController) {
        //        [nav setNavigationBarHidden:NO animated:NO];
        [self changeToCoolNavigation:NO];
    }
//    if (![[UIViewController imy_currentTopViewController] isKindOfClass:[IMYVKCoolViewController class]]) {
//        [[UIApplication sharedApplication] setStatusBarStyle:self.orignalStatusBarStyle];
//    }
    [super viewDidDisappear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.beforeVC = [self imy_beforeViewController];
    self.changeStatusBarStyle = UIStatusBarStyleLightContent;
    self.topStatusBarStyle = UIStatusBarStyleLightContent;
    [[UIApplication sharedApplication] setStatusBarStyle:self.topStatusBarStyle];
}

- (void)setCoolScrollView:(UIScrollView *)coolScrollView {
    if (![_coolScrollView isEqual:coolScrollView]) {
        if (_coolScrollView) {
            [_coolScrollView removeObserver:self forKeyPath:@"contentOffset"];
        }
        _coolScrollView = coolScrollView;
        if (_coolScrollView) {
            [_coolScrollView addObserver:self forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionNew context:nil];
            //        self.scrollView.delegate = self;
            self.coolScrollView.imy_top = -[UIApplication sharedApplication].statusBarFrame.size.height;
        } else {
            
        }
    }
}

- (void)changeToCoolNavigation:(BOOL)yesOrNo {
    @weakify(self);
    imy_asyncMainExecuteBlock(^{
        @strongify(self);
        if (yesOrNo) {
            self.holdNavigationController = self.navigationController;
            [[UIApplication sharedApplication] setStatusBarStyle:self.topStatusBarStyle];
            
            /// navigation bar 动画
            CGFloat alpha = self.coolScrollView.contentOffset.y / self.maxOffsetForNavAlpha;
            
            ///强制隐藏navbar
            if (self.navbarIsHide) {
                alpha = 0;
            }
            
            if (alpha >= 1.0) {
                alpha = 1.0;
                [[UIApplication sharedApplication] setStatusBarStyle:self.changeStatusBarStyle];
            } else {
                [[UIApplication sharedApplication] setStatusBarStyle:self.topStatusBarStyle];
            }
            self.navBar.alpha = alpha;
            if (self.navBarTitleFollowNavBar) {
                self.navBarTitle.alpha = alpha;
            }
            
            /// button 一直不隐藏  给背景做alpha切换
            CGFloat buttonAlpha = (1 - alpha) * 2;
            if (buttonAlpha > 1) {
                buttonAlpha = 1;
            } else if (buttonAlpha < 0) {
                buttonAlpha = 0;
            }
            self.navLeftButtonDot.alpha = self.navRightButtonDot.alpha = buttonAlpha;
        } else {
            self.holdNavigationController = nil;
            [[UIApplication sharedApplication] setStatusBarStyle:self.orignalStatusBarStyle];
        }
    });
}

- (void)dealloc {
    if (self.keepHideNavBar) {
        
    }else if (self.holdNavigationController && ![[self imy_currentShowViewController] isKindOfClass:[IMYVKCoolViewController class]]) {
        [self.holdNavigationController setNavigationBarHidden:NO animated:YES];
    }
    if (self.coolScrollView) {
        [self.coolScrollView removeObserver:self forKeyPath:@"contentOffset"];
    }
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSString *,id> *)change context:(void *)context{
    @weakify(self);
    imy_asyncMainExecuteBlock(^{
        @strongify(self);
        if ([keyPath isEqualToString:@"contentOffset"]) {
            if (self.minOffsetForNavAlpha > self.maxOffsetForNavAlpha) {
                return;
            }
            
            CGFloat offsetY = [[change valueForKey:NSKeyValueChangeNewKey] CGPointValue].y;
            
            CGFloat alpha;
            if (offsetY < self.minOffsetForNavAlpha) {
                alpha = 0;
            } else {
                alpha = (offsetY - self.minOffsetForNavAlpha) / (self.maxOffsetForNavAlpha - self.minOffsetForNavAlpha);
            }
            ///强制隐藏navbar
            if (self.navbarIsHide) {
                alpha = 0;
            }
            
            if (alpha > 1.0) {
                alpha = 1.0;
            } else if (alpha > 0.5) {
                if ([self.view imy_isDisplayedInScreen]) {
                    [[UIApplication sharedApplication] setStatusBarStyle:self.changeStatusBarStyle];
                }
            } else {
                if ([self.view imy_isDisplayedInScreen]) {
                    [[UIApplication sharedApplication] setStatusBarStyle:self.topStatusBarStyle];
                }
            }
            
            self.navBar.alpha = alpha;
            if (self.navBarTitleFollowNavBar) {
                self.navBarTitle.alpha = alpha;
            }
            CGFloat buttonAlpha = (1 - alpha) * 2;
            if (buttonAlpha > 1) {
                buttonAlpha = 1;
            } else if (buttonAlpha < 0) {
                buttonAlpha = 0;
            }
            self.navLeftButtonDot.alpha = self.navRightButtonDot.alpha = buttonAlpha;
        }
    });
}

#pragma mark - Getter, Setter methods

- (void)setMinOffsetForNavAlpha:(CGFloat)minOffsetForNavAlpha {
    _minOffsetForNavAlpha = minOffsetForNavAlpha;
}

- (void)setMaxOffsetForNavAlpha:(CGFloat)maxOffsetForNavAlpha {
    _maxOffsetForNavAlpha = maxOffsetForNavAlpha;
}

#pragma mark - GA Params

- (NSDictionary *)ga_appendParams {
    NSDictionary *dict = [super ga_appendParams];
    if (self.search_key) {
        // 如果有带搜索key，则追加到页面参数中
        NSMutableDictionary *params = [dict mutableCopy];
        params[@"search_key"] = self.search_key;
        dict = params;
        // 只执行一次，下次进入则不带 search_key
        self.search_key = nil;
    }
    return dict;
}

@end
