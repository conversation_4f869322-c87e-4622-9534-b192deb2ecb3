//
//  IMYToolsBIHelper.m
//  IMYTools
//
//  Created by <PERSON><PERSON><PERSON> on 2019/4/26.
//

#import "IMYToolsBIHelper.h"
#import <IMYBaseKit/IMYPublic.h>

@implementation IMYToolsBIHelper

+ (void)reqToolsEatCanDoFilterClickEvent:(NSString *)pageName {

    NSMutableDictionary *dic = [NSMutableDictionary new];

    [dic setObject:@"nbnc_shx" forKey:@"event"];
    [dic setObject:@(2) forKey:@"action"];
    [dic setObject:pageName forKey:@"pageName"];

    NSLog(@"bi_tools/event--%@", [dic imy_jsonString]);

    [IMYGAEventHelper postWithPath:@"event"
                            params:dic
                           headers:nil
                         completed:^(NSData *_Nullable data, NSURLResponse *_Nullable response, NSError *_Nullable error){

                         }];
}

@end
