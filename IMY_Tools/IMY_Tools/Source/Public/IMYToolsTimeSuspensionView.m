//
//  IMYToolsTimeSuspensionView.m
//  IMY_Tools
//
//  Created by 黄训瑜 on 2019/6/19
//  Copyright © 2019年 Meetyou. All rights reserved.
//

#import "IMYToolsTimeSuspensionView.h"
#import <IMYBaseKit/IMYPublic.h>

@implementation IMYToolsTimeSuspensionView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addObserver];
            
        CGFloat offset = 16.0;
        _marginInsets = UIEdgeInsetsMake(offset, offset, offset, offset);
        
    }
    
    return self;
}

#pragma mark - Private

- (void)addObserver {
    @weakify(self);
    
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillTerminateNotification object:nil] subscribeNext:^(id x) {
        @strongify(self);
        
        if (self.appWillTerminateBlock) {
            self.appWillTerminateBlock();
        }
    }];
}

#pragma mark - Public
// 重写父类动画
- (void)showAnimation {
    UIImage *image = nil;
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, NO, [UIScreen mainScreen].scale);
    
    [self.layer renderInContext:UIGraphicsGetCurrentContext()];
    image = UIGraphicsGetImageFromCurrentImageContext();
    
    UIGraphicsEndImageContext();
    
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:self.frame];
    imageView.image = image;
    
    CGPoint center = imageView.center;
    CGRect beiginFrame = imageView.frame;
    CGRect endFrame = imageView.frame;
    
    beiginFrame.size.width *= 0.1;
    beiginFrame.size.height *= 0.1;
    
    imageView.frame = beiginFrame;
    imageView.center = center;
    
    [[UIApplication sharedApplication].keyWindow addSubview:imageView];
    
    self.alpha = 0.0;
    
    @weakify(self);
    [UIView animateWithDuration:0.2
                          delay:0
                        options:UIViewAnimationOptionLayoutSubviews
                     animations:^{
                         imageView.frame = endFrame;
                     }
                     completion:^(BOOL finish) {
                         @strongify(self);
                         self.alpha = 1.0;
                         self.hidden = NO;
                         [imageView removeFromSuperview];
                     }];
}

@end
