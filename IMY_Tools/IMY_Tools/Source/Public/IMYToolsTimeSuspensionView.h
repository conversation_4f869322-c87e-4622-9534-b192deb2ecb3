//
//  IMYToolsTimeSuspensionView.h
//  IMY_Tools
//
//  Created by 黄训瑜 on 2019/6/19
//  Copyright © 2019年 Meetyou. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <IMYYQBasicServices/IMYSuspensionView.h>

NS_ASSUME_NONNULL_BEGIN

/**
 计时悬浮窗
 */
@interface IMYToolsTimeSuspensionView : IMYSuspensionView

/**
 App 即将被杀掉
 */
@property (nonatomic, copy) void (^appWillTerminateBlock)(void);

/**
 当前是否处在与其它悬浮窗冲突后, 被调整的位置
 */
@property (nonatomic, assign) BOOL isCurrentInConflictPosition;

@property (nonatomic, assign) UIEdgeInsets marginInsets;

/**
 当前正在计数的时间(单位: 秒), 子类需要重写
 */
//- (NSTimeInterval)currentTimeCount;

@end

NS_ASSUME_NONNULL_END
