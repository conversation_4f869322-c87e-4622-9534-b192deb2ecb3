//
//  IMYToolsCoolViewController.h
//  IMYYQHome
//
//  Created by why on 2017/10/31.
//  Copyright © 2017年 Linggan. All rights reserved.
//

#import <IMYBaseKit/IMYPublic.h>

@interface IMYToolsCoolViewController : IMYPublicBaseViewController

@property (nonatomic) BOOL navbarIsHide;
@property (nonatomic) BOOL navBarTitleFollowNavBar;//标题是否跟随navbar一起隐藏出现
@property (assign, nonatomic) UIStatusBarStyle orignalStatusBarStyle;
@property (assign, nonatomic) UIStatusBarStyle topStatusBarStyle;
@property (assign, nonatomic) UIStatusBarStyle changeStatusBarStyle;

@property (nonatomic, strong, readonly) UIImageView *navBar;
@property (nonatomic, strong, readonly) UILabel *navBarTitle;

@property (nonatomic, strong, readonly) UIButton *navLeftButton;
@property (nonatomic, strong, readonly) UIButton *navRightButton;

@property (nonatomic, strong, readonly) UIImageView *navLeftButtonDot;
@property (nonatomic, strong, readonly) UIImageView *navRightButtonDot;

@property(strong, nonatomic) UIScrollView *coolScrollView;

@property (nonatomic, assign) BOOL keepHideNavBar;

@property (nonatomic, assign) CGFloat minOffsetForNavAlpha; // 从某个位置开始透明效果, 小于等于该值时透明度为0, 默认为0
@property (nonatomic, assign) CGFloat maxOffsetForNavAlpha; //滑动多少距离使透明度为1。一般为（header的高度-nav高度），默认为200

- (void)setupNavigationBarByScrollView:(UIScrollView *)scrollView;

/// 埋点用
@property (nonatomic, copy) NSString *search_key;

@end
