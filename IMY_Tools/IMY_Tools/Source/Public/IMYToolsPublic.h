//
//  IMYToolsPublic.h
//  IMY_Tools
//
//  Created by TR on 3/15/16.
//  Copyright © 2016 linggan. All rights reserved.
//

#ifndef IMYToolsPublic_h
#define IMYToolsPublic_h

#import <IMYYQBasicServices/IMYToolsURIDefine.h>
#import "IMYSearchEventTrack.h"
#import "IMYToolsURIRunner.h"

// 判断 iPad
#define DX_UI_IS_IPAD (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad)
//判断iPhone12 | 12Pro
#define DX_Is_iPhone12 ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1170, 2532), [[UIScreen mainScreen] currentMode].size) && !DX_UI_IS_IPAD : NO)

//判断iPhone12 Pro Max
#define DX_Is_iPhone12_ProMax ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1284, 2778), [[UIScreen mainScreen] currentMode].size) && !DX_UI_IS_IPAD : NO)


#endif /* IMYToolsPublic_h */
