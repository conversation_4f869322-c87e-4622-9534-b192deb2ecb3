//
//  IMYYQSpecialNavigationBar.m
//  IMYYQHome
//
//  Created by why on 2017/8/31.
//  Copyright © 2017年 Linggan. All rights reserved.
//

#import "IMYToolsSpecialNavigationBar.h"
#import <IMYBaseKit/IMYViewKit.h>

@implementation IMYToolsSpecialNavigationBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {

        UIView *backView = [[UIView alloc] init];
        [backView imy_setBackgroundColorForKey:kCK_White_AN];
        [self addSubview:backView];
        self.backView = backView;

        CGRect boxFrame = CGRectMake(15, 20, 65, 44);

        IMYTouchEXButton *leftButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
        leftButton.tag = -101;
        leftButton.extendTouchInsets = UIEdgeInsetsMake(20, 6, 20, 6);
        [leftButton imy_setTitleColor:kCK_Black_A highl:kIMY_TopbarButtonTitleHighlightedColor];
        leftButton.titleEdgeInsets = UIEdgeInsetsMake(0, -3.5, 0, 0);
        leftButton.titleLabel.font = [UIFont fontWithName:@"iconfont" size:22];
        [leftButton setTitle:@"\U0000e6f3" forState:UIControlStateNormal];
        leftButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        [self addSubview:leftButton];
        self.topLeftButton = leftButton;

        boxFrame = CGRectMake(SCREEN_WIDTH - 15 - 60, 20, 65, 44);
        IMYTouchEXButton *rightButton = [[IMYTouchEXButton alloc] initWithFrame:boxFrame];
        rightButton.extendTouchInsets = UIEdgeInsetsMake(20, 6, 20, 6);
        [rightButton imy_setTitleColor:kCK_Black_A highl:kIMY_TopbarButtonTitleHighlightedColor];
        rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
        rightButton.titleLabel.font = [UIFont systemFontOfSize:16];
        [self addSubview:rightButton];
        self.topRightButton = rightButton;

        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(80, 20, SCREEN_WIDTH - 160, 44)];
        [titleLabel imy_setTextColor:kCK_Black_A highl:nil];
        titleLabel.font = [UIFont boldSystemFontOfSize:20];
        titleLabel.textAlignment = NSTextAlignmentCenter;
        [self addSubview:titleLabel];
        self.titleLabel = titleLabel;

        [self imy_setBackgroundColorForKey:kCK_White_AN];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.topLeftButton.frame = CGRectMake(15, 0, 65, 44);
    self.topRightButton.frame = CGRectMake(SCREEN_WIDTH - 15 - 70, 0, 70, 44);
    self.titleLabel.frame = CGRectMake(80, 0, SCREEN_WIDTH - 160, 44);
    self.backView.frame = CGRectMake(0, -44, SCREEN_WIDTH, 44 + 44);
}

- (void)setCustomTitleView:(UIView *)customTitleView {
    if (_customTitleView != customTitleView) {
        if (customTitleView) {
            customTitleView.center = CGPointMake(SCREEN_WIDTH / 2, 44 / 2 + 20);
            [self addSubview:customTitleView];
            self.titleLabel.hidden = YES;
        } else {
            [customTitleView removeFromSuperview];
            self.titleLabel.hidden = NO;
        }
        _customTitleView = customTitleView;
    }
}

- (void)setTopRightButton:(UIButton *)topRightButton {
    if (_topRightButton != topRightButton) {
        topRightButton.frame = _topRightButton.frame;
        [self addSubview:topRightButton];
        [_topRightButton removeFromSuperview];
        _topRightButton = topRightButton;
    }
}

- (void)setTopLeftButton:(UIButton *)topLeftButton {
    if (_topLeftButton != topLeftButton) {
        topLeftButton.frame = _topLeftButton.frame;
        [self addSubview:topLeftButton];
        [_topLeftButton removeFromSuperview];
        _topLeftButton = topLeftButton;
    }
}

@end
