//
//  IMYToolsCustomNavBarVC.m
//  IMYTools
//
//  Created by hsk on 2020/11/12.
//

#import "IMYToolsCustomNavBarVC.h"
#import "Masonry.h"

@interface IMYToolsCustomNavBarVC (){
    UILabel *titleLabel;
}

@end

@implementation IMYToolsCustomNavBarVC

- (void)fixitEdgesForExtendedLayoutZero {

}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    [self showNavView];
}

- (void)showNavView{
    [self.view bringSubviewToFront:self.navView];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.navigationBarHidden = YES;
    
    [self creatNavView];
    
    self.hiddenNavLeftView = NO;
    self.hiddenNavRightView = YES;
}

#pragma mark - 创建默认导航栏
- (void)creatNavView{
    self.navView = [UIView new];
    [self.navView imy_setBackgroundColorForKey:kCK_Black_F];
    
    [self.view addSubview:self.navView];
    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(SCREEN_NAVIGATIONBAR_HEIGHT+SCREEN_STATUSBAR_HEIGHT);
        make.left.right.top.equalTo(self.view);
    }];
   
    //分割线
    self.navSepLineView = [UIView new];
    [self.navSepLineView imy_setBackgroundColorForKey:kCK_Black_J];
    
    [self.navView addSubview:self.navSepLineView];
    
    [self.navSepLineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.navView);
        make.height.mas_equalTo(1.0 / [UIScreen mainScreen].scale);
    }];
    
    
    [self creatNavLeftView];
    [self creatNavRightView];
    [self creatNavTitleView];
}

#pragma mark - 创建默认导航栏左边视图
- (void)creatNavLeftView{
    UIButton *leftBtn = [IMYToolsCustomNavBarVC createButton:@"" Target:self Sel:@selector(leftBtnClick:) Img1:@"nav_btn_back_black" Img2:@"" font:16 tag:9];
    
    self.navLeftView = leftBtn;
    [self.navView addSubview:self.navLeftView];
    
    [self.navLeftView mas_makeConstraints:^(MASConstraintMaker* make) {
        make.size.mas_equalTo(CGSizeMake(56, 40));
        make.bottom.mas_equalTo(self.navView).offset(tools_navBottomEdges);
        make.left.mas_equalTo(self.navView).offset(0);
    }];
}

#pragma mark - 创建默认导航栏右边视图
- (void)creatNavRightView{
    
}

#pragma mark - 创建默认导航栏中间视图
- (void)creatNavTitleView{
    titleLabel = [IMYToolsCustomNavBarVC createLabel:self.navTitle withFontSize:17];
    [titleLabel imy_setTextColorForKey:kCK_Black_A];
    titleLabel.font = [UIFont boldSystemFontOfSize:17];
    titleLabel.numberOfLines = 1;
    titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    
    self.navTitleView = titleLabel;
    [self.navView addSubview:self.navTitleView];
    
    [self.navTitleView mas_makeConstraints:^(MASConstraintMaker* make) {
        make.size.mas_equalTo(CGSizeMake(SCREEN_WIDTH-56*2, 40));
        make.bottom.mas_equalTo(self.navView).offset(tools_navBottomEdges);
        make.centerX.equalTo(self.navView);
    }];
}

#pragma mark - 设置导航栏左边视图隐藏
-(void)setHiddenNavLeftView:(BOOL)hiddenNavLeftView{
    _hiddenNavLeftView = hiddenNavLeftView;
    self.navLeftView.hidden = hiddenNavLeftView;
}

#pragma mark - 设置导航栏右边隐藏
-(void)setHiddenNavRightView:(BOOL)hiddenNavRightView{
    _hiddenNavRightView = hiddenNavRightView;
    self.navRightView.hidden = hiddenNavRightView;
}

#pragma mark - 设置分割线隐藏
- (void)setHiddenNavLineView:(BOOL)hiddenNavLineView{
    _hiddenNavLineView = hiddenNavLineView;
    self.navSepLineView.hidden = hiddenNavLineView;
}

#pragma mark - title
- (void)setNavTitle:(NSString *)navTitle{
    _navTitle = navTitle;
    titleLabel.text = navTitle;
}

+ (UIButton*)createButton:(NSString*)title Target:(id)t_target Sel:(SEL)t_sel Img1:(NSString*)t_img1 Img2:(NSString*)t_img2 font:(NSInteger)fontsize tag:(NSInteger)t_tag{
    
    UIButton* button = [UIButton buttonWithType:UIButtonTypeCustom];
    if (title) {
        [button setTitle:[[NSString alloc] initWithFormat:@"%@", title] forState:UIControlStateNormal];
    }
    [button imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
    button.titleLabel.font = [UIFont systemFontOfSize:fontsize];
    if (t_img1) {
        [button imy_setImage:t_img1 
                       state:UIControlStateNormal];
    }
    if (t_img2) {
        [button imy_setImage:t_img2 
                       state:UIControlStateSelected];
    }
    [button setTag:t_tag];
    button.adjustsImageWhenHighlighted = NO;
    [button addTarget:t_target action:t_sel forControlEvents:UIControlEventTouchUpInside];
    
    return button;
}

+ (UILabel *)createLabel:(NSString*)t_text withFontSize:(NSUInteger)size{
    UILabel* label = [[UILabel alloc] init];
    if (t_text) {
        [label setText:[[NSString alloc] initWithFormat:@"%@", t_text]];
    }
    [label imy_setTextColorForKey:kCK_White_AT];
    label.textAlignment = NSTextAlignmentCenter;
    [label setBackgroundColor:[UIColor clearColor]];
    label.font = [UIFont systemFontOfSize:size];
    label.numberOfLines = 0;
    
    return label;
}
#pragma mark - go back
- (void)leftBtnClick:(UIButton *)sender{
    [self imy_pop:YES];
}

@end
