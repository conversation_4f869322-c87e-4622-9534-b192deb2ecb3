//
//  IMYToolsCustomNavBarVC.h
//  IMYTools
//
//  Created by hsk on 2020/11/12.
//

#import "IMYPublicBaseViewController.h"
#import <IMYBaseKit/IMYPublic.h>

NS_ASSUME_NONNULL_BEGIN

//适配导航栏
#define tools_navBottomEdges -5.f

@interface IMYToolsCustomNavBarVC : IMYPublicBaseViewController

//导航栏自定义
@property (nonatomic , assign) BOOL hiddenNavLeftView; //隐藏导航栏左视图
@property (nonatomic , assign) BOOL hiddenNavRightView; //隐藏导航栏右视图
@property (nonatomic , assign) BOOL hiddenNavLineView; //隐藏导航栏分割线

@property (nonatomic , strong) UIView *navView; //导航栏视图
@property (nonatomic , strong) UIView *navLeftView; //导航栏左视图
@property (nonatomic , strong) UIView *navTitleView; //导航栏中间视图
@property (nonatomic , strong) UIView *navRightView; //导航栏右视图
@property (nonatomic , strong) UIView *navSepLineView; //导航栏分割线
@property (nonatomic , copy) NSString *navTitle; //导航栏中间标题

#pragma mark - creat button
+ (UIButton*)createButton:(NSString*)title Target:(id)t_target Sel:(SEL)t_sel Img1:(NSString*)t_img1 Img2:(NSString*)t_img2 font:(NSInteger)fontsize tag:(NSInteger)t_tag;

#pragma mark - creat Label
+ (UILabel *)createLabel:(NSString*)t_text withFontSize:(NSUInteger)size;

#pragma mark - 处理无网络状态进入时，导航栏被覆盖问题
- (void)showNavView;

@end

NS_ASSUME_NONNULL_END
