//
//  IMYToolsOvulateManager.m
//  YunQi
//
//  Created by why on 15/5/13.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import "IMYToolsOvulateManager.h"
#import "IMYToolNetWorking.h"
#import "IMYToolsUserService.h"
#import <IMYYQBasicServices/IMYToolsOvulateModel+helper.h>
#import <IMYBaseKit/YYJSONHelper.h>
#import <IMYRecord/IMYAIPredictiveManager.h>
#import <IMYRecord/IMYRecordDefines.h>

IMY_KYLIN_FUNC_IDLE_ASYNC {
    //排卵试纸记录项右侧的内容，来自接口：
    //https://gravidity.seeyouyima.com/dipstick
    //未进入“排卵试纸”页面，需要显示排卵试纸记录项右侧的内容，则需要提前请求接口
    [[[IMYPublicAppHelper shareAppHelper].useridChangedSignal deliverOnMainThread] subscribeNext:^(id x) {
        if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
            //同步本地数据到服务器。
            [[IMYToolsOvulateManager sharedManager] updateOvulateServer:^(BOOL success, NSError *error) {
                //下载网络数据。
                [[IMYToolsOvulateManager sharedManager] updateOvulateLocal:^(BOOL isSuccess, NSError *error) {
                    //下载完成
                    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordCalendarReload object:nil];
                }];
            }];
        }
    }];
}

#define IMYTOOLS_OVULATE_PATH @"dipstick"

@implementation IMYToolsOvulateManager

+ (id)sharedManager {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
        [instance setupData];
    });
    return instance;
}

- (void)setupData {
    self.modifyList = [[NSMutableArray alloc] init];
}

#pragma mark - 网络
/// 排卵试纸列表
- (void)getOvulateTestListWithSuccess:(BKArrayBlock)success failure:(void (^)(NSError *error))failure {
    [IMYToolNetworking getWithHost:gravidity_seeyouyima_com
        path:IMYTOOLS_OVULATE_PATH
        parameters:nil
        successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
            if (![[responseObject YYJSONString] isEqualToString:@"false"]) {

                NSArray *datas = [responseObject toModels:[IMYToolsOvulateModel class]];
                success(datas);
            } else {
                success(nil);
            }
        }
        failedBlock:^(id error) {
            failure(error);
        }];
}

- (void)postOvulateTestToServer:(void (^)(BOOL success, NSError *error))handler {
    NSMutableArray *addedOvulateArrayLocal = [NSMutableArray array];
    NSMutableArray *editedOvulateArrayLocal = [NSMutableArray array];
    NSMutableArray *deletedOvulateArrayLocal = [NSMutableArray array];
    // 上传所有未同步的数据
    if ([IMYToolsOvulateModel hasTempUserTable]) {
        NSArray *tempDataArray = [IMYToolsOvulateModel getTempUserData];
        [tempDataArray enumerateObjectsUsingBlock:^(IMYToolsOvulateModel *obj, NSUInteger idx, BOOL *_Nonnull stop) {
            if (obj.operationState == IMYToolsOvulateOperationStateAdd) {
                [addedOvulateArrayLocal addObject:obj];
            } else if (obj.operationState == IMYToolsOvulateOperationStateEdit) {
                [editedOvulateArrayLocal addObject:obj];
            } else if (obj.operationState == IMYToolsOvulateOperationStateDelete) {
                [deletedOvulateArrayLocal addObject:obj];
            }
        }];
    } else {
        addedOvulateArrayLocal = [IMYToolsOvulateModel getOvulatesWithOperationState:IMYToolsOvulateOperationStateAdd];
        editedOvulateArrayLocal = [IMYToolsOvulateModel getOvulatesWithOperationState:IMYToolsOvulateOperationStateEdit];
        deletedOvulateArrayLocal = [IMYToolsOvulateModel getOvulatesWithOperationState:IMYToolsOvulateOperationStateDelete];
    }

    NSMutableArray *addArr = [NSMutableArray new];
    for (NSInteger i = 0; i < addedOvulateArrayLocal.count; i++) {
        IMYToolsOvulateModel *model = [addedOvulateArrayLocal objectAtIndex:i];
        NSMutableDictionary *info = [NSMutableDictionary new];
        NSString *time;
        if (model.imageDate > 0) {
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.imageDate];
            IMYDateFormatter *formatter = [[IMYDateFormatter alloc] init];
            [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
            NSTimeZone *timeZone = [NSTimeZone timeZoneForSecondsFromGMT:8*60*60];
            [formatter setTimeZone:timeZone];
            time = [formatter stringFromDate:date];
        } else { //在非东八区的地区，直接上传imageDateString会导致get接口下发的时间戳字段time，与实际时间不一致的问题，上传接口默认客户端上传的“time”是东八区时间字符串。
            time = model.imageDateString;
        }
        [info setValue:time forKey:@"time"];
        [info setValue:[NSString stringWithFormat:@"%zd", model.state] forKey:@"label"];

        if (model.imageURLString.length == 0) {
            [info setValue:@"" forKey:@"img"];
        } else {
            [info setValue:model.imageURLString forKey:@"img"];
        }
        [info setValue:@([model.max floatValue]) forKey:@"max"];
        if (imy_isNotBlankString(model.t)) {
            [info setValue:@([model.t floatValue]) forKey:@"t"];
        }else{
            [info setValue:@(-1) forKey:@"t"];
        }
        if (imy_isNotBlankString(model.c)) {
            [info setValue:@([model.c floatValue]) forKey:@"c"];
        } else {
            [info setValue:@(-1) forKey:@"c"];
        }
        [addArr addObject:info];
    }

    NSMutableArray *editArr = [NSMutableArray new];
    for (NSInteger i = 0; i < editedOvulateArrayLocal.count; i++) {
        IMYToolsOvulateModel *model = [editedOvulateArrayLocal objectAtIndex:i];
        if (model.ovulate_id == 0) {
            //旧数据
            continue;
        }
        NSMutableDictionary *info = [NSMutableDictionary new];
        [info setValue:model.imageDateString forKey:@"time"];
        [info setValue:[NSString stringWithFormat:@"%zd", model.state] forKey:@"label"];
        if (model.imageURLString.length == 0) {
            [info setValue:@"" forKey:@"img"];
        } else {
            [info setValue:model.imageURLString forKey:@"img"];
        }
        [info setValue:@([model.max floatValue]) forKey:@"max"];
        if (imy_isNotBlankString(model.t)) {
            [info setValue:@([model.t floatValue]) forKey:@"t"];
        }else{
            [info setValue:@(-1) forKey:@"t"];
        }
        if (imy_isNotBlankString(model.c)) {
            [info setValue:@([model.c floatValue]) forKey:@"c"];
        } else {
            [info setValue:@(-1) forKey:@"c"];
        }
        [info setValue:[NSString stringWithFormat:@"%zd", model.ovulate_id] forKey:@"id"];
        [editArr addObject:info];
    }


    NSMutableArray *delArr = [NSMutableArray new];
    for (NSInteger i = 0; i < deletedOvulateArrayLocal.count; i++) {
        IMYToolsOvulateModel *model = [deletedOvulateArrayLocal objectAtIndex:i];
        if (model.ovulate_id == 0) {
            //旧数据
            continue;
        }
        NSMutableDictionary *info = [NSMutableDictionary new];
        [info setValue:[NSString stringWithFormat:@"%zd", model.ovulate_id] forKey:@"id"];

        [delArr addObject:info];
        //        [model deleteToDB];
    }


    NSMutableDictionary *param = [NSMutableDictionary new];
    [param setValue:addArr forKey:@"add"];
    [param setValue:editArr forKey:@"update"];
    [param setValue:delArr forKey:@"delete"];

    if (addArr.count == 0 && editArr.count == 0 && delArr.count == 0) {
        self.isNotOvulateDateChange = YES;
        handler(YES, nil);
        self.isNotOvulateDateChange = NO;
        [self deleteTempData];
        return;
    }

    if ([param allKeys].count == 0) {
        handler(NO, nil);
        return;
    }

    self.isUploading = YES;
    @weakify(self);
    [IMYToolNetworking postWithHost:gravidity_seeyouyima_com
        path:IMYTOOLS_OVULATE_PATH
        parameters:param
        successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
            @strongify(self);
            self.isUploading = NO;
            NSString *response = [responseObject YYJSONString];
            if (response) {
                handler(YES, nil);
                [self deleteTempData];
            } else {

                if ([responseObject isKindOfClass:[NSData class]]) {

                    NSString *str = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
                    if ([str isEqualToString:@"true"]) {
                        handler(YES, nil);
                        [self deleteTempData];
                        return;
                    }
                }

                handler(NO, nil);
            }
        }
        failedBlock:^(id error) {
            @strongify(self);
            self.isUploading = NO;
            handler(NO, error);
        }];
}


///添加排卵试纸
- (void)addOvulateTestToServerWithModel:(IMYToolsOvulateModel *)model handler:(void (^)(BOOL success, NSInteger ovulateId, NSError *error))handler {
    NSMutableDictionary *info = [NSMutableDictionary new];
    NSString *time;
    if (model.imageDate > 0) {
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.imageDate];
        IMYDateFormatter *formatter = [[IMYDateFormatter alloc] init];
        [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
        NSTimeZone *timeZone = [NSTimeZone timeZoneForSecondsFromGMT:8*60*60];
        [formatter setTimeZone:timeZone];
        time = [formatter stringFromDate:date];
    } else { //在非东八区的地区，直接上传imageDateString会导致get接口下发的时间戳字段time，与实际时间不一致的问题，上传接口默认客户端上传的“time”是东八区时间字符串。
        time = model.imageDateString;
    }
    [info setValue:time forKey:@"time"];
    [info setValue:[NSString stringWithFormat:@"%zd", model.state] forKey:@"label"];

    if (model.imageURLString.length == 0) {
        [info setValue:@"" forKey:@"img"];
    } else {
        [info setValue:model.imageURLString forKey:@"img"];
    }
    [info setValue:@([model.max floatValue]) forKey:@"max"];
    if (imy_isNotBlankString(model.t)) {
        [info setValue:@([model.t floatValue]) forKey:@"t"];
    }else{
        [info setValue:@(-1) forKey:@"t"];
    }
    if (imy_isNotBlankString(model.c)) {
        [info setValue:@([model.c floatValue]) forKey:@"c"];
    } else {
        [info setValue:@(-1) forKey:@"c"];
    }
    @weakify(self);
    IMYHTTPBuildable *buildable = [IMYServerRequest post:@"v3/dipstick/add" host:gravidity_seeyouyima_com params:info headers:nil];
    buildable.timeout = 5;//超时时间
    [[buildable.signal deliverOnMainThread] subscribeNext:^(id<IMYHTTPResponse> response) {
        @strongify(self);
        //延迟1秒去请求数据(预测经期长度)
        imy_asyncBlock(1.0, ^{
            NSDictionary *dict = @{@"method": @"POST", @"type": @(6)};
            [[IMYAIPredictiveManager shareManager] downloadPredictiveInfo:dict complete:^(BOOL isUpdate, NSError * _Nonnull error) {
            }];
        });
        
        if ([response.responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *responseObject = (NSDictionary *)response.responseObject;
            IMYToolsOvulateModel *infoModel = [responseObject[@"info"] toModel:[IMYToolsOvulateModel class]];
              handler(YES,infoModel.ovulate_id, nil);
            [self deleteTempData];
            [[IMYURIManager shareURIManager] runActionWithString:@"record/sync/alldata"];
        } else {
            handler(NO,0, nil);
        }
    } error:^(NSError *error) {
        @strongify(self);
        handler(NO,0, error);
    }];
}


///添加覆盖排卵试纸
- (void)coverOvulateTestToServerWithModel:(IMYToolsOvulateModel *)model oldModel:(IMYToolsOvulateModel *)oldModel handler:(void (^)(BOOL success, NSError *error))handler {
    NSMutableArray *addArr = [NSMutableArray new];
    {  //添加
        NSMutableDictionary *info = [NSMutableDictionary new];
        NSString *time;
        if (model.imageDate > 0) {
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.imageDate];
            IMYDateFormatter *formatter = [[IMYDateFormatter alloc] init];
            [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
            NSTimeZone *timeZone = [NSTimeZone timeZoneForSecondsFromGMT:8*60*60];
            [formatter setTimeZone:timeZone];
            time = [formatter stringFromDate:date];
        } else { //在非东八区的地区，直接上传imageDateString会导致get接口下发的时间戳字段time，与实际时间不一致的问题，上传接口默认客户端上传的“time”是东八区时间字符串。
            time = model.imageDateString;
        }
        [info setValue:time forKey:@"time"];
        [info setValue:[NSString stringWithFormat:@"%zd", model.state] forKey:@"label"];

        if (model.imageURLString.length == 0) {
            [info setValue:@"" forKey:@"img"];
        } else {
            [info setValue:model.imageURLString forKey:@"img"];
        }
        [info setValue:@([model.max floatValue]) forKey:@"max"];
        if (imy_isNotBlankString(model.t)) {
            [info setValue:@([model.t floatValue]) forKey:@"t"];
        }else{
            [info setValue:@(-1) forKey:@"t"];
        }
        if (imy_isNotBlankString(model.c)) {
            [info setValue:@([model.c floatValue]) forKey:@"c"];
        } else {
            [info setValue:@(-1) forKey:@"c"];
        }
        [addArr addObject:info];
    }
    
    NSMutableArray *delArr = [NSMutableArray new];
    {  //删除
        NSMutableDictionary *info = [NSMutableDictionary new];
        [info setValue:[NSString stringWithFormat:@"%zd", oldModel.ovulate_id] forKey:@"id"];
        [delArr addObject:info];
    }
    
    NSMutableDictionary *param = [NSMutableDictionary new];
    [param setValue:addArr forKey:@"add"];
    [param setValue:delArr forKey:@"delete"];
    [self handlePostOvulateTestToServerWithParam:param handler:handler];
}

///编辑排卵试纸
- (void)editOvulateTestToServerWithModel:(IMYToolsOvulateModel *)model handler:(void (^)(BOOL success, NSError *error))handler {
    NSMutableArray *editArr = [NSMutableArray new];
    NSMutableDictionary *info = [NSMutableDictionary new];
    [info setValue:model.imageDateString forKey:@"time"];
    [info setValue:[NSString stringWithFormat:@"%zd", model.state] forKey:@"label"];
    if (model.imageURLString.length == 0) {
        [info setValue:@"" forKey:@"img"];
    } else {
        [info setValue:model.imageURLString forKey:@"img"];
    }
    [info setValue:@([model.max floatValue]) forKey:@"max"];
    if (imy_isNotBlankString(model.t)) {
        [info setValue:@([model.t floatValue]) forKey:@"t"];
    }else{
        [info setValue:@(-1) forKey:@"t"];
    }
    if (imy_isNotBlankString(model.c)) {
        [info setValue:@([model.c floatValue]) forKey:@"c"];
    } else {
        [info setValue:@(-1) forKey:@"c"];
    }
    [info setValue:[NSString stringWithFormat:@"%zd", model.ovulate_id] forKey:@"id"];
    [editArr addObject:info];
    
    NSMutableDictionary *param = [NSMutableDictionary new];
    [param setValue:editArr forKey:@"update"];
    [self handlePostOvulateTestToServerWithParam:param handler:handler];
}

/// 删除排卵试纸
- (void)deleteOvulateTestToServerWithModel:(IMYToolsOvulateModel *)model handler:(void (^)(BOOL success, NSError *error))handler {
    if (model.ovulate_id == 0) {
        //旧数据 异常数据。服务端还没数据。直接算删除成功
        handler(YES, nil);
        return;;
    }
    NSMutableArray *delArr = [NSMutableArray new];
    NSMutableDictionary *info = [NSMutableDictionary new];
    [info setValue:[NSString stringWithFormat:@"%zd", model.ovulate_id] forKey:@"id"];
    [delArr addObject:info];

    NSMutableDictionary *param = [NSMutableDictionary new];
    [param setValue:delArr forKey:@"delete"];
    [self handlePostOvulateTestToServerWithParam:param handler:handler];
}

- (void)handlePostOvulateTestToServerWithParam:(NSMutableDictionary *)param handler:(void (^)(BOOL success, NSError *error))handler {
    @weakify(self);
    [IMYToolNetworking postWithHost:gravidity_seeyouyima_com
                               path:IMYTOOLS_OVULATE_PATH
                         parameters:param
                       successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
        @strongify(self);
        //延迟1秒去请求数据(预测经期长度)
        imy_asyncBlock(1.0, ^{
            NSDictionary *dict = @{@"method": @"POST", @"type": @(6)};
            [[IMYAIPredictiveManager shareManager] downloadPredictiveInfo:dict complete:^(BOOL isUpdate, NSError * _Nonnull error) {
            }];
        });
        NSString *response = [responseObject YYJSONString];
        if (response) {
            handler(YES, nil);
            [self deleteTempData];
        } else {
            if ([responseObject isKindOfClass:[NSData class]]) {
                NSString *str = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
                if ([str isEqualToString:@"true"]) {
                    handler(YES, nil);
                    [self deleteTempData];
                    return;
                }
            }
            handler(NO, nil);
        }
    } failedBlock:^(id error) {
        @strongify(self);
        handler(NO, error);
    }];
}


// 删除临时用户的本地记录
- (void)deleteTempData {
    if ([IMYToolsOvulateModel hasTempUserTable]) {
        [IMYToolsOvulateModel deleteTempUserTable];
    }
}

#pragma mark - 添加或修改，需要将模型将在最后一位

- (void)handleModifyListWithModel:(IMYToolsOvulateModel *)model {
    __block IMYToolsOvulateModel *findOvulateModel = nil;
    [[IMYToolsOvulateManager sharedManager].modifyList enumerateObjectsUsingBlock:^(IMYToolsOvulateModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj.imageDateString isEqualToString:model.imageDateString]) {
            findOvulateModel = obj;
            *stop = YES;
        }
    }];
    if (findOvulateModel) {
        [[IMYToolsOvulateManager sharedManager].modifyList removeObject:findOvulateModel];
        [[IMYToolsOvulateManager sharedManager].modifyList addObject:model];
    } else {
        [[IMYToolsOvulateManager sharedManager].modifyList addObject:model];
    }
}

#pragma mark - 外部方法
- (void)synchroData:(void (^)(BOOL success, NSError *error))completed {
    //同步本地数据到服务器。
    [[IMYToolsOvulateManager sharedManager] updateOvulateServer:^(BOOL success, NSError *error) {
        //下载网络数据。
        [[IMYToolsOvulateManager sharedManager] updateOvulateLocal:^(BOOL isSuccess, NSError *error) {
            if (isSuccess) {
                if (completed) {
                    completed(YES,nil);
                }
            } else {
                if (completed) {
                    completed(NO,error);
                }
            }
        }];
    }];
}

- (void)updateOvulateServer:(void (^)(BOOL success, NSError *error))completed {

    [self postOvulateTestToServer:^(BOOL success, NSError *error) {
        if (success) {
            NSLog(IMYString(@"上传试纸数据成功"));
            // 更改本地数据都为默认状态
            NSMutableArray *localOvulates = [IMYToolsOvulateModel getLocalOvulate];
            for (IMYToolsOvulateModel *model in localOvulates) {
                model.operationState = IMYToolsOvulateOperationStateNormal;
            }
            [IMYToolsOvulateModel saveOvulates:localOvulates];

            NSMutableArray *deletedOvulateArrayLocal = [IMYToolsOvulateModel getOvulatesWithOperationState:IMYToolsOvulateOperationStateDelete];
            for (IMYToolsOvulateModel *model in deletedOvulateArrayLocal) {
                [model deleteToDB];
            }

        } else {
            NSLog(IMYString(@"post出错--%@"), error);
        }

        if (completed) {
            completed(success,nil);
        }
    }];
}


- (void)updateOvulateServer {
    [self postOvulateTestToServer:^(BOOL success, NSError *error) {
        if (success) {
            NSLog(IMYString(@"上传试纸数据成功"));
            // 更改本地数据都为默认状态
            NSMutableArray *localOvulates = [IMYToolsOvulateModel getLocalOvulate];
            for (IMYToolsOvulateModel *model in localOvulates) {
                model.operationState = IMYToolsOvulateOperationStateNormal;
            }
            [IMYToolsOvulateModel saveOvulates:localOvulates];

            NSMutableArray *deletedOvulateArrayLocal = [IMYToolsOvulateModel getOvulatesWithOperationState:IMYToolsOvulateOperationStateDelete];
            for (IMYToolsOvulateModel *model in deletedOvulateArrayLocal) {
                [model deleteToDB];
            }
            if (!self.isNotOvulateDateChange) {
                //记录模块更新首页
                IMY_POST_NOTIFY(@"kNotificationRecordSyncImmediately");
                //上传成功，BI埋点
                [[IMYURIManager shareURIManager] runActionWithPath:@"record/bi_record"
                                                            params:@{@"item_id": @12,
                                                                     @"position": @2,
                                                                     @"action": @3}
                                                              info:nil];
            }

        } else {
            NSLog(IMYString(@"post出错--%@"), error);
        }
    }];
}

/// 将cu_default的数据转移
- (void)cudefaultDataReSave {
    // 存在cu_default 这个id的数据
    BOOL hasTempUserData = [IMYToolsOvulateModel hasTempUserTable];
    BOOL notLogin = ![IMYPublicAppHelper shareAppHelper].hasLogin;
    BOOL notCuDefault = [[IMYPublicAppHelper shareAppHelper].userid isEqualToString:kTempUserId];
    /// 用户是注册登录还是已有账号登录
    BOOL isRegistLogin = [[IMYUserDefaults standardUserDefaults] boolForKey:@"IsRegistLoginUDKEY"];

    if (hasTempUserData && (notLogin || isRegistLogin) && notCuDefault) {
        NSArray *tempUserData = [IMYToolsOvulateModel getTempUserData];

        for (IMYToolsOvulateModel *oldModel in tempUserData) {
            [IMYToolsOvulateModel saveWithTempUserModel:oldModel];
        }

        // 删除cu_default数据
        [IMYToolsOvulateModel deleteTempUserTable];
    }
}

- (void)updateOvulateLocal:(void (^)(BOOL isSuccess, NSError *error))successBlock {
    [self cudefaultDataReSave];

    //    [NSObject imy_asyncBlock:^{
    [self
        getOvulateTestListWithSuccess:^(NSArray *serverArray) {
            if (!serverArray) {
                if (successBlock) {
                    successBlock(YES,nil);
                }
                return;
            }
            //            NSMutableArray *writeArray = [NSMutableArray new];
            //            NSArray *localArray = [self getLocalOvulate];
            //旧版本数据，ovulate_id为0，这个是不能删除掉的
            [IMYToolsOvulateModel deleteWithWhere:@"ovulate_id > 0"];
            for (IMYToolsOvulateModel *model in serverArray) {
                model.operationState = IMYToolsOvulateOperationStateNormal;
            }
            [IMYToolsOvulateModel saveOvulates:serverArray];

            //            if (localArray.count == 0) {
            //                for (SYOvulateModel *model in serverArray) {
            //                    model.operationState = SYOvulateOperationStateNormal;
            //                }
            //                [writeArray addObjectsFromArray:serverArray];
            //            } else {
            //                for (NSInteger i = 0; i < serverArray.count; i++) {
            //                    SYOvulateModel *serverModel = [serverArray objectAtIndex:i];
            //                    serverModel.operationState = SYOvulateOperationStateNormal;
            //
            //                    for (NSInteger j = 0; j < localArray.count; j++) {
            //                        SYOvulateModel *localModel = [localArray objectAtIndex:j];
            //                        if (serverModel.ovulate_id == localModel.ovulate_id && localModel.ovulate_id != 0) {
            //                            [localModel deleteToDB];
            //
            //                            [self saveOvulates:@[serverModel]];
            //                            break;
            //                        }
            //                        else if (serverModel.imageDate == localModel.imageDate && localModel.ovulate_id == 0){
            //                            [localModel deleteToDB];
            //
            //                            [self saveOvulates:@[serverModel]];
            //                            break;
            //                        }
            //                        else if (j == localArray.count - 1) {
            //
            //                            [writeArray addObject:serverModel];
            //                            break;
            //                        }
            //                    }
            //                }
            //            }
            //
            //            if (writeArray.count) {
            //                [self saveOvulates:writeArray];
            //            }
            if (successBlock) {
                successBlock(YES,nil);
            }
        }
        failure:^(NSError *error) {
            NSLog(IMYString(@"get出错--%@"), error);
            if (successBlock) {
                successBlock(NO,error);
            }
        }];
    //    }];
}


@end
