//
//  IMYToolsOvulateTablePageView.m
//  YunQi
//
//  Created by zcf on 15/5/18.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import "IMYToolsOvulateTablePageView.h"
#import "IMYToolsOvulateCell.h"
#import <IMYYQBasicServices/IMYToolsURIDefine.h>
#import "IMYToolsOvulateAiAiSuggestView.h"
#if __has_include(<BBJViewKit/BBJNavBarBottomLine.h>)
#import <BBJViewKit/BBJNavBarBottomLine.h>
#endif
#import <IMYCommonKit/IMYCKOperateBannerView.h>
#import <IMYCommonKit/IMYCKOperateBannerConfig.h>
#import <IMYRecord/IMYRecordBannerManager.h>

@interface IMYToolsOvulateTablePageView () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) IMYCaptionView *captionView;

@property (nonatomic, strong) UIButton *notifyOvulateButton;
@property (nonatomic, strong) UIButton *helpOvulateButton;

/// 是否已经反色
@property (nonatomic, assign) BOOL isInvert;
@property (nonatomic, strong) UIView *tableFooterView;
@property (nonatomic, strong) IMYToolsOvulateAiAiSuggestView *aiaiSuggestView;
@property (nonatomic, strong) IMYCKOperateBannerView *bannerView;
@property (nonatomic, assign) BOOL hasScrollEnd;

@property (nonatomic, strong) UIView *lineView;

@end

@implementation IMYToolsOvulateTablePageView

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IMYToolsOvulateInvertNotification object:nil];
}

- (instancetype)initWithFrame:(CGRect)frame andViewModel:(IMYToolsOvulateTablePageVM *)viewModel {
    self = [super initWithFrame:frame];
    if (self) {
        [self prepareUI];
        self.reuseIdentifier = NSStringFromClass([self class]);
        self.viewModel = viewModel;
        @weakify(self);
        [RACObserve(self, viewModel.ovulateVMsArray) subscribeNext:^(NSArray *array) {
            @strongify(self);
            [self reloadViewWithCount:array.count];
            if (!self.hasScrollEnd) {
                self.hasScrollEnd = YES;
                imy_asyncMainBlock(0.1, ^{
                    [self scrollToBottomAnimated:NO];
                });
            }
        }];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changInvert:) name:IMYToolsOvulateInvertNotification object:nil];
    }
    return self;
}

//滚动到底部
- (void)scrollToBottomAnimated:(BOOL)animated {
    NSInteger rows = [self.tableView numberOfRowsInSection:0];
    if (rows > 0 && self.tableView.contentSize.height > self.tableView.frame.size.height) {
        [self.tableView setContentOffset:CGPointMake(0, self.tableView.contentSize.height - self.tableView.frame.size.height) animated:animated];
    }
}


// 滚动关闭键盘
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    NSLog(@"scrollView y = %f",scrollView.contentOffset.y);
    CGFloat alpha = 0.0;
    if (scrollView.contentOffset.y > 0) {
        alpha = scrollView.contentOffset.y/8.0;
    }else if (scrollView.contentOffset.y <= 0){
        alpha = 0.0;
    }else {
        alpha = 1.0;
    }
    self.lineView.alpha = alpha;
}

- (void)prepareUI {
    self.tableView = [[UITableView alloc] initWithFrame:self.bounds style:UITableViewStylePlain];
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView registerClass:IMYToolsOvulateCell.class forCellReuseIdentifier:@"IMYToolsOvulateCell"];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self addSubview:self.tableView];
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 4)];
    headerView.backgroundColor = UIColor.clearColor;
    [self.tableView setTableHeaderView:headerView];
    self.tableFooterView.frame = CGRectMake(0, 0, SCREEN_WIDTH, 8);
    [self.tableView setTableFooterView:self.tableFooterView];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self addSubview:self.lineView];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo(0.5);
    }];
    
    [self addSubview:self.captionView];
    [self.captionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self imy_setBackgroundColor:kCK_Black_F];
    
    @weakify(self);
    [self imy_addThemeActionBlock:^(id weakObject) {
        @strongify(self);
        [self checkAiAiSuggest];
    } forKey:@"ThemeChange"];
}

#pragma mark - public Methods
- (BOOL)confirmDelete {
    NSLog(@"真的要删除咯--\n%@", self.viewModel.deleteArray);
    BOOL commit = self.viewModel.deleteArray.count;
    if (commit) {
        [self.viewModel deleteData_sync];
        [self.tableView reloadData];
        @weakify(self);
        [NSObject
         imy_asyncMainBlock:^{
            @strongify(self);
            NSInteger remainCount = self.viewModel.ovulateVMsArray.count;
            if (remainCount == 0) {
                [self reloadViewWithCount:0];
            } else {
                [self.viewModel cleanOOXXTips];
                [self checkAiAiSuggest];
                [self.tableView reloadData];
                [self.viewModel updateOvulatesToDB];
            }
            [self.viewModel.deleteArray removeAllObjects];
            for (IMYToolsOvulateCellVM *cellVM in self.viewModel.ovulateVMsArray) {
                cellVM.tagDelete = @0;
            }
        } afterSecond:0.2];
    } else {
        [UIAlertView imy_showAlertViewWithTitle:IMYString(@"先勾选才能删除哟") message:nil cancelButtonTitle:IMYString(@"知道啦") otherButtonTitles:nil handler:nil];
    }
    return commit;
}

- (void)confirmAdd {
    NSInteger oldCount = self.viewModel.ovulateVMsArray.count;
    if (oldCount == 0) {
        [self reloadViewWithCount:1];
    }
    BOOL isAddLast = [self.viewModel insertData:self.isInvert];
    [self.tableView reloadData];
    
    [self.viewModel updateOvulatesToDB];
    [self.viewModel cleanOOXXTips];
    [self checkAiAiSuggest];
    [self.tableView reloadData];
    NSIndexPath *indexPath = [self.viewModel.addArray firstObject];
    if (isAddLast) {
        [self.tableView scrollToRowAtIndexPath:indexPath atScrollPosition:UITableViewScrollPositionTop animated:YES];
    } else {
        [self.tableView scrollToRowAtIndexPath:indexPath atScrollPosition:UITableViewScrollPositionNone animated:YES];
    }
    @weakify(self);
    [NSObject
     imy_asyncMainBlock:^{
        @strongify(self);
        [self.viewModel.addArray removeAllObjects];
    }afterSecond:0.5];
}

- (void)confirmRemoveWithOriginalIndex:(NSInteger)index newDate:(NSDate *)newDate {
    
    IMYToolsOvulateCellVM *cellVM = self.viewModel.ovulateVMsArray[index];
    [cellVM.ovulate deleteToDB];
    cellVM.imageDateString = [[NSDateFormatter imy_getDateTimeFormater] stringFromDate:newDate];
    
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:index inSection:0];
    [self.viewModel.deleteArray addObject:indexPath];
    
    [self.viewModel removeData];
    [self.tableView deleteRowsAtIndexPaths:self.viewModel.deleteArray withRowAnimation:UITableViewRowAnimationFade];
    [self.viewModel.deleteArray removeAllObjects];
    
    
    NSInteger remainCount = self.viewModel.ovulateVMsArray.count;
    if (remainCount == 0) {
        [self reloadViewWithCount:0];
    } else {
        [self.viewModel updateOvulatesToDB];
        @weakify(self);
        [NSObject
         imy_asyncMainBlock:^{
            @strongify(self);
            [self.viewModel cleanOOXXTips];
            [self checkAiAiSuggest];
            [self.tableView reloadData];
        } afterSecond:0.2];
    }
}

- (void)confirmCover {
    NSInteger coveredIndex = [self.viewModel getCoverIndexWithIsIvert:self.isInvert];
    if (coveredIndex >= 0) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:coveredIndex inSection:0];
        [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
        [self.viewModel cleanOOXXTips];
        [self checkAiAiSuggest];
        [self.tableView reloadData];
        [self.viewModel updateOvulatesToDB];
    }
}


- (void)confirmRemoveWithCoverIndex:(NSInteger)index {
    IMYToolsOvulateCellVM *cellVM = self.viewModel.ovulateVMsArray[index];
    [cellVM deleteOvulate];
    
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:index inSection:0];
    [self.viewModel.deleteArray addObject:indexPath];
    
    [self.viewModel deleteData];
    [self.tableView reloadData];
    [self.viewModel.deleteArray removeAllObjects];
    
    @weakify(self);
    [NSObject
     imy_asyncMainBlock:^{
        @strongify(self);
        [self.viewModel cleanOOXXTips];
        [self checkAiAiSuggest];
        [self.tableView reloadData];
    }
     afterSecond:0.3];
}

- (void)confirmCoverInSamePageFromIndex:(NSInteger)fromIndex newDateString:(NSString *)imageDateString {
    
    IMYToolsOvulateCellVM *originalCellVM = [self.viewModel.ovulateVMsArray objectAtIndex:fromIndex];
    [originalCellVM.ovulate deleteToDB];
    originalCellVM.imageDateString = imageDateString;
    [originalCellVM.ovulate saveToDB];
    
    [self confirmMoveFromIndex:fromIndex newDateString:imageDateString];
}


- (void)checkAiAiSuggest{
    if (!self.viewModel.isLastPage) {  //展示最近一条 用户试纸结果的提示文案
        return;
    }
    IMYToolsOvulateCellVM *cellVM = [self.viewModel.ovulateVMsArray lastObject];
    BOOL showXXOO = NO;
    NSString *xxooTip = nil;
    //结果【强阳】，展示：出现强阳，建议mm月dd日hh时左右安排爱爱，并继续每隔4小时监测一次。（mm月dd日hh时为测出时间+12小时，格式为24小时制）
    if (cellVM.state == IMYToolsOvulateStateYangTwo || cellVM.state == IMYToolsOvulateStateStrongYang) {
        showXXOO = YES;
        NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:cellVM.ovulate.imageDate];
        NSDate *aiaiDate = [currentDate dateByAddingHours:12];
        xxooTip = [NSString stringWithFormat:@"出现强阳，建议%d月%d日%d时左右安排爱爱，并继续每隔4小时监测一次",aiaiDate.month,aiaiDate.day,aiaiDate.hour];
    }else{
        if (self.viewModel.ovulateVMsArray.count > 1) {
            IMYToolsOvulateCellVM *precellVM = [self.viewModel.ovulateVMsArray objectAtIndex:self.viewModel.ovulateVMsArray.count - 2]; ///倒数第二个
            //上次结果【强阳】，本次结果为【 阳 或 弱阳】时，展示：出现强阳转弱，建议mm月dd日hh时左右安排爱爱，提高怀孕几率。（mm月dd日hh时为测出时间+4小时，格式为24小时制）
            if (cellVM.state >= 4 && cellVM.state <= 8 && precellVM.state >= 9) {
                showXXOO = YES;
                NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:cellVM.ovulate.imageDate];
                NSDate *aiaiDate = [currentDate dateByAddingHours:4];
                xxooTip = [NSString stringWithFormat:@"出现强阳转弱，建议%d月%d日%d时左右安排爱爱，提高怀孕几率",aiaiDate.month,aiaiDate.day,aiaiDate.hour];
            }else if (cellVM.state == IMYToolsOvulateStateYangOne && precellVM.state < 9){//结果【阳】，上次结果【非强阳】时，展示：建议持续每隔4小时监测一次，适当安排爱爱
                showXXOO = YES;
                xxooTip = [NSString stringWithFormat:@"建议持续每隔4小时监测一次，适当安排爱爱"];
            }
        }
    }
    CGFloat tableFooterViewHeight = 0;
    [self.tableFooterView imy_removeAllSubviews];
    if (showXXOO) {
        CGFloat viewHeight = [IMYToolsOvulateAiAiSuggestView viewHeightWithText:xxooTip];
        if (_aiaiSuggestView && self.aiaiSuggestView.imy_height != viewHeight) {
            [self.aiaiSuggestView removeFromSuperview];
            _aiaiSuggestView = nil;
        }
        self.aiaiSuggestView.hidden = NO;
        self.aiaiSuggestView.frame = CGRectMake(0, 0, SCREEN_WIDTH, viewHeight);
        tableFooterViewHeight = viewHeight;
        [self.aiaiSuggestView setupViewWithText:xxooTip];
        [self.tableFooterView addSubview:self.aiaiSuggestView];
        [self.aiaiSuggestView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(viewHeight);
        }];
    } else {
        [self.aiaiSuggestView setupViewWithText:@""];
        self.aiaiSuggestView.hidden = YES;
        tableFooterViewHeight = 4;
    }
    
    IMYCKOperateBannerModel *bannerModel = [self bannerModelForOvulate];
    if (cellVM && bannerModel) {
        self.bannerView.hidden = NO;
        [self.bannerView configModel:bannerModel];
        tableFooterViewHeight += showXXOO ? 4 : 0;
        tableFooterViewHeight += (bannerModel.viewHeight + 12);
        [self.tableFooterView addSubview:self.bannerView];
        [self.bannerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(12);
            make.trailing.mas_equalTo(-12);
            make.bottom.mas_equalTo(-12);
            make.height.mas_equalTo(bannerModel.viewHeight);
        }];
        [self.bannerView imy_drawAllCornerRadius:12.0f];
        
        
        //埋点 https://metadata-dp.meiyou.com/scene/info?id=331
        [self.bannerView setClickCompleteBlock:^{
            //点击
            NSDictionary *params2 = @{@"event": @"yy_plsz_hjsyqrk", @"action": @(2), @"info_key": @(bannerModel.biId)};
            [IMYGAEventHelper postWithPath:@"event" params:params2 headers:NULL completed:NULL];
        }];
        
        NSString *eventName = [NSString stringWithFormat:@"IMYCKOperateBannerView_%p", self.bannerView];
        self.imyut_eventInfo.eventName = eventName;
        self.imyut_eventInfo.showRadius = 1.0;
        @weakify(self);
        self.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            //曝光
            NSDictionary *params2 = @{@"event": @"yy_plsz_hjsyqrk", @"action": @(1), @"info_key": @(bannerModel.biId)};
            [IMYGAEventHelper postWithPath:@"event" params:params2 headers:NULL completed:NULL];
        };
        
    }else{
        self.bannerView.hidden = YES;
    }
    self.tableFooterView.imy_height = tableFooterViewHeight;
}

/// 排卵试纸
- (IMYCKOperateBannerModel *)bannerModelForOvulate {
    // 【【协同】运营可以配置与记录/分析结果相关的横幅】
    // https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001279127
    IMYToolsOvulateCellVM *cellVM = self.viewModel.ovulateVMsArray.lastObject;
    if (!cellVM || !cellVM.ovulate) {
        return nil;
    }
    IMYToolsOvulateModel *ovulateModel = cellVM.ovulate;
    IMYRecordBannerActionModel *actionModel = [[IMYRecordBannerActionModel alloc] init];
    actionModel.actionType = IMYRecordSummaryActionTypeOvulationTest;
    actionModel.subType = [ovulateModel recordBannerSubType];
    
    IMYSGRecordBannerConfig *config = [IMYRecordBannerManager configsForItem:99 actionModel:actionModel];
    if (config) {
        IMYCKOperateBannerModel *model = [[IMYCKOperateBannerModel alloc] init];
        model.content = config.title;
        model.subContent = config.subtitle;
        model.uri = config.uri;
        model.image = [IMYPublicAppHelper shareAppHelper].isNight ? config.image_night : config.image;
        
        model.showGradientView = YES;
        model.viewWidth = SCREEN_WIDTH - 24;
        model.viewHeight = SCREEN_By375(64);
        //埋点属性
        //不能设置 model.eventName 属性
        //这边没有使用 IMYCKOperateBannerView 底层的BI曝光与点击事件
        model.biId = config.vip_material_id;
        return model;
    } else {
        return nil;
    }
}

#pragma mark - Delegate && DataSource
#pragma mark UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.ovulateVMsArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYToolsOvulateCell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYToolsOvulateCell" forIndexPath:indexPath];
    IMYToolsOvulateCellVM *cellVM = self.viewModel.ovulateVMsArray[indexPath.row];
    cell.refPage = self;
    NSInteger position = [self cellPositionWithIndex:indexPath.row];
    [cell updateCellLayoutWithPosition:position];
    cellVM.cellIndex = indexPath;
    cellVM.isDeleting = self.isDeleting;
    cellVM.isInvert = self.isInvert;
    cell.viewModel = cellVM;
    @weakify(self, cell);
    [RACObserve(cell, viewModel.tagDelete) subscribeNext:^(NSNumber *hasTag) {
        @strongify(self, cell);
        if (hasTag.boolValue && self.viewModel.deleteArray.count == 0) {
            [self.viewModel updateDeleteArrayWith:hasTag.boolValue cellIndex:cell.viewModel.cellIndex];
        }
        [cell layoutOvulateCell];
    }];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    IMYToolsOvulateCellVM *cellVM = self.viewModel.ovulateVMsArray[indexPath.row];
    if ([self.delegate respondsToSelector:@selector(pageTableCellTapWithCellViewModel:)]) {
        [self.delegate pageTableCellTapWithCellViewModel:cellVM];
    }
}

#pragma mark UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYToolsOvulateCellVM *cellVM = self.viewModel.ovulateVMsArray[indexPath.row];
    cellVM.pageVM = self.viewModel;
    NSInteger position = [self cellPositionWithIndex:indexPath.row];
    CGFloat cellHeight = [self cellHeightWithPosition:position];
    return cellHeight;
}

#pragma mark - action

/*
 判断当前 index cell 的位置
 position = 0 只有一个
 position = 1 多个中的 顶部
 position = 2 多个中的 中间部
 position = 3 多个中的 低部
 */
- (NSInteger)cellPositionWithIndex:(NSInteger)index{
    NSInteger position = 0;
    IMYToolsOvulateCellVM *cellVM_pre = [self cellVMWithIndex:index - 1];
    IMYToolsOvulateCellVM *cellVM_curr = [self cellVMWithIndex:index];
    IMYToolsOvulateCellVM *cellVM_next = [self cellVMWithIndex:index + 1];
    //只有一个cell
    if (!cellVM_pre && !cellVM_next && cellVM_curr) {
        position = 0;
    }else{ //cell 大于一个
        NSDate *imageDate_pre = [[NSDateFormatter imy_getDateTimeFormater] dateFromString:cellVM_pre.imageDateString];
        NSDate *imageDate_curr = [[NSDateFormatter imy_getDateTimeFormater] dateFromString:cellVM_curr.imageDateString];
        NSDate *imageDate_next = [[NSDateFormatter imy_getDateTimeFormater] dateFromString:cellVM_next.imageDateString];
        //最顶部cell
        if (!cellVM_pre && cellVM_next && cellVM_curr) {
            if ([imageDate_next isSameDayAsDate:imageDate_curr]) {  //同一天
                position = 1;
            } else {  //不是同一天
                position = 0;
            }
        }
        
        //最地部cell
        if (cellVM_pre && !cellVM_next && cellVM_curr) {
            if ([imageDate_pre isSameDayAsDate:imageDate_curr]) {  //同一天
                position = 3;
            } else {  //不是同一天
                position = 0;
            }
        }
        // 上 中 下 都有cell 的
        if (cellVM_pre && cellVM_next && cellVM_curr) {
            //跟上下两个cell 时间都不一致
            if (![imageDate_pre isSameDayAsDate:imageDate_curr] && ![imageDate_next isSameDayAsDate:imageDate_curr]) {
                position = 0;
            }
            //跟上一个不一样，跟下一个一样
            if (![imageDate_pre isSameDayAsDate:imageDate_curr] && [imageDate_next isSameDayAsDate:imageDate_curr]) {
                position = 1;
            }
            //跟上一个下一个 都一样
            if ([imageDate_pre isSameDayAsDate:imageDate_curr] && [imageDate_next isSameDayAsDate:imageDate_curr]) {
                position = 2;
            }
            //跟上一个 一样 跟下一个不一样
            if ([imageDate_pre isSameDayAsDate:imageDate_curr] && ![imageDate_next isSameDayAsDate:imageDate_curr]) {
                position = 3;
            }
        }
    }
    return position;
}

- (CGFloat )cellHeightWithPosition:(NSInteger)position{
    CGFloat top_clear_height = 4;
    CGFloat top_white_height = 8;
    CGFloat bottom_white_height = 8;
    CGFloat image_height = 36;
    CGFloat height = 0;
    switch (position) {
        case 1:{
            height = top_clear_height + top_white_height + image_height + bottom_white_height;
            break;
        }
        case 2:{
            height = image_height + bottom_white_height;
            break;
        }
        case 3:{
            height = image_height + bottom_white_height;
            break;
        }
        default: //0
            height = top_clear_height + top_white_height + image_height + bottom_white_height;
            break;
    }
    return height;
}

- (IMYToolsOvulateCellVM *)cellVMWithIndex:(NSInteger)index{
    if (self.viewModel.ovulateVMsArray.count > index) {
        return self.viewModel.ovulateVMsArray[index];
    } else {
        return nil;
    }
}
/// 监听通知变色
- (void)changInvert:(NSNotification *)notification {
    NSNumber *index = [notification object];
    BOOL isInvert = [index boolValue];
    self.isInvert = isInvert;
}


/// 当前页面内的移动
- (void)confirmMoveFromIndex:(NSInteger)fromIndex newDateString:(NSString *)imageDateString {
    
    [self.viewModel updateDataWithIsLast:NO isAdd:NO];
    
    NSInteger toIndex = [self.viewModel moveDataFromIndex:fromIndex withImageDateString:imageDateString];
    
    if (toIndex != -1 && toIndex != fromIndex) {
        NSIndexPath *fromIndexPath = [NSIndexPath indexPathForRow:fromIndex inSection:0];
        NSIndexPath *toIndexPath = [NSIndexPath indexPathForRow:toIndex inSection:0];
        if (self.viewModel.ovulateVMsArray.count > toIndex) {
            [self.tableView scrollToRowAtIndexPath:toIndexPath atScrollPosition:UITableViewScrollPositionNone animated:YES];
            [self.tableView moveRowAtIndexPath:fromIndexPath toIndexPath:toIndexPath];
        }else{
            imy_asyncMainBlock(^{
                [self.tableView reloadData];
            });
        }
        [self.viewModel updateOvulatesToDB];
        [NSObject
         imy_asyncMainBlock:^{
            [self.viewModel cleanOOXXTips];
            [self checkAiAiSuggest];
            [self.tableView reloadData];
        }afterSecond:0.5];
    }
}


#pragma mark - private Methods

- (void)reloadViewWithCount:(NSInteger)count {
    if (count) {
        self.captionView.state = IMYCaptionViewStateHidden;
    } else {
        [self.captionView setTitle:@"当前周期没有记录" andState:IMYCaptionViewStateNoResult];
        self.captionView.state = IMYCaptionViewStateNoResult;
    }
    [self checkAiAiSuggest];
}



#pragma mark - setter && getter
- (void)setIsDeleting:(NSNumber *)isDeleting {
    if (_isDeleting.boolValue != isDeleting.boolValue) {
        _isDeleting = isDeleting;
        if (!isDeleting.boolValue) {
            [self.viewModel.deleteArray removeAllObjects];
            for (IMYToolsOvulateCellVM *cellVM in self.viewModel.ovulateVMsArray) {
                cellVM.tagDelete = @0;
            }
        }
    }
}

- (IMYCaptionView *)captionView{
    if (!_captionView) {
        IMYCaptionView *view = [IMYCaptionView new];
        view.backgroundColor = UIColor.clearColor;
        [view setState:IMYCaptionViewStateHidden];
        _captionView = view;
    }
    return _captionView;
}

- (UIView *)tableFooterView{
    if (!_tableFooterView) {
        UIView *view = [UIView new];
        view.backgroundColor = UIColor.clearColor;
        _tableFooterView = view;
    }
    return _tableFooterView;
}

- (IMYToolsOvulateAiAiSuggestView *)aiaiSuggestView{
    if (!_aiaiSuggestView) {
        IMYToolsOvulateAiAiSuggestView *view = [IMYToolsOvulateAiAiSuggestView new];
        _aiaiSuggestView = view;
    }
    return _aiaiSuggestView;
}

- (IMYCKOperateBannerView *)bannerView {
    if (!_bannerView) {
        _bannerView = [IMYCKOperateBannerView new];
    }
    return _bannerView;
}

- (UIView *)lineView{
    if (!_lineView) {
        UIView *view = [UIView new];
        view.alpha = 0;
        [view imy_setBackgroundColorForKey:kCK_Black_J];
        _lineView = view;
    }
    return _lineView;
}

@end
