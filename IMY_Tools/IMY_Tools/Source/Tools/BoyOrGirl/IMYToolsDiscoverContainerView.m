//
//  IMYToolsDiscoverContainerView.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/4/28.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "IMYToolsDiscoverContainerView.h"

#define titleMargin  10
#define subtitleMargin  6

#define titleFontSize  16
#define subtitleFontSize  12

static NSInteger kNewIconTag = 5027;

@implementation IMYToolsDiscoverContainerView

+ (instancetype)containerViewWithWidth:(NSInteger)width {
    NSInteger height = width + titleMargin + (titleFontSize+2) + subtitleMargin + (subtitleFontSize + 2);
    return [[self alloc] initWithFrame:CGRectMake(0, 0, width, height)];
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        UIButton *lkbutton = [UIButton buttonWithType:UIButtonTypeCustom];
        lkbutton.frame = CGRectMake(0, 0, frame.size.width, frame.size.width);
        self.bt_icon = lkbutton;
        [self addSubview:_bt_icon];
        
        self.lb_title = [[UILabel alloc] initWithFrame:CGRectMake(-50, _bt_icon.imy_bottom + titleMargin, frame.size.width + 100, titleFontSize+2)];
        _lb_title.font = [UIFont systemFontOfSize:titleFontSize];
        _lb_title.backgroundColor = [UIColor clearColor];
        _lb_title.textAlignment = NSTextAlignmentCenter;
        [self addSubview:_lb_title];
        
        self.lb_subtitle = [[UILabel alloc] initWithFrame:CGRectMake(-50, _lb_title.imy_bottom + subtitleMargin, frame.size.width + 100, subtitleFontSize+2)];
        _lb_subtitle.font = [UIFont systemFontOfSize:subtitleFontSize];
        _lb_subtitle.backgroundColor = [UIColor clearColor];
        _lb_subtitle.textAlignment = NSTextAlignmentCenter;
        [self addSubview:_lb_subtitle];
    }
    return self;
}

- (void)setIsNew:(BOOL)isNew {
    UIImageView *newIcon = (UIImageView*)[self viewWithTag:kNewIconTag];
    
    if (isNew) {
        if (newIcon == nil) {
            newIcon = [[UIImageView alloc] initWithImage:[UIImage imy_imageForKey:@"all_newsbigbg.png"]];
            [newIcon sizeToFit];
            
            UILabel *newTxt = [[UILabel alloc] initWithFrame:CGRectMake(4, 1, 25, 15)];
            newTxt.font = [UIFont systemFontOfSize:12];
            newTxt.backgroundColor = [UIColor clearColor];
            [newTxt imy_setTextColorForKey:kCK_White_A];
            newTxt.textAlignment = NSTextAlignmentCenter;
            newTxt.text = @"new";
            [newTxt sizeToFit];
            [newIcon addSubview:newTxt];
            newIcon.tag = kNewIconTag;
            [self addSubview:newIcon];
            
            @weakify(self);
            [newIcon mas_makeConstraints:^(MASConstraintMaker *make) {
                @strongify(self);
                make.centerX.equalTo(self.mas_right).with.offset(-5);
                make.top.equalTo(self).with.mas_equalTo(8);
            }];
        }
    } else {
        if (newIcon != nil) {
            UIView *subView = [self imy_findViewWithTag:kNewIconTag];
            if (subView) {
                [subView removeFromSuperview];
            }
        }
    }
}

@end
