//
//  IMYToolsCategoryVC.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/4/28.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "IMYToolsCategoryVC.h"
#import "IMYToolDataManager.h"
#import "IMYToolModel.h"
#import "IMYToolsDiscoverContainerView.h"
#import "IMYToolsInner.h"
#import "IMYToolsURIRunner.h"

@interface IMYToolsCategoryVC ()
@property (nonatomic, strong) UIScrollView *contentView;
@property (nonatomic, strong) IMYCaptionView *captionView;

@property (nonatomic, assign) IMYToolsCategoryType type;
@property (nonatomic, strong) NSArray *models;
@property (nonatomic, assign) IMYVKUserMode userMode;
@property (nonatomic, assign) BOOL isDemo;
@end

@implementation IMYToolsCategoryVC

- (instancetype)initWithType:(IMYToolsCategoryType)type {
    if (self = [super init]) {
        self.isDemo = YES;
        self.type = type;
        self.isWhiteNavigationBar = YES;
    }
    return self;
}

- (void)setType:(IMYToolsCategoryType)type {
    if (type == IMYToolsCategoryTypeBoyOrGirl) {
        self.title = IMYString(@"生男生女");
        IMYVKUserMode userMode = [IMYToolsUserService userMode];
        if (userMode == IMYVKUserModePregnancy) {
            self.categoryId = 1;
        } else if (userMode == IMYVKUserModeForPregnant) {
            self.categoryId = 7;
        } else {
            //另外这二种，不知道categoryID为多少，就传1吧
            self.categoryId = 1;
        }
        self.subTitleColor = [UIColor imy_colorWithHexString:@"72b5ff"];
    } else if (type == IMYToolsCategoryTypePregnancy) {
        self.title = IMYString(@"孕期工具");
        self.categoryId = 4;
        self.subTitleColor = IMY_COLOR_KEY(kIMY_Red);
        self.userMode = IMYVKUserModePregnancy;
    } else if (type == IMYToolsCategoryTypeLama) {
        self.title = IMYString(@"育儿工具");
        self.categoryId = 11;
        self.subTitleColor = IMY_COLOR_KEY(kIMY_Red);
        self.userMode = IMYVKUserModeLama;
    } else if (type == IMYToolsCategoryTypeForPregnant) {
        self.title = IMYString(@"备孕工具");
        self.categoryId = 17;
        self.subTitleColor = IMY_COLOR_KEY(kIMY_Red);
        self.userMode = IMYVKUserModeForPregnant;
    }
}

- (void)viewWillAppear:(BOOL)animated {
    //    [IMYEventHelper event:@"fx-gjfl" addType:0 attributes:@{IMYString(@"来源") : self.navigationItem.title}];
    [super viewWillAppear:animated];
}

- (void)loadView {
    [super loadView];
    self.contentView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
    self.contentView.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
    self.contentView.showsHorizontalScrollIndicator = NO;
    [self.view imy_setBackgroundColor:kIMY_BG];
    [self.view addSubview:_contentView];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupCaptionView];
    [self initData];
    if (self.models.count > 0) {
        [self initViews];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - view

- (void)setupCaptionView {
    self.captionView = [IMYCaptionView addToView:self.view];
    @weakify(self)
        self.captionView.retryBlock = ^{
        @strongify(self)
            [self requestNewTools];
    };
    self.captionView.state = IMYCaptionViewStateHidden;
}

- (void)initViews {
    NSInteger imageWidth = ceil(90 * (SCREEN_WIDTH / 320));

    for (UIView *subview in _contentView.subviews) {
        subview.hidden = YES;
    }
    NSInteger lastViewBottom = 0;
    NSUInteger allrows = _models.count / 2;
    if (_models.count % 2) {
        allrows++;
    }

    for (NSInteger i = 0; i < _models.count; i++) {
        IMYToolModel *model = _models[i];
        UIButton *categoryView = (id)[_contentView viewWithTag:100 + i];
        IMYToolsDiscoverContainerView *iconView = (id)[categoryView viewWithTag:1000];
        if (categoryView == nil) {
            categoryView = [[UIButton alloc] initWithFrame:CGRectMake((i % 2) * SCREEN_WIDTH / 2, (i / 2) * SCREEN_WIDTH / 2, SCREEN_WIDTH / 2, SCREEN_WIDTH / 2)];

            //            UIImage *normalImage = [UIImage imy_imageFromColor:[UIColor colorWithRed:250/255.0 green:250/255.0 blue:250/255.0 alpha:1] andSize:categoryView.imy_size];
            UIImage *normalImage = [UIImage imy_imageFromColor:IMY_COLOR_KEY(kIMY_White) andSize:categoryView.imy_size];

            [categoryView setBackgroundImage:normalImage forState:UIControlStateNormal];
            UIImage *highlightedImage = [UIImage imy_imageFromColor:IMY_COLOR_KEY(kIMY_White_Highlighted) andSize:categoryView.imy_size];
            [categoryView setBackgroundImage:highlightedImage forState:UIControlStateHighlighted];

            [categoryView addTarget:self action:@selector(bt_action_pressed:) forControlEvents:UIControlEventTouchUpInside];
            [iconView setImyut_exposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
                NSString *url = model.localToolURL;
                if (!url) {
                    url = model.webToolURL;
                }
                if ([url isEqualToString:@"tools/dailyVote"]) {
                    [[IMYURIManager shareURIManager] runActionWithPath:@"record/bi_record"
                                                                params:@{ @"item_id": @130,
                                                                          @"position": @4,
                                                                          @"action": @1 }
                                                                  info:nil];
                }
            }];

            iconView = [IMYToolsDiscoverContainerView containerViewWithWidth:imageWidth];
            iconView.userInteractionEnabled = NO;
            iconView.tag = 1000;
            categoryView.imy_size = CGSizeMake(SCREEN_WIDTH / 2, SCREEN_WIDTH / 2);
            iconView.lb_title.textColor = [UIColor blackColor];
            iconView.center = CGPointMake(categoryView.imy_width / 2, categoryView.imy_height / 2);
            [categoryView addSubview:iconView];

            //底部的线;最底下那条线不画了
            UIImageView *line = nil;
            if (i / 2 != allrows - 1) {
                line = [[UIImageView alloc] initWithFrame:CGRectMake(0, categoryView.imy_height - 1, categoryView.imy_width, 1)];
                [line imy_setImage:@"all_lineone"];
                [categoryView addSubview:line];
            }
            //右边的线 靠右的就不画了
            if (i % 2 == 0) {
                line = [[UIImageView alloc] initWithFrame:CGRectMake(categoryView.imy_width - 1, 0, 1, categoryView.imy_height)];
                [line imy_setImage:@"all_linetow"];
                [categoryView addSubview:line];
            }

            [iconView.lb_title imy_setTextColorForKey:kCK_Black_A];
            iconView.lb_subtitle.textColor = self.subTitleColor;
        }
        categoryView.hidden = NO;

        if ([model.icon hasPrefix:@"http"]) {
            NSURL *url = [NSURL URLWithString:[NSString qiniuURL:model.icon type:IMY_QiNiu_WEBP]];
            [iconView.bt_icon sd_setBackgroundImageWithURL:url forState:UIControlStateNormal placeholderImage:[UIImage imy_imageForKey:@"tata_greyicon"]];
        } else {
            [iconView.bt_icon imy_setBackgroundImage:model.icon highl:nil];
        }
        iconView.lb_title.text = model.title;
        iconView.lb_subtitle.text = model.subTitle;
        if (model.isNew) {
            [iconView setIsNew:[IMYToolDataManager getLastUsedVersionById:model.toolId] < model.editTime];
        } else {
            [iconView setIsNew:NO];
        }
        categoryView.tag = 100 + i;
        [self.contentView addSubview:categoryView];

        lastViewBottom = categoryView.imy_bottom + 10;
    }
    self.contentView.contentSize = CGSizeMake(0, MAX(lastViewBottom, SCREEN_HEIGHT - 64 - 49 + 0.5));
}

#pragma mark - data

- (void)initData {
    NSData *fileData = [IMYToolDataManager getCategoryDataWithId:self.categoryId];
    if (fileData.length > 0) {
        self.models = [fileData toModels:[IMYToolModel class]];
    }
    [self requestNewTools];
}

- (void)requestNewTools {
    if (self.models.count == 0) {
        self.captionView.state = IMYCaptionViewStateLoading;
    }

    if (!self.isDemo) {
        self.userMode = [IMYToolsUserService userMode];
    }

    NSDictionary *param = @{ @"catid": @(self.categoryId),
                             @"mode": @(self.userMode) };
    @weakify(self)

        [IMYToolNetworking getWithHost:tools_seeyouyima_com
            path:@"tools"
            parameters:param
            successBlock:^(IMYToolNetworkingOperation *operation, id responseObject) {
                @strongify(self);
                NSArray *array = [responseObject toModels:[IMYToolModel class]];
                if (array.count > 0) {
                    self.models = array;
                    [self initViews];
                }
                self.captionView.state = IMYCaptionViewStateHidden;
            }
            failedBlock:^(id error) {
                @strongify(self);
                if (self.models.count == 0) {
                    self.captionView.state = IMYCaptionViewStateRetry;
                }
            }];
}

- (void)bt_action_pressed:(UIButton *)bt {
    NSInteger index = bt.tag - 100;
    IMYToolModel *model = _models[index];

    if (97 == model.toolId) {
        //孕酮参考值
        [IMYEventHelper event:@"yqgj-yt"];
    } else if (98 == model.toolId) {
        //HCG查询
        [IMYEventHelper event:@"yqgj-hcg"];
    }

    if (model.isNew && [IMYToolDataManager getLastUsedVersionById:model.toolId] < model.editTime) {
        IMYToolsDiscoverContainerView *view = (id)[bt viewWithTag:1000];
        if ([view isKindOfClass:[IMYToolsDiscoverContainerView class]]) {
            [view setIsNew:NO];
        }
        [IMYToolDataManager saveLastUsedVersionById:model.toolId version:model.editTime];
    }
    [IMYToolsURIRunner pushToolWithModel:model];
}

@end
