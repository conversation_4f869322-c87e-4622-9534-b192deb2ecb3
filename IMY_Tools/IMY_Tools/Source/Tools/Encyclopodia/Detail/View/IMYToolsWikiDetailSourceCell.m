//
//  IMYToolsWikiDetailSourceCell.m
//  BabyBluetooth
//
//  Created by sjd on 2022/10/14.
//

#import "IMYToolsWikiDetailSourceCell.h"
#import "UILabel+Tools.h"

@interface IMYToolsWikiDetailSourceCell ()
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIView *openView;

@property (nonatomic, strong) IMYToolsWikiCellModel *cellModel;

@end

@implementation IMYToolsWikiDetailSourceCell

- (void)setupUI {
    self.clipsToBounds = YES;
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.contentLabel];
    [self.contentView addSubview:self.openView];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(16);
    }];

    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(12);
        make.trailing.mas_equalTo(-12);
        make.top.mas_equalTo(42);
    }];


    [self.openView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(12);
        make.trailing.mas_equalTo(-12);
        make.bottom.mas_equalTo(0);
        make.top.mas_equalTo(68);
    }];
}

-(void)setupWithCellModel:(IMYToolsWikiCellModel *)cellModel {
    self.cellModel = cellModel;
    [self.contentLabel tools_setText:cellModel.content lineHeight:26];
    if (cellModel.rowHeight == cellModel.rowHeightOther || cellModel.useRowHeightOther == NO) {
        self.openView.hidden = YES;
    } else {
        self.openView.hidden = NO;
    }
    IMYLineView *line = [self imy_lineViewWithDirection:IMYDirectionUp show:YES margin:12];
    line.imy_width = SCREEN_WIDTH - 24;
}

- (void)openContentClick {
    if (self.openClickBlock) {
        self.openClickBlock();
    }
    self.cellModel.useRowHeightOther = NO;
    [self animation];
}

-(void)animation {
    UITableView *tableView = [self imy_findParentViewWithClass:[UITableView class]];
    [UIView animateWithDuration:0.2 animations:^{
        [[tableView visibleCells] makeObjectsPerformSelector:@selector(layoutIfNeeded)];
    }];
//    //控制吸底
//    CGPoint contentOffset = tableView.contentOffset;
//    contentOffset.y -= 1;
//    tableView.contentOffset = contentOffset;

    [self setupWithCellModel:self.cellModel];
    self.openView.hidden = NO;
    [UIView animateWithDuration:0.25 delay:0.0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        self.openView.alpha = 0;
    } completion:^(BOOL finished) {
        self.openView.hidden = YES;
        self.openView.alpha = 1;
    }];
    [tableView beginUpdates];
    [tableView endUpdates];
}

-(UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = UILabel.new;
        _titleLabel.font = [UIFont imy_regularWith:12];
        [_titleLabel imy_setTextColorForKey:kCK_Black_B];
        _titleLabel.text = @"参考文献";
    }
    return _titleLabel;
}

-(UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = UILabel.new;
        _contentLabel.font = [UIFont imy_regularWith:12];
        [_contentLabel imy_setTextColorForKey:kCK_Black_B];
        _contentLabel.numberOfLines = 0;
    }
    return _contentLabel;
}

- (UIView *)openView {
    if (!_openView) {
        _openView = UIView.new;
        [_openView imy_setBackgroundColorForKey:kCK_White_AN];
        
        IMYButton *openBtn = IMYButton.new;
        [openBtn imy_setTitle:@"展开"];
        [openBtn imy_setTitleColor:@"#4F7CAE"];
        [openBtn imy_setImage:@"wiki_icon_list_more"];
        openBtn.imageAtDirection = IMYDirectionRight;
        openBtn.offset = 4;
        openBtn.titleLabel.font = [UIFont imy_regularWith:12];
        [openBtn addTarget:self action:@selector(openContentClick) forControlEvents:UIControlEventTouchUpInside];
        
        [_openView addSubview:openBtn];
        [openBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.top.mas_equalTo(0);
            make.height.mas_equalTo(26);
        }];
    }
    return _openView;
}

@end
