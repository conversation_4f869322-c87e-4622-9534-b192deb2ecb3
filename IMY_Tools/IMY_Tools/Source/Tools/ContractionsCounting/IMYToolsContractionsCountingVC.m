//
// Created by lins<PERSON><PERSON> on 15/1/23.
// Copyright (c) 2015 linggan. All rights reserved.
//

#import "IMYToolsContractionsCountingVC.h"
#import "IMYToolsContractionsDataManager.h"
#import "IMYToolsFMCountingCell.h"
#import "IMYToolsHelpInfoViewController.h"
#import <IMYYQBasicServices/NSDate+IMYTools.h>
#import "IMYToolsCCAllDataVC.h"
#import <IMYCommonKit/IMYRequestOperateService.h>
#import <IMYCommonKit/IMYRequestOperateService+Filter.h>


NSString *TOOLS_CCTIP_FIRST = nil;
NSString *TOOLS_CCTIP_WITHIN5S = nil;
NSString *TOOLS_CCTIP_INTERVALWITHIN10S = nil;
NSString *TOOLS_CCTIP_INTERVALWITHIN5S = nil;
NSString *TOOLS_CCTIP_BETWEEN10STO5MIN = nil;
NSString *TOOLS_CCTIP_BETWEEN5MINTO10MIN = nil;
NSString *TOOLS_CCTIP_BETWEEN10MINLESS = nil;
NSString *TOOLS_CCTIP_DEFAULT = nil;
NSString *TOOLS_CCTIP_LASTFOR3MIN = nil;
NSString *TOOLS_CCTIP_LASTFOR10MIN = nil;
//NSString *TOOLS_CCTIP_OPERATION          = nil;
//NSString *TOOLS_CCTIP_OPERATION2         = nil;
NSString *TOOLS_CCTIP_LASTRECORD = nil;

@interface IMYToolsContractionsCountingVC () <UITableViewDataSource, UITableViewDelegate, IMYFakeNavigationBarTransitionDelegate>
@property (nonatomic, strong) UIView *headerView;
@property (strong, nonatomic) UITableView *tableView;
@property (strong, nonatomic) UIView *animBox;

@property (strong, nonatomic) UILabel *labelTapToStop;
@property (strong, nonatomic) UILabel *labelTiming;
@property (strong, nonatomic) UILabel *labelTapToBegin;

@property (strong, nonatomic) UILabel *tipLabel;
@property (strong, nonatomic) UIView *tipBg;
@property (nonatomic, strong) UIView *tabelHeaderView;
@property (nonatomic, strong) UILabel *tabelFooterView;

@property (nonatomic, assign) BOOL isDeleteUpdate;  //delete辅助参数

@property (nonatomic, assign) BOOL isFirstUse; //是不是第一次使用
@property (nonatomic, strong) UIView *useGuideView;
@property (nonatomic, assign) BOOL isBebound10Min;      //是否超过10分钟
@property (nonatomic, strong) UIButton *helpInfoButton; //帮组说明button

@property (nonatomic, strong) NSNumber *end_type;
@property (nonatomic, strong) NSNumber *start_type;
@property (nonatomic, assign) NSTimeInterval startTimeStamp;
@property (nonatomic, assign) BOOL isCounting;;

@property (nonatomic, strong) IMYCaptionViewV2 *captionView;

@property (nonatomic, strong) IMYToolsContractionsDataManager *viewModel;


@property (strong, nonatomic) UILabel *tipWidgetLabel;
@property (strong, nonatomic) UIView *tipWidgetBg;
@property (nonatomic, strong) IMYRequestOperateService *operateService;
@property (nonatomic, strong) IMYReportOperateModel *operateModel;

@end

static NSString *fmcell = @"fmcell";

#define kHeaderViewYOffSet (246.0 - 145.0);
#define kIsNotFirstUseContractionCounting @"kIsNotFirstUseContractionCounting"
#define kBottonHeight (255.0)


@implementation IMYToolsContractionsCountingVC {
    BOOL _isInited;
    BOOL _isIniting;

    float _emptyRowHeight;
    NSMutableArray *_sortedKey;
    IMYToolsContractionDataModel *_currentModel;

    NSTimer *_timer;
    NSInteger _cachedDataRowCount;
    NSUInteger _recordCountWhileDisappear;
    CGFloat _widthFactory;
    CGFloat _heightFactory;
    CGFloat _headerViewHeight;
    CGFloat _bottomHeight;
}

- (void)dealloc {
    [[IMYRequestOperateService sharedManager] clearAllCacheData];
}

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        self.isWhiteNavigationBar = NO;
        self.hideNavBarBottomLine = YES;
        self.title = IMYString(@"数宫缩");
        _isInited = NO;
        [self setFactory];
        
        @weakify(self);
        
        [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillEnterForegroundNotification object:nil] distinctUntilChanged] subscribeNext:^(id x) {
            @strongify(self);
            if (self.isCounting) {
                self.end_type = @(3);
                [self uploadGA];
                self.start_type = @(1);
            }
        }];
        
        [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil] distinctUntilChanged] subscribeNext:^(id x) {
            @strongify(self);
            if (self.isCounting) {
                self.end_type = @(2);
                [self uploadGA];
                self.start_type = @(2);
            }
        }];

    }

    return self;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController.navigationBar setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Red_BN) andSize:CGSizeMake(1, 1)] forBarMetrics:UIBarMetricsDefault];
    NSMutableDictionary *textDic = [[NSMutableDictionary alloc] initWithDictionary:[IMYAppBaseStyle sharedInstance].navigationBarStyle.titleAttribute];
    textDic[NSForegroundColorAttributeName] = IMY_COLOR_KEY(kCK_White_A);
    [self.navigationController.navigationBar setTitleTextAttributes:textDic];
    [self.navigationController.navigationBar setTintColor:IMY_COLOR_KEY(kCK_White_A)];
    [self.imy_topLeftButton imy_setImage:@"nav_btn_back"];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    if (!_isInited) {
        [self preparyData];
    } else {
        // reload
        imy_asyncBlock(^{
            imy_asyncMainBlock(^{
                [self initSortedKey];
                [self checkIsEmpty];
                [self.tableView reloadData];

                if (_recordCountWhileDisappear != self.viewModel.linearedAllData.count) {
                    [self setTipText:TOOLS_CCTIP_FIRST];
                }
            });
        });
    }

    self.isFirstUse = [[[IMYUserDefaults standardUserDefaults] valueForKey:kIsNotFirstUseContractionCounting] boolValue];

    //是否展示引导页
    if (!_sortedKey.count) {
        if (!self.isFirstUse) {
            [self showUseGuideView];
        }
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];

    _recordCountWhileDisappear = self.viewModel.linearedAllData.count;

    if (self.useGuideView) {
        [self removeUseGuideView];
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}


#pragma mark - init view

- (void)loadView {

    [super loadView];
    [self ensureStringInited];

    [self initHeaderView];
    [self addHeaderViewSubViews];

    [self.view addSubview:self.tabelHeaderView];
    [self initTableView];

    [self initNavigateBar];

    [self setTipText:TOOLS_CCTIP_FIRST];
    [self updateLabelVisible];

    //显示开始按钮的时候都是第一条提示
    @weakify(self);
    [RACObserve(self.labelTapToBegin, hidden) subscribeNext:^(id x) {
        @strongify(self);
        if (![x boolValue]) {
            [self setTipText:TOOLS_CCTIP_FIRST];
        }
    }];
}

#pragma - mark headerView
- (void)initHeaderView {

    UIView *aHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, _headerViewHeight)];
    //    [aHeaderView imy_setBackgroundColorForKey:kIMY_Pink];
    aHeaderView.backgroundColor = [UIColor clearColor];
    self.headerView = aHeaderView;
    [self.view addSubview:self.headerView];
}

#pragma - mark use guide
- (void)showUseGuideView {

    self.useGuideView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    self.useGuideView.backgroundColor = [UIColor clearColor];

    UIView *backView = [[UIView alloc] initWithFrame:self.useGuideView.frame];
    [backView imy_addThemeChangedBlock:^(UIView *weakObject) {
        if (IMYPublicAppHelper.shareAppHelper.isNight) {
            weakObject.backgroundColor = IMY_COLOR_KEY(@"000000");
        } else {
            weakObject.backgroundColor = IMY_COLOR_KEY(@"323232");
        }
    }];
    backView.alpha = 0.5;
    [self.useGuideView addSubview:backView];


    UIWindow *window = [[UIApplication sharedApplication] keyWindow];
    [window addSubview:self.useGuideView];

    //mask
    CAShapeLayer *mask = [CAShapeLayer layer];
    mask.bounds = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    mask.fillRule = kCAFillRuleEvenOdd; //反向裁剪
    UIBezierPath *maskLayerPath = [UIBezierPath bezierPathWithRect:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    CGRect rect1 = [self.headerView convertRect:self.animBox.frame toView:window];
    rect1.origin.x -= 10 * _heightFactory;
    rect1.origin.y -= 10 * _heightFactory;
    rect1.size.width += 20 * _heightFactory;
    rect1.size.height += 20 * _heightFactory;
    [maskLayerPath appendPath:[UIBezierPath bezierPathWithOvalInRect:rect1]];
    mask.path = maskLayerPath.CGPath;
    mask.position = self.useGuideView.center;
    self.useGuideView.layer.mask = mask;

    //round image
    UIImageView *roundImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 177 * _heightFactory, 177 * _heightFactory)];
    roundImgView.contentMode = UIViewContentModeScaleAspectFit;
    roundImgView.center = CGPointMake(self.useGuideView.imy_centerX,
                                      rect1.origin.y + rect1.size.height / 2.0);
    [roundImgView imy_setImage:@"img_sgs_round"];
    [self.useGuideView addSubview:roundImgView];

    //arrows image
    UIImageView *arrowsImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 42 * _heightFactory, 54 * _heightFactory)];
    arrowsImgView.contentMode = UIViewContentModeScaleAspectFit;
    [arrowsImgView imy_setImage:@"img_sgs_arrow"];
    arrowsImgView.imy_top = roundImgView.imy_bottom + 8;
    arrowsImgView.imy_centerX = roundImgView.imy_centerX;
    [self.useGuideView addSubview:arrowsImgView];

    //text image
    UIImageView *textImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 224 * _heightFactory, 40 * _heightFactory)];
    textImgView.contentMode = UIViewContentModeScaleAspectFit;
    [textImgView imy_setImage:@"img_sgs_text"];
    textImgView.imy_left += roundImgView.imy_left + 20 * _heightFactory;
    textImgView.imy_top += arrowsImgView.imy_bottom + 8 * _heightFactory;
    [self.useGuideView addSubview:textImgView];

    //button
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.frame = CGRectMake(0, 0, 114 * _heightFactory, 47 * _heightFactory);
    button.imy_centerX = self.useGuideView.imy_centerX;
    button.imy_top = textImgView.imy_bottom + 63 * _heightFactory;
    [button imy_setImage:@"all_bt_know" state:UIControlStateNormal];
    [button addTarget:self action:@selector(removeUseGuideView) forControlEvents:UIControlEventTouchUpInside];
    [self.useGuideView addSubview:button];
}

- (void)removeUseGuideView {
    [IMYEventHelper event:@"sgs-wzdl"];
    [self.useGuideView removeFromSuperview];
    self.useGuideView = nil;
    [[IMYUserDefaults standardUserDefaults] setObject:@(YES) forKey:kIsNotFirstUseContractionCounting];
    [[IMYUserDefaults standardUserDefaults] synchronize];
    self.isFirstUse = YES;
}

- (void)initTableView {

    UITableView *table = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    [self.view addSubview:table];

    table.imy_top = self.tabelHeaderView.imy_bottom;
    table.imy_height = _bottomHeight - self.tabelHeaderView.imy_height + 5;

    _tableView = table;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.backgroundColor = [UIColor clearColor];
    [_tableView registerClass:[IMYToolsFMCountingCell class] forCellReuseIdentifier:fmcell];
    _tableView.showsVerticalScrollIndicator = YES;
    [_tableView imy_setBackgroundColor:kIMY_White];
    _tableView.tableFooterView = self.tabelFooterView;
    _tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0.1f)];
}

- (UIView *)animBox {

    if (_animBox == nil) {
        CGRect bg_frame = CGRectMake(0, 0, 133 * _heightFactory, 133 * _heightFactory);
        _animBox = [[UIView alloc] initWithFrame:bg_frame];
        _animBox.imy_centerX = self.headerView.imy_centerX;
        _animBox.imy_bottom = self.headerView.imy_height - 70 * _heightFactory;
        _animBox.layer.cornerRadius = _animBox.imy_height / 2.0;
        _animBox.clipsToBounds = YES;
        [_animBox imy_addThemeChangedBlock:^(UIView *weakObject) {
            if (IMYPublicAppHelper.shareAppHelper.isNight) {
                weakObject.backgroundColor = IMY_COLOR_KEY(kCK_Black_E);
            } else {
                weakObject.backgroundColor = [IMY_COLOR_KEY(kCK_White_A) colorWithAlphaComponent:0.85];
            }
        }];
        

        if (SCREEN_HEIGHT == 480) {
            _animBox.imy_top += 20;
        } else if (SCREEN_HEIGHT == 568) {
            _animBox.imy_top += 20;
        }

        if (iPhoneX) {
            _animBox.imy_top += 20;
        }
    }
    return _animBox;
}

- (void)addHeaderViewSubViews {

    [self.headerView addSubview:self.animBox];
    [self addAnimBoxSubViews];
    [self initTextTip];
    [self.headerView addSubview:self.tipBg];
    [self initWidgetTip];
}

- (void)initWidgetTip{
    self.tipWidgetBg = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 48)];
    [self.tipWidgetBg imy_drawAllCornerRadius:12];
    [self.tipWidgetBg imy_setBackgroundColorForKey:kCK_White_AN];
    self.tipWidgetBg.imyut_eventInfo.eventName = @"IMYToolsContractionsCountingVC.tipWidgetBg";
    @weakify(self);
    [self.tipWidgetBg bk_whenTapped:^{
        @strongify(self);
        if (imy_isNotBlankString(self.operateModel.uri)) {
            [[IMYURIManager sharedInstance] runActionWithString:self.operateModel.uri];
        }
        [IMYGAEventHelper postWithPath:@"event"
                                params:@{@"event": @"yy_yywrk",
                                         @"action": @(2),
                                         @"public_type": MODULE_CONTRACTION,
                                         @"public_info": self.operateModel.moduleKey ?:@"",
                                         @"info_type": @(99),
                                         @"info_id": @(self.operateModel.imyId),
                                         @"info_key":@(self.operateModel.label)
                                       } headers:nil completed:nil];
    }];
    self.tipWidgetBg.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        [IMYGAEventHelper postWithPath:@"event"
                                params:@{@"event": @"yy_yywrk",
                                         @"action": @(1),
                                         @"public_type": MODULE_CONTRACTION,
                                         @"public_info": self.operateModel.moduleKey ?:@"",
                                         @"info_type": @(99),
                                         @"info_id": @(self.operateModel.imyId),
                                         @"info_key":@(self.operateModel.label)
                                       } headers:nil completed:nil];
    };

    
    [self.headerView addSubview:self.tipWidgetBg];
    [self.tipWidgetBg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12);
        make.right.mas_equalTo(-12);
        make.height.mas_equalTo(48);
        make.bottom.mas_equalTo(-8);
    }];
    
    
    UIImageView *arrowImgView = [[UIImageView alloc] init];
    [arrowImgView imy_setImage:@"icon_hint_arrow"];
    [self.tipWidgetBg addSubview:arrowImgView];
    [arrowImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-12);
        make.centerY.equalTo(self.tipWidgetBg);
        make.width.height.mas_equalTo(14);
    }];
    
    self.tipWidgetLabel = [UILabel new];
    self.tipWidgetLabel.font = [UIFont imy_regularWith:17];
    [self.tipWidgetLabel imy_setTextColor:kCK_Black_A];
    self.tipWidgetLabel.text = @"数胎动小组件上新啦！快来试试看";
    [self.tipWidgetBg addSubview:self.tipWidgetLabel];
    [self.tipWidgetLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12);
        make.centerY.equalTo(self.tipWidgetBg);
        make.right.mas_equalTo(arrowImgView.mas_left).offset(-4);
    }];
    self.tipWidgetBg.hidden = YES;
    [self requestWidgetTipData];
}

- (void)requestWidgetTipData {
    IMYVKUserMode mode = [IMYPublicAppHelper shareAppHelper].userMode;
    NSDictionary *params = @{@"page_key" : MODULE_CONTRACTION, @"mode":@(mode)};
    @weakify(self)
    [self.operateService requestWith:params completeBlock:^(NSError * _Nullable error, NSArray<IMYReportOperateModel *> * _Nullable models, NSDictionary * _Nonnull dict, BOOL isCache) {
        @strongify(self);
        if (models.count <= 0) {
            imy_asyncMainBlock(^{
                self.tipWidgetBg.hidden = YES;
            });
            return;
        }
        self.operateModel = models.firstObject;
        imy_asyncMainBlock(^{
            self.tipWidgetLabel.text = self.operateModel.content;
            self.tipWidgetBg.hidden = NO;
        });
    }];
}

- (void)helpInfoButtonAction:(UIButton *)button {
    [IMYEventHelper event:@"sgs-bzsm"];
    NSArray *titles = @[IMYString(@"操作说明"),
                        IMYString(@"颜色说明"),
                        IMYString(@"什么是宫缩"),
                        IMYString(@"表现形式"),
                        IMYString(@"注意事项"),
                        IMYString(@"缓解方法")];

    NSArray *contens = @[IMYString(@"阵痛开始时，请点击计时器，停止阵痛后再次点击记录结束时间。\n如果误操作，可左滑记录进行删除。"),
                         IMYString(@"红色代表间隔时间小于5分钟：马上有喜。"),
                         IMYString(@"宫缩是临产的一个重要特征，简而言之，就是有规则的子宫收缩即宫缩。"),
                         IMYString(@"分娩前数周，子宫肌肉较敏感，将会出现不规则的子宫收缩，持续的时间短，力量弱，或只限于子宫下部。经数小时后又停止，不能使子宫颈口张开，故并非临产，称为假阵缩。而临产的子宫收缩，是有规则性的。初期间隔时间大约是10分钟一次，孕妇感到腹部阵痛，随后阵痛的持续时间逐渐延长，至40秒~60秒。程度也随之加重，间隔时间缩短，约3~5分钟。当子宫收缩出现腹痛时，可感到下腹部很硬。"),

                         IMYString(@"大约在分娩前一个月，宫缩就已经开始了。有些人刚开始时还没感觉，只有用手去摸肚子时，才会感受到宫缩，而且孕妈妈会感觉宫缩频率越来越高。一般计算宫缩时，如果每小时宫缩次数在10次左右就属于比较频繁的，应及时去医院，在医生指导下服用一些抑制宫缩的药物，以预防早产的发生。\n如果宫缩次数不是很频繁，没有腹痛，注意休息就可以了。\n需要注意的是，不要自行用药，而且服用药物一般也不能缓解，这时，孕妈妈要注意休息，尤其不能刺激腹部。假如，宫缩伴有较强烈的腹痛，比如，痛到坐立不安、工作和生活受到影响，就需要去医院就诊。"),
                         IMYString(@"第一步，平卧，闭目，以鼻深吸气;\n第二步，以口呼气，放松腹部;\n第三步，以鼻吸气后，屏气10秒左右，然后以口长呼气。")];

    IMYToolsHelpInfoViewController *vc = [[IMYToolsHelpInfoViewController alloc] initWithTitles:titles contents:contens];
    [self imy_push:vc];
}

- (UILabel *)labelTapToBegin {

    if (_labelTapToBegin == nil) {

        _labelTapToBegin = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 60, 40)];
        _labelTapToBegin.backgroundColor = [UIColor clearColor];
        _labelTapToBegin.textAlignment = NSTextAlignmentCenter;
        _labelTapToBegin.font = [UIFont systemFontOfSize:30 * _heightFactory];
        [_labelTapToBegin imy_setTextColorForKey:kCK_Red_B];
        _labelTapToBegin.text = IMYString(@"开始");
        [_labelTapToBegin sizeToFit];
        _labelTapToBegin.imy_centerX = self.animBox.imy_width / 2.0;
        _labelTapToBegin.imy_centerY = self.animBox.imy_height / 2.0;
    }

    return _labelTapToBegin;
}

- (UILabel *)labelTiming {

    if (_labelTiming == nil) {

        _labelTiming = [[UILabel alloc] initWithFrame:CGRectMake(0, 50 * _heightFactory, 100, 34)];
        _labelTiming.backgroundColor = [UIColor clearColor];
        _labelTiming.textAlignment = NSTextAlignmentCenter;
        [_labelTiming imy_setTextColorForKey:kCK_Red_B];
        _labelTiming.font = [UIFont systemFontOfSize:34 * _heightFactory];
        _labelTiming.text = IMYString(@"00:00");
        [_labelTiming sizeToFit];
        _labelTiming.imy_centerY = self.animBox.imy_height / 2.0;
        _labelTiming.imy_width += 10;
        _labelTiming.imy_centerX = self.animBox.imy_width / 2.0;
    }

    return _labelTiming;
}

- (UILabel *)labelTapToStop {

    if (_labelTapToStop == nil) {

        _labelTapToStop = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 60, 40)];
        _labelTapToStop.backgroundColor = [UIColor clearColor];
        _labelTapToStop.textAlignment = NSTextAlignmentCenter;
        _labelTapToStop.font = [UIFont systemFontOfSize:14 * _heightFactory];
        [_labelTapToStop imy_setTextColorForKey:kCK_Red_B];
        _labelTapToStop.text = IMYString(@"点击结束");
        [_labelTapToStop sizeToFit];
        _labelTapToStop.imy_centerX = self.labelTiming.imy_centerX;
        _labelTapToStop.imy_bottom = self.animBox.imy_height - 24 * _heightFactory;
    }
    return _labelTapToStop;
}

- (void)addAnimBoxSubViews {

    [self.animBox addSubview:self.labelTapToBegin];
    [self.animBox addSubview:self.labelTiming];
    [self.animBox addSubview:self.labelTapToStop];

    // overlay button
    UIButton *btn = [[UIButton alloc] initWithFrame:self.animBox.bounds];
    btn.backgroundColor = [UIColor clearColor];
    [btn addTarget:self action:@selector(onRingTaped) forControlEvents:UIControlEventTouchUpInside];
    [self.animBox addSubview:btn];
}

- (void)initTextTip {

    UIView *tipbg = [[UIView alloc] initWithFrame:CGRectMake(0,
                                                             28 * _heightFactory,
                                                             self.headerView.imy_width,
                                                             80)];
    tipbg.backgroundColor = [UIColor clearColor];

    UILabel *tip = [[UILabel alloc] initWithFrame:CGRectMake(5, 0, self.headerView.imy_width - 10, 70)];
    tip.numberOfLines = 0;
    tip.lineBreakMode = NSLineBreakByWordWrapping;
    tip.adjustsFontSizeToFitWidth = YES;
    tip.font = [UIFont systemFontOfSize:14 * _heightFactory];
    [tip imy_setTextColorForKey:kCK_White_A];
    tip.textAlignment = NSTextAlignmentCenter;
    tip.accessibilityIdentifier = @"tipLabel";
    [tipbg addSubview:tip];

    self.tipBg = tipbg;
    self.tipLabel = tip;
}

- (void)setTipText:(NSString *)text {

    self.tipLabel.imy_height = self.tipBg.imy_height - 10;

    NSDictionary *attributeDict = @{
        NSFontAttributeName: [UIFont systemFontOfSize:14.0 * _heightFactory],
        NSForegroundColorAttributeName: IMY_COLOR_KEY(kCK_White_A)
    };

    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:text attributes:attributeDict];
    //调整行距 9 * q
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 9 * _heightFactory;
    paragraphStyle.alignment = NSTextAlignmentCenter;
    [attributedStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [text length])];

    [self.tipLabel setAttributedText:attributedStr];

    [self.tipLabel sizeToFit];

    self.tipLabel.imy_width = self.headerView.imy_width - 10;
    self.tipLabel.imy_centerX = self.tipBg.imy_centerX;
    self.tipBg.imy_height = self.tipLabel.imy_height;
}

- (void)initNavigateBar {

    self.imy_topRightButton.titleLabel.font = [UIFont systemFontOfSize:16.0];
    [self.imy_topRightButton setTitle:IMYString(@"所有记录") forState:UIControlStateNormal];
    [self.imy_topRightButton addTarget:self action:@selector(onTopRightBtnTaped:) forControlEvents:UIControlEventTouchUpInside];
    self.imy_topRightButton.userInteractionEnabled = YES;
}

- (void)ensureStringInited {

    if (TOOLS_CCTIP_FIRST == nil) {

        //温馨提醒
        //开始
        //0~5s
        TOOLS_CCTIP_INTERVALWITHIN5S = IMYString(@"两次间隔时间太短了");

        //5~10s
        TOOLS_CCTIP_INTERVALWITHIN10S = IMYString(@"两次宫缩间隔小于10秒，别玩手机啦，该进产房了吧，宝宝迫不及待想见您咯~");

        //10s~5m
        TOOLS_CCTIP_BETWEEN10STO5MIN = IMYString(@"距离上次宫缩不到五分钟，有可能是临产征兆哦，快快联系医生吧~");

        //5m~10m
        TOOLS_CCTIP_BETWEEN5MINTO10MIN = IMYString(@"距离上次宫缩不到十分钟，建议您咨询一下您的妇产医生哦~");

        //10m~
        TOOLS_CCTIP_BETWEEN10MINLESS = IMYString(@"疼痛剧烈时，请按以下方法调整呼吸：\n第一步，平卧，闭目，以鼻深吸气\n第二步，以口呼气，放松腹部\n第三步，以鼻吸气后，屏气10秒左右，然后以口长呼气。");

        //持续
        //3m
        TOOLS_CCTIP_LASTFOR3MIN = IMYString(@"临产宫缩一般持续时间在1分钟左右，建议您联系一下您的妇产医生吧~");

        //10m
        TOOLS_CCTIP_LASTFOR10MIN = IMYString(@"持续时间超过10分钟，已停止计时，这条记录数据异常不被保存哦~");

        //结束
        //0~5s
        TOOLS_CCTIP_WITHIN5S = IMYString(@"宫缩时间过短，这条错误数据已删除");


        //特殊状态
        //orginal
        TOOLS_CCTIP_FIRST = IMYString(@"宫缩是临产的一个重要特征，监测宫缩次数和间隔时间，可以及时与医生联系反馈");

        //delete

        //no data
        //        TOOLS_CCTIP_OPERATION = IMYString(@"操作说明\n阵痛开始时，请点击计时器，停止阵痛后再次点击记录结束时间。\n如果误操作，可左滑记录进行删除。\n颜色说明\n红色代表间隔时间小于5分钟：马上有喜");

        TOOLS_CCTIP_DEFAULT = IMYString(@"第一步，平卧，闭目，以鼻深呼吸；\n第二步，以口深呼吸放松腹部；\n第三步，以鼻吸气后，屏气，然后长呼气。");

        //其他提醒
        TOOLS_CCTIP_LASTRECORD = IMYString(@"点击右上角“所有记录”查看更多记录~");
    }
}

#pragma mark - table delegates
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {

    _cachedDataRowCount = _sortedKey.count;

    NSInteger num = _sortedKey.count;
    if (num == 0) {
        num = self.isDeleteUpdate ? 0 : 1; //empty cell
        if (self.tableView.tableFooterView) {
            self.tableView.tableFooterView = nil;
        }
    } else {
        if (!self.tableView.tableFooterView) {
            self.tableView.tableFooterView = self.tabelFooterView;
        }
    }

    [self refreshTableViewFooterView];


    return num;
}


- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {

    if (!_isInited || _cachedDataRowCount <= 0) {

        return 1;
    } else if (_cachedDataRowCount > 0) {
        NSString *key = _sortedKey[section];
        NSArray *arr = self.viewModel.allData[key];
        return arr.count;
    } else {
        return 0;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {

    //empty cell
    if (_cachedDataRowCount == 0) {
        return nil;
    }

    UILabel *label;

    UITableViewHeaderFooterView *header = [_tableView dequeueReusableHeaderFooterViewWithIdentifier:@"Header"];
    if (header == nil) {

        header = [[UITableViewHeaderFooterView alloc] initWithReuseIdentifier:@"Header"];
        header.frame = CGRectMake(0, 0, self.view.imy_width, 34);
        [header.contentView imy_setBackgroundColor:kIMY_White];

        label = [[UILabel alloc] initWithFrame:CGRectMake(10, 8, self.view.imy_width - 20, 28)];
        label.font = [UIFont systemFontOfSize:14];
        [label imy_setTextColorForKey:kCK_Black_B];

        [header imy_showLineForDirection:IMYDirectionDown];
        [header addSubview:label];
        [label setTag:3389];
    } else {

        label = (UILabel *)[header viewWithTag:3389];
    }

    label.text = _sortedKey[section];
    [label sizeToFit];
    label.imy_centerY = header.imy_height / 2;

    return header;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {

    UITableViewHeaderFooterView *foot = [tableView dequeueReusableHeaderFooterViewWithIdentifier:@"Foot"];

    if (foot == nil) {
        foot = [[UITableViewHeaderFooterView alloc] initWithReuseIdentifier:@"Foot"];
        [foot.contentView imy_setBackgroundColor:kIMY_BG];
    }

    return foot;
}

- (UIView *)tabelHeaderView {

    if (_tabelHeaderView == nil) {
        _tabelHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0,
                                                                    SCREEN_HEIGHT - _bottomHeight - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT,
                                                                    SCREEN_WIDTH,
                                                                    44.0)];
        [_tabelHeaderView imy_setBackgroundColor:kIMY_White];
        [_tabelHeaderView imy_showLineForDirection:IMYDirectionDown];

        UILabel *beginLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 100, 44.0)];
        UILabel *durtionLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 100, 44.0)];
        UILabel *intervalLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 100, 44.0)];

        beginLabel.imy_left = 20;
        durtionLabel.imy_centerX = _tabelHeaderView.imy_centerX;
        intervalLabel.imy_right = _tabelHeaderView.imy_right - 20;

        durtionLabel.textAlignment = NSTextAlignmentCenter;
        intervalLabel.textAlignment = NSTextAlignmentRight;

        beginLabel.font = [UIFont systemFontOfSize:14.0];
        durtionLabel.font = [UIFont systemFontOfSize:14.0];
        intervalLabel.font = [UIFont systemFontOfSize:14.0];

        [beginLabel imy_setTextColorForKey:kCK_Black_B];
        [durtionLabel imy_setTextColorForKey:kCK_Black_B];
        [intervalLabel imy_setTextColorForKey:kCK_Black_B];

        beginLabel.text = IMYString(@"开始时间");
        durtionLabel.text = IMYString(@"持续时间");
        intervalLabel.text = IMYString(@"间隔时间");

        [_tabelHeaderView addSubview:beginLabel];
        [_tabelHeaderView addSubview:durtionLabel];
        [_tabelHeaderView addSubview:intervalLabel];
    }

    return _tabelHeaderView;
}


- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (_cachedDataRowCount == 0) {
        return 0.0;
    }
    return 34.0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section < _sortedKey.count - 1) {
        return 10.0;
    } else {
        return 0.1;
    }
}

- (void)refreshTableViewFooterView {

    CGFloat totalHeigh = 0;
    NSInteger count = 0;
    for (NSString *key in _sortedKey) {
        NSArray *arr = self.viewModel.allData[key];
        count += arr.count;
    }

    if (count == 0) {
        //no data
        totalHeigh = self.tableView.imy_height;
    } else {
        // have datas
        totalHeigh += count * 44 + _sortedKey.count * 34 + (_sortedKey.count - 1) * 10;
    }

    CGFloat h = 28 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    if (self.tableView.imy_height - totalHeigh > h) {
        self.tabelFooterView.imy_height = self.tableView.imy_height - totalHeigh;
    } else {
        self.tabelFooterView.imy_height = h + 12;
    }
    self.helpInfoButton.imy_bottom = self.tabelFooterView.imy_height - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
}

- (UILabel *)tabelFooterView {

    if (_tabelFooterView == nil) {

        _tabelFooterView = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 28)];
        _tabelFooterView.userInteractionEnabled = YES;
        UIButton *button = [UIButton buttonWithType:UIButtonTypeRoundedRect];
        button.frame = CGRectMake(0, 0, 80, 28);
        button.imy_bottom = _tabelFooterView.imy_height;
        button.imy_right = _tabelFooterView.imy_width;
        [button imy_setTitle:IMYString(@"帮助说明") state:UIControlStateNormal];
        [button imy_setTitleColor:IMY_COLOR_KEY(kCK_Red_B) state:UIControlStateNormal];
        button.titleLabel.font = [UIFont systemFontOfSize:14.0];
        [button sizeToFit];
        button.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, -15);
        [button addTarget:self action:@selector(helpInfoButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        [_tabelFooterView addSubview:button];

//        [_tabelFooterView imy_showLineForDirection:IMYDirectionUp];

        self.helpInfoButton = button;
    }

    return _tabelFooterView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {

    if (!_isInited)
        return _emptyRowHeight;

    if (_cachedDataRowCount > 0) {

        return 44;
    } else {
        // empty
        return _emptyRowHeight;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {

    if (_isInited && _cachedDataRowCount > 0) {
        return [self contentCellAtIndexPath:indexPath];
    } else {
        return [self emptyTipCell];
    }
}

- (UITableViewCell *)contentCellAtIndexPath:(NSIndexPath *)indexPath {

    IMYToolsFMCountingCell *cell = [_tableView dequeueReusableCellWithIdentifier:fmcell forIndexPath:indexPath];
    IMYToolsContractionDataModel *model = [self getContractionsDataModel:indexPath];
    cell.cellHeighIs44 = YES;
    cell.contractionModel = model;
    [cell imy_lineViewWithDirection:IMYDirectionDown show:YES margin:10];
    return cell;
}

- (UITableViewCell *)emptyTipCell {

    static NSString *HowToCellIdentifier = @"HowToCell";
    UITableViewCell *cell = [_tableView dequeueReusableCellWithIdentifier:HowToCellIdentifier];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:HowToCellIdentifier];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        [cell.contentView imy_setBackgroundColor:kIMY_White];
        //处理IOS7下，UI不能超出cell范围的问题
        [cell.contentView.superview setClipsToBounds:YES];
        cell.imy_height = self.tableView.imy_height;
        
        IMYCaptionViewV2 *captionView = [[IMYCaptionViewV2 alloc] initWithViewStyle:IMYCaptionViewV2StyleMini];
        [captionView setTitle:IMYString(@"最近7天没有宫缩记录哦") andState:IMYCaptionViewStateNoResult];
        [captionView setImages:@[[UIImage imy_imageForKey:@"tool_caption_view_no_record.png"]] forState:IMYCaptionViewStateNoResult];
        captionView.state = IMYCaptionViewStateLoading;
        [cell.contentView addSubview:captionView];
        [captionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(cell);
        }];
        @weakify(self);
        captionView.retryBlock = ^{
            @strongify(self);
            [self requestData];
        };
        self.captionView = captionView;

        _emptyRowHeight = cell.imy_height;

        //帮组说明
        UIButton *button = [UIButton buttonWithType:UIButtonTypeRoundedRect];
        button.tag = 100;
        button.frame = CGRectMake(0, 0, 80, 28);
        button.imy_bottom = _emptyRowHeight - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        button.imy_right = SCREEN_WIDTH;
        [button imy_setTitle:IMYString(@"帮助说明") state:UIControlStateNormal];
        [button imy_setTitleColor:IMY_COLOR_KEY(kCK_Red_B) state:UIControlStateNormal];
        button.titleLabel.font = [UIFont systemFontOfSize:14.0];
        button.hidden = YES;
        [button sizeToFit];
        button.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, -15);
        [button addTarget:self action:@selector(helpInfoButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        [cell.contentView addSubview:button];
    }
    
    UIView *helpBtn = [cell.contentView viewWithTag:100];
    [cell.contentView bringSubviewToFront:helpBtn];
    
    if (_sortedKey.count == 0 && _isInited) {
        helpBtn.hidden = NO;
    } else {
        helpBtn.hidden = YES;
    }
    
    [cell imy_showLineForRow:0 leftMargin:0 rowCount:1];

    return cell;
}


#pragma - mark UITableView Delete Action
- (NSString *)tableView:(UITableView *)tableView titleForDeleteConfirmationButtonForRowAtIndexPath:(NSIndexPath *)indexPath {
    return IMYString(@"删除");
}

- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {

    if (!_cachedDataRowCount) {
        return UITableViewCellEditingStyleNone;
    } else {
        return UITableViewCellEditingStyleDelete;
    }
}


- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath {

    if (editingStyle == UITableViewCellEditingStyleDelete) {

        @weakify(self);
        [UIAlertView imy_showAlertViewWithTitle:IMYString(@"要删除这条记录吗？")
                                        message:nil
                              cancelButtonTitle:IMYString(@"取消")
                              otherButtonTitles:@[IMYString(@"删除")]
                                        handler:^(UIAlertView *alertView, NSInteger buttonIndex) {
                                            @strongify(self);
                                            if (buttonIndex == 1) {
                                                [self.viewModel removeRecord:[self getContractionsDataModel:indexPath] completeBlock:^(NSError *error) {
                                                    if (!error) {
//                                                        self.isDeleteUpdate = YES;
//                                                        BOOL isDeleteSection = [self getTotalRowCount:indexPath real:YES] == 1 ? YES : NO;

                                                        [self initSortedKey];

                                                        //section 最后一条数据直接刷新数据
//                                                        if (isDeleteSection) {
//                                                            [tableView deleteSections:[NSIndexSet indexSetWithIndex:indexPath.section] withRowAnimation:UITableViewRowAnimationBottom];
//                                                        } else {
//                                                            [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationBottom];
//                                                        }

//                                                        self.isDeleteUpdate = NO;

                                                        imy_asyncMainBlock(^{
                                                            [self checkIsEmpty];
                                                            [self setTipText:TOOLS_CCTIP_FIRST];
                                                            [tableView reloadData];
                                                        });
                                                    }
                                                }];
                                                
                                            } else {
                                                [tableView setEditing:NO animated:YES];
                                            }
                                        }];
    }
}

- (IMYToolsContractionDataModel *)getContractionsDataModel:(NSIndexPath *)indexPath {

    IMYToolsContractionDataModel *model = nil;
    NSString *key = _sortedKey[indexPath.section];
    NSArray *arr = self.viewModel.allData[key];
    model = arr[indexPath.row];
    return model;
}
//real NO:如果是最后一条数据，count加一，  YES:真实的count值
- (NSInteger)getTotalRowCount:(NSIndexPath *)indexPath real:(BOOL)real {

    NSInteger count = 0;
    NSString *key = _sortedKey[indexPath.section];
    NSArray *arr = self.viewModel.allData[key];
    count = arr.count;

    if (real) {
        return count;
    }
    //为了透出最后一个cell的下划线，做的处理；
    if (indexPath.section == arr.count - 1 && indexPath.row == count - 1) {
        count += 1;
    }

    return count;
}

#pragma mark - visual update
- (void)setTimingInterval:(NSTimeInterval)interval {

    int min = (int)(interval / 60);
    int sec = ((int)interval) % 60;
    self.labelTiming.text = [NSString stringWithFormat:@"%02d:%02d", min, sec];
}

- (void)updateLabelVisible {

    BOOL isCounting = _timer && _currentModel;

    _labelTapToBegin.hidden = isCounting;
    _labelTiming.hidden = !isCounting;
    _labelTapToStop.hidden = !isCounting;
}

#pragma mark - data
- (void)preparyData {

    if (_isInited || _isIniting)
        return;
    _isIniting = YES;
    self.viewModel = IMYToolsContractionsDataManager.new;
    self.viewModel.isHome = YES;
    [self requestData];
}

-(void)requestData {
    if (![IMYNetState networkEnable]) {
        [self.captionView setTitle:IMYString(@"网络不见了，请检查网络") andState:IMYCaptionViewStateRetry];
        self.captionView.state = IMYCaptionViewStateRetry;
        return;
    }
    self.captionView.state = IMYCaptionViewStateLoading;
    @weakify(self);
    [self.viewModel uploadAllRecord:^(NSError *error) {
        [self.viewModel getRecordsForHomePage:^(NSError *error) {
            @strongify(self);
            if (!error) {
                [self imy_asyncBlock:^{
                    @strongify(self);
                    [self initSortedKey];
                    [self imy_asyncMainBlock:^{
                        @strongify(self);
                        [self checkIsEmpty];
                        _isInited = YES;
                        _isIniting = NO;
                        [self.tableView reloadData];
                    }];
                }];
            } else {
                [self imy_asyncMainBlock:^{
                    [self.captionView setTitle:IMYString(@"加载失败，请点击重新加载") andState:IMYCaptionViewStateRetry];
                    self.captionView.state = IMYCaptionViewStateRetry;
                }];
                
                [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                    pageName:@"IMYToolsContractionsCountingVC"
                                    category:IMYErrorTraceCategoryYunyu
                                     message:@"v3/contraction error"
                                      detail:@{
                                               @"code":@(error.code),
                                               @"reason" : error.localizedFailureReason ?: error.localizedDescription
                                             }];
            }
        }];
    }];
}

- (void)initSortedKey {

    _sortedKey = [[NSMutableArray alloc] init];

    for (NSArray *arr in self.viewModel.allData.allValues) {
        if (arr.count > 0) {
            IMYToolsContractionDataModel *m = arr.firstObject;
            [_sortedKey addObject:[m.beginTime tools_getOnlyDateString]];
        }
    }

    [_sortedKey sortUsingComparator:^(NSString *k1, NSString *k2) {
        return [k2 compare:k1];
    }];
}

- (void) checkIsEmpty {
    
    if (_sortedKey.count > 0) {
        _captionView.state = IMYCaptionViewStateHidden;
    } else {
        _captionView.state = IMYCaptionViewStateNoResult;
    }
}

#pragma mark - events

- (void)onRingTaped {
    if (_timer == nil) {
        if (self.isBebound10Min) {
            self.isBebound10Min = NO;
            [self setTipText:TOOLS_CCTIP_FIRST];
            [self updateLabelVisible];
        } else {
            // start
            [self onBeginTiming];
            [IMYEventHelper event:@"sgs-ks"];
            [IMYToolsContractionsCountingVC saveStartContractionAction];
        }
    } else {
        // stop
        [self stopTimingByManual:YES];
        [IMYEventHelper event:@"sgs-js"];
        self.end_type = @(1);
        [self uploadGA];
        self.isCounting = NO;
    }
}

//数宫缩：当用户开始数宫缩时，告知广告侧此时的时间戳，此后2小时内不展示广告
+ (void)saveStartContractionAction{
    NSString *key = [NSString stringWithFormat:@"IMY_contraction_start_time_user_id=%@", [IMYPublicAppHelper shareAppHelper].userid];
    NSString *timestamp = @((uint64_t)[[NSDate date] timeIntervalSince1970]).stringValue;
    [[IMYKV defaultKV] setString:timestamp forKey:key];
}

- (void)onTopRightBtnTaped:(UIButton *)button {
    IMYToolsCCAllDataVC *avc = IMYToolsCCAllDataVC.new;
    @weakify(self);
    avc.deleRecordFinish = ^(IMYToolsContractionDataModel *record) {
        @strongify(self);
        [self.viewModel removeRefrenceRecord:record];
    };
    [self imy_push:avc];
}

- (void)onBeginTiming {

    NSAssert(_timer == nil, @"");
    IMYToolsContractionDataModel *lastModel = nil;
    for (IMYToolsContractionDataModel *m in self.viewModel.linearedAllData) {
        if (m.beginTime.timeIntervalSinceNow <= 0) {
            lastModel = m;
            break;
        }
    }
    if (lastModel != nil) {
        if (lastModel.endTime.timeIntervalSinceNow >= -5) {
            [UIView imy_showTextHUD:TOOLS_CCTIP_INTERVALWITHIN5S];
            return;
        } else if (lastModel.endTime.timeIntervalSinceNow >= -10) {
            [UIView imy_showTextHUD:TOOLS_CCTIP_INTERVALWITHIN10S];
            return;
        } else if (lastModel.endTime.timeIntervalSinceNow >= -5 * 60) {
            [self setTipText:TOOLS_CCTIP_BETWEEN10STO5MIN];
        } else if (lastModel.endTime.timeIntervalSinceNow >= -10 * 60) {
            [self setTipText:TOOLS_CCTIP_BETWEEN5MINTO10MIN];
        } else {
            [self setTipText:TOOLS_CCTIP_BETWEEN10MINLESS];
        }
    } else {
        [self setTipText:TOOLS_CCTIP_BETWEEN10MINLESS];
    }

    _timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(onTick:) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];

    _currentModel = [[IMYToolsContractionDataModel alloc] init];

    double time = floor([NSDate date].timeIntervalSince1970);
    _currentModel.beginTime = [NSDate dateWithTimeIntervalSince1970:time];
    self.start_type = @(1);
    [self resetStartTime];
    self.isCounting = YES;

    [self onTick:nil];
    [self updateLabelVisible];
}

- (void)onTick:(NSTimer *)timer {

    NSTimeInterval passed = -_currentModel.beginTime.timeIntervalSinceNow;
    [self setTimingInterval:passed];

    if (passed > 10 * 60) {
        // timeout
        //        [self stopTimingByManual:NO];
        self.isBebound10Min = YES;
        [_timer invalidate];
        _timer = nil;
        self.end_type = @(1);
        [self uploadGA];
        self.isCounting = NO;
        _currentModel = nil;
        [self setTipText:TOOLS_CCTIP_LASTFOR10MIN];
    } else if (passed > 3 * 60) {
        [self setTipText:TOOLS_CCTIP_LASTFOR3MIN];
    }
}

- (void)stopTimingByManual:(BOOL)isManual {

    [_timer invalidate];
    _timer = nil;

    if (isManual) {

        NSTimeInterval dur = -_currentModel.beginTime.timeIntervalSinceNow;
        if ((NSInteger)dur <= 5) {
            [UIView imy_showTextHUD:TOOLS_CCTIP_WITHIN5S];
            //_currentModel = nil;
        } else {
            // save data
            _currentModel.duration = (NSInteger)dur;
            [self.viewModel addRecord:_currentModel];
            //_currentModel = nil;

            // add key if need
            NSDate *now = [NSDate date];
            NSString *newkey = [now tools_getOnlyDateString];
            if (![_sortedKey containsObject:newkey]) {
                NSUInteger insertIdx = 0;
                for (NSString *k in _sortedKey) {
                    if ([k compare:newkey] == NSOrderedAscending) {
                        break;
                    } else {
                        insertIdx++;
                    }
                }

                [_sortedKey insertObject:[now tools_getOnlyDateString] atIndex:insertIdx];
            }

            [_tableView reloadData];
        }
    } else {
        // drop data
        _currentModel = nil;
    }

    [self updateLabelVisible];
}

- (void)imy_topLeftButtonTouchupInside {

    if (_timer == nil) {
        [self imy_pop:YES];
        return;
    }

    [UIAlertView imy_showAlertViewWithTitle:IMYString(@"提示")
                                    message:IMYString(@"退出后，本次记录将会丢失，确定要放弃记录吗？")
                          cancelButtonTitle:IMYString(@"取消")
                          otherButtonTitles:@[IMYString(@"确定")]
                                    handler:^(UIAlertView *alertView, NSInteger buttonIndex) {
                                        if (buttonIndex == 1) {
                                            // app内结束，并上报GA
                                            self.end_type = @(1);
                                            [self uploadGA];
                                            [self stopTimingByManual:NO];
                                            [self imy_pop:YES];
                                        }
                                    }];
}

#pragma - mark 适配
- (void)setFactory {

    _widthFactory = SCREEN_WIDTH / 375.0;
    _heightFactory = SCREEN_HEIGHT / 667.0;
    _bottomHeight = kBottonHeight * _heightFactory;
    _headerViewHeight = SCREEN_HEIGHT - _bottomHeight - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
}

#pragma mark - fake navigationbar delegate

- (BOOL)shouldFakeNavigationBarTransition {
    return YES;
}

#pragma mark - GA
//https://www.tapd.cn/21039721/prong/stories/view/1121039721001042054
- (void)uploadGA {
  
    if (!self.isCounting) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    // 18 ：数宫缩，17：数胎动
    params[@"tools_id"] = @(18);

    //1 APP内  2：后台（包含熄屏和退出app）
    params[@"start_type"] = self.start_type;
    
    /*
     结束方式:
     1：结束（开始方式是APP内，在模块内点击结束；开始方式是后台，在后台杀进程结束 )
     2：后台（开始方式是APP内，跳转到后台）
     3：APP内（开始方式是后台，跳转到前台）
     */
    params[@"end_type"] = self.end_type;
    
    NSTimeInterval duration = self.startTimeStamp - [_currentModel.beginTime timeIntervalSince1970];
    params[@"start_time"] = [NSString stringWithFormat:@"%.0f", duration];
    
    NSTimeInterval endDuration = [[NSDate date] timeIntervalSince1970]  - [_currentModel.beginTime timeIntervalSince1970];
    params[@"end_time"] = [NSString stringWithFormat:@"%.0f",endDuration];
    
    [IMYGAEventHelper postWithPath:@"bi_timer" params:params headers:nil completed:nil];
    // 上报完成后需重新设置上报开始时间及开始方式
    [self resetStartTime];
}

- (void)resetStartTime {
    self.startTimeStamp = [[NSDate date] timeIntervalSince1970];
}


- (IMYRequestOperateService *)operateService {
    if (!_operateService) {
        _operateService = [IMYRequestOperateService sharedManager];
        IMYVKUserMode mode = [IMYPublicAppHelper shareAppHelper].userMode;
        _operateService.cacheKey = [NSString stringWithFormat:@"v3/operation_guide_module_%@_mode_%@",MODULE_CONTRACTION,@(mode)];
    }
    return _operateService;
}
@end
