//
//  IMYToolsPostpartumChangeCell.m
//  IMY_Tools
//
//  Created by meiyou on 2021/4/8.
//  Copyright © 2021 linggan. All rights reserved.
//

#import "IMYToolsPostpartumChangeCell.h"
#import <IMYBaseKit/IMYPublic.h>
#import "UILabel+Tools.h"
#import <IMYBaseKit/IMY_ViewKit.h>

@interface IMYToolsPostpartumChangeInfoLayout : NSObject

@property(nonatomic, assign) CGRect questionFrame;
@property(nonatomic, assign) CGRect optionOneFrame;
@property(nonatomic, assign) CGRect optionTwoFrame;
@property(nonatomic, assign) CGRect answerOneFrame;
@property(nonatomic, assign) CGRect answerTwoFrame;

@property(nonatomic, assign) CGFloat testViewTop;

@property(nonatomic, assign) CGFloat heightNoAnswer;
@property(nonatomic, assign) CGFloat heightAnswerOne;
@property(nonatomic, assign) CGFloat heightAnswerTwo;

@property(nonatomic, assign) CGFloat totalHeight;

@property(nonatomic, assign) CGFloat testHeight;
@property(nonatomic, assign) CGRect testFrame;

@end

@implementation IMYToolsPostpartumChangeInfoLayout

+(IMYToolsPostpartumChangeInfoLayout *)layoutWithModel:(IMYToolsPostpartumChangeInfoModel *)model
{
    IMYToolsPostpartumChangeInfoLayout *layout = IMYToolsPostpartumChangeInfoLayout.new;
    CGSize contentSize = [self sizeWithContent:model.content lineH:26 font:[UIFont systemFontOfSize:16] paragraphSpacing:12 maxWidth:SCREEN_WIDTH - 48];
    
    CGFloat titleLabelH = 22;
    CGFloat maxW = SCREEN_WIDTH - 72;
    CGFloat space = 12;
    
    NSArray *options = model.options;
    if (options.count < 2 || imy_isBlankString(model.question)) {
        layout.totalHeight = titleLabelH + space + contentSize.height + space;
        return layout;
    }
   
    NSString *question = model.question;
    CGSize  questionSize = [self sizeWithContent:question lineH:24 font:[UIFont systemFontOfSize:14]];
    
    IMYToolsPostpartumChangeOptionModel *o1 = options[0];
    IMYToolsPostpartumChangeOptionModel *o2 = options[1];

    CGSize o1size = [self sizeWithContent:o1.title lineH:17 font:[UIFont systemFontOfSize:12]];
    CGSize o2size = [self sizeWithContent:o2.title lineH:17 font:[UIFont systemFontOfSize:12]];
    CGFloat oMaxW = MAX(o1size.width, o2size.width) + 24;

    //文字尾部有箭头
    NSString *o1Content = imy_isBlankString(o1.uri) ? o1.content : [o1.content stringByAppendingString:@"占位"];
    NSString *o2Content = imy_isBlankString(o2.uri) ? o2.content : [o2.content stringByAppendingString:@"占位"];

    CGSize a1size = [self sizeWithContent:o1Content lineH:22 font:[UIFont systemFontOfSize:13]];
    CGSize a2size = [self sizeWithContent:o2Content lineH:22 font:[UIFont systemFontOfSize:13]];
    
    layout.questionFrame = CGRectMake(space, space, questionSize.width, questionSize.height);
//    if ((questionSize.width + 6 + 8 + 2 * oMaxW) < maxW)
    if (question.length < 12 && o1.title.length < 4 && o2.title.length < 4)
    { //一行放得下
        layout.optionOneFrame = CGRectMake(CGRectGetMaxX(layout.questionFrame) + 6, space, oMaxW, 24);
        layout.optionTwoFrame = CGRectMake(CGRectGetMaxX(layout.optionOneFrame) + 8, space, oMaxW, 24);
    } else {
        if (oMaxW > 92) {
            oMaxW = (maxW - 8) / 2;//最大
        } else {
            oMaxW = 92;//最小
        }
        layout.optionOneFrame = CGRectMake(space, CGRectGetMaxY(layout.questionFrame) + space, oMaxW, 24);
        layout.optionTwoFrame = CGRectMake(CGRectGetMaxX(layout.optionOneFrame) + 8, CGRectGetMinY(layout.optionOneFrame), oMaxW, 24);
    }
    
    layout.answerOneFrame = CGRectMake(space, CGRectGetMaxY(layout.optionOneFrame) + space, a1size.width, a1size.height);
    layout.answerTwoFrame = CGRectMake(space, CGRectGetMaxY(layout.optionOneFrame) + space, a2size.width, a2size.height);

    layout.heightNoAnswer = CGRectGetMaxY(layout.optionTwoFrame) + space;
    layout.heightAnswerOne = CGRectGetMaxY(layout.answerOneFrame) + space;
    layout.heightAnswerTwo = CGRectGetMaxY(layout.answerTwoFrame) + space;
    layout.testViewTop = titleLabelH + space + contentSize.height + 8;
    if (model.selectIndex == 0) {
        layout.totalHeight = layout.testViewTop + layout.heightNoAnswer + space;
        layout.testHeight = layout.heightNoAnswer;
    } else if (model.selectIndex == 1) {
        layout.totalHeight = layout.testViewTop + layout.heightAnswerOne + space;
        layout.testHeight = layout.heightAnswerOne;
    } else if (model.selectIndex == 2) {
        layout.totalHeight = layout.testViewTop + layout.heightAnswerTwo + space;
        layout.testHeight = layout.heightAnswerTwo;
    }
    layout.testFrame = CGRectMake(space, layout.testViewTop, SCREEN_WIDTH - 48, layout.testHeight);
    return layout;
}

+(CGSize)sizeWithContent:(NSString *)string lineH:(CGFloat)lineH font:(UIFont *)font {
    CGSize  size = [self sizeWithContent:string lineH:lineH font:font paragraphSpacing:0 maxWidth:0];
    
    return CGSizeMake(ceil(size.width), ceil(size.height));
}

+(CGSize)sizeWithContent:(NSString *)string
                   lineH:(CGFloat)lineH
                    font:(UIFont *)font
        paragraphSpacing:(CGFloat)paragraphSpacing
                maxWidth:(CGFloat)maxWidth{
    CGFloat maxW = maxWidth > 0.0 ? maxWidth: SCREEN_WIDTH - 72;
    NSMutableParagraphStyle *style = NSMutableParagraphStyle.new;
    if (paragraphSpacing > 0) {
        style.paragraphSpacing = 12;
    }
    style.maximumLineHeight = lineH;
    style.minimumLineHeight = lineH;
    CGSize  size = [string boundingRectWithSize:CGSizeMake(maxW,  MAXFLOAT)
                                                options: NSStringDrawingUsesLineFragmentOrigin
                                             attributes: @{ NSFontAttributeName:font,
                                                            NSParagraphStyleAttributeName:style}
                                                context: nil].size;
    return CGSizeMake(ceil(size.width)+1, ceil(size.height));
}


@end



@implementation IMYToolsPostpartumChangeTestView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self imy_setBackgroundColor:@"#FFF9FB"];
        [self imy_drawAllCornerRadius:8];
        [self addSubview:self.questionLabel];
        [self addSubview:self.optionTwoBtn];
        [self addSubview:self.optionOneBtn];
        [self addSubview:self.contentLabel];
    }
    return self;
}

-(void)setupWithModel:(IMYToolsPostpartumChangeInfoModel *)model {
    self.model = model;
    NSArray *options = model.options;
    if (options.count < 2) {
        return;
    }
    [self.questionLabel tools_setText:model.question lineHeight:24];
    IMYToolsPostpartumChangeOptionModel *o1 = options[0];
    IMYToolsPostpartumChangeOptionModel *o2 = options[1];

    [self.optionOneBtn imy_setTitle:o1.title];
    [self.optionTwoBtn imy_setTitle:o2.title];
    
    IMYToolsPostpartumChangeInfoLayout *layout = [IMYToolsPostpartumChangeInfoLayout layoutWithModel:model];
    self.frame = layout.testFrame;
    self.questionLabel.frame = layout.questionFrame;
    self.optionOneBtn.frame = layout.optionOneFrame;
    self.optionTwoBtn.frame = layout.optionTwoFrame;
    
    if (model.selectIndex == 0) {
        self.contentLabel.alpha = 0;
    } else if (model.selectIndex == 1) {
        [self.contentLabel tools_setText:o1.content lineHeight:22];
        if (imy_isNotBlankString(o1.uri)) {
            NSMutableAttributedString *att = self.contentLabel.attributedText.mutableCopy;
            [att appendAttributedString: [self imageAttach:@"icon_zhankai_blue"]];
            self.contentLabel.attributedText = att;
            [self.contentLabel imy_setTextColorForKey:@"#4F7CAE"];
            self.contentLabel.userInteractionEnabled = YES;
        } else {
            [self.contentLabel imy_setTextColorForKey:kCK_Black_B];
            self.contentLabel.userInteractionEnabled = NO;
        }
        self.contentLabel.alpha = 1;
        self.optionOneBtn.selected = YES;
        self.optionTwoBtn.selected = NO;
        self.contentLabel.hidden = NO;
        self.contentLabel.frame = layout.answerOneFrame;
    } else if (model.selectIndex == 2) {
        [self.contentLabel tools_setText:o2.content lineHeight:22];
        if (imy_isNotBlankString(o2.uri)) {
            NSMutableAttributedString *att = self.contentLabel.attributedText.mutableCopy;
            [att appendAttributedString: [self imageAttach:@"icon_zhankai_blue"]];
            self.contentLabel.attributedText = att;
            [self.contentLabel imy_setTextColorForKey:@"#4F7CAE"];
            self.contentLabel.userInteractionEnabled = YES;
        } else {
            [self.contentLabel imy_setTextColorForKey:kCK_Black_B];
            self.contentLabel.userInteractionEnabled = NO;
        }
        self.contentLabel.alpha = 1;
        self.optionOneBtn.selected = NO;
        self.optionTwoBtn.selected = YES;
        self.contentLabel.hidden = NO;
        self.contentLabel.frame = layout.answerTwoFrame;
    }
}

- (NSAttributedString *)imageAttach:(NSString *)imageKey {
    CGFloat y = 0;
    NSTextAttachment *attch = [[NSTextAttachment alloc] init];
    attch.image = [UIImage imy_imageForKey:imageKey];
    attch.bounds = CGRectMake(0, y, 12, 12);
    NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
    return string;
}

//
-(UILabel *)questionLabel {
    if (!_questionLabel) {
        _questionLabel = [[UILabel alloc] init];
        _questionLabel.font = [UIFont systemFontOfSize:14];
        _questionLabel.numberOfLines = 0;
        [_questionLabel imy_setTextColorForKey:kCK_Black_A];
    }
    return _questionLabel;
}

-(UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        _contentLabel.alpha = 0.0;
        _contentLabel.font = [UIFont systemFontOfSize:13];
        _contentLabel.numberOfLines = 0;
        [_contentLabel imy_setTextColorForKey:kCK_Black_B];
        @weakify(self);
        [_contentLabel bk_whenTapped:^{
            @strongify(self);
            IMYToolsPostpartumChangeOptionModel *o1 = self.model.options[0];
            IMYToolsPostpartumChangeOptionModel *o2 = self.model.options[1];
            if (self.model.selectIndex == 1) {
                [IMYURIManager.shareURIManager runActionWithString:o1.uri];
            } else if (self.model.selectIndex == 2) {
                [IMYURIManager.shareURIManager runActionWithString:o2.uri];
            }
            if (self.contentClick) {
                self.contentClick();
            }
        }];
    }
    return _contentLabel;
}

-(UIButton *)optionOneBtn {
    if (!_optionOneBtn) {
        _optionOneBtn = UIButton.new;
        [_optionOneBtn imy_drawAllCornerRadius:12];
        _optionOneBtn.layer.borderColor = IMY_COLOR_KEY(kCK_Red_A).CGColor;
        CGFloat lineWidth = 1 / MIN([UIScreen mainScreen].scale, 2.0);
        _optionOneBtn.layer.borderWidth = lineWidth;
        _optionOneBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [_optionOneBtn imy_setTitleColor:IMY_COLOR_KEY(kCK_Red_A) state:UIControlStateNormal];
        [_optionOneBtn imy_setTitleColor:IMY_COLOR_KEY(kCK_White_AT) state:UIControlStateSelected];
        [_optionOneBtn imy_setBackgroundImage:[UIImage imageWithColor:IMY_COLOR_KEY(kCK_Red_A)] state:UIControlStateSelected stretch:YES];
        [_optionOneBtn imy_setBackgroundImage:nil state:UIControlStateNormal stretch:YES];

        @weakify(self);
        [[_optionOneBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.optionOneBtn.selected) {
                return;
            }
            self.optionOneBtn.selected = YES;
            self.optionTwoBtn.selected = NO;
            UITableView *tableView = [self imy_findParentViewWithClass:[UITableView class]];

            if (self.model.selectIndex == 0) {
                self.model.selectIndex = 1;
                [self animation];
            } else {
                self.model.selectIndex = 1;
                [tableView reloadData];
            }
            if (self.optionClick) {
                self.optionClick();
            }
        }];
    }
    return _optionOneBtn;
}

-(UIButton *)optionTwoBtn {
    if (!_optionTwoBtn) {
        _optionTwoBtn = UIButton.new;
        [_optionTwoBtn imy_drawAllCornerRadius:12];
        _optionTwoBtn.layer.borderColor = IMY_COLOR_KEY(kCK_Red_A).CGColor;
        CGFloat lineWidth = 1 / MIN([UIScreen mainScreen].scale, 2.0);
        _optionTwoBtn.layer.borderWidth = lineWidth;
        _optionTwoBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [_optionTwoBtn imy_setTitleColor:IMY_COLOR_KEY(kCK_Red_A) state:UIControlStateNormal];
        [_optionTwoBtn imy_setTitleColor:IMY_COLOR_KEY(kCK_White_AT) state:UIControlStateSelected];
        [_optionTwoBtn imy_setBackgroundImage:[UIImage imageWithColor:IMY_COLOR_KEY(kCK_Red_A)] state:UIControlStateSelected stretch:YES];
        [_optionTwoBtn imy_setBackgroundImage:nil state:UIControlStateNormal stretch:YES];

        @weakify(self);
        [[_optionTwoBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            if (self.optionTwoBtn.selected) {
                return;
            }
            self.optionTwoBtn.selected = YES;
            self.optionOneBtn.selected = NO;
            UITableView *tableView = [self imy_findParentViewWithClass:[UITableView class]];
            if (self.model.selectIndex == 0) {
                self.model.selectIndex = 2;
                [self animation];
            } else {
                self.model.selectIndex = 2;
                [tableView reloadData];
            }
            if (self.optionClick) {
                self.optionClick();
            }
        }];
    }
    return _optionTwoBtn;
}

-(void)animation {
    UITableView *tableView = [self imy_findParentViewWithClass:[UITableView class]];
    IMYToolsPostpartumChangeInfoLayout *layout = [IMYToolsPostpartumChangeInfoLayout layoutWithModel:self.model];
    self.frame = layout.testFrame;
    [UIView animateWithDuration:0.2 animations:^{
        [[tableView visibleCells] makeObjectsPerformSelector:@selector(layoutIfNeeded)];
    }];
    [self setupWithModel:self.model];
    self.contentLabel.alpha = 0.f;
    [UIView animateWithDuration:0.15 delay:0.1 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        self.contentLabel.alpha = 1.f;
    } completion:nil];
    [tableView beginUpdates];
    [tableView endUpdates];
}

@end

@interface IMYToolsPostpartumChangeCell ()

@property (nonatomic, strong) UIImageView *iconIV;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;

@end

@implementation IMYToolsPostpartumChangeCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:UITableViewCellStyleDefault reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.clipsToBounds = YES;
        
        //0.设置背景颜色
        self.backgroundColor = [UIColor clearColor];

        //2.初始化UI
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self.contentView addSubview:self.bgView];
    [self.bgView addSubview:self.iconIV];
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.contentLabel];
    [self.bgView addSubview:self.testView];
    [self.bgView addSubview:self.biHelperView];

    CGFloat left = 12;
    CGFloat height = 44;
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.mas_equalTo(0);
        make.left.mas_offset(12);
        make.right.mas_offset(-12);
    }];
    
    [self.iconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12);
        make.top.mas_equalTo(6);
        make.width.height.mas_equalTo(8);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.equalTo(self.iconIV.mas_right).mas_offset(8);
        make.height.mas_equalTo(22);
    }];
    
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(12);
        make.right.mas_offset(-12);
        make.top.mas_equalTo(34);
    }];
    [self.biHelperView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_offset(0);
        make.height.mas_offset(1);
    }];
}

- (void)setContent:(IMYToolsPostpartumChangeInfoModel*)infoModel {
    self.titleLabel.text = infoModel.title;
    [self.contentLabel tools_setText:infoModel.content lineHeight:26 paragraphSpacing:12];
    [self.testView setupWithModel:infoModel];
}

#pragma mark - Get
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
        [_bgView imy_setBackgroundColorForKey:kCK_White_AN];
        _bgView.clipsToBounds = YES;
    }
    
    return _bgView;
}

-(UIImageView *)iconIV {
    if (!_iconIV) {
        _iconIV = [[UIImageView alloc] init];
        [_iconIV imy_setImage:@"icon_circle"];
    }
    
    return _iconIV;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont imy_mediumWith:16];
        [_titleLabel imy_setTextColorForKey:kCK_Black_A];
    }
    
    return  _titleLabel;
}

-(UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        _contentLabel.numberOfLines = 0;
        _contentLabel.font = [UIFont systemFontOfSize:16];
        [_contentLabel imy_setTextColorForKey:kCK_Black_M];
    }
    return  _contentLabel;
}

-(IMYToolsPostpartumChangeTestView *)testView {
    if (!_testView) {
        _testView = IMYToolsPostpartumChangeTestView.new;
        _testView.frame = CGRectZero;
    }
    return _testView;
}

-(UIView *)biHelperView {
    if (!_biHelperView) {
        _biHelperView = UIView.new;
    }
    return _biHelperView;
}


+ (CGFloat)cellHeight:(IMYToolsPostpartumChangeInfoModel*)infoModel {
    IMYToolsPostpartumChangeInfoLayout *layout = [IMYToolsPostpartumChangeInfoLayout layoutWithModel:infoModel];
    return ceil(layout.totalHeight);
}

@end
