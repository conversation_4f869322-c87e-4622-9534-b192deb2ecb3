//
//  IMYDueDateCaculateViewModel.m
//  IMY_Tools
//
//  Created by 施东苗 on 16/9/9.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "IMYDueDateCaculateViewModel.h"
#import <IMYYQBasicServices/NSDate+IMYTools.h>

@implementation IMYDueDateCaculateViewModel

- (RACSignal *)checkLastMenses:(NSDate *)date {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        NSInteger daydiff = [[NSDate imy_today] getDayDiff:date];
        if (daydiff < -280) {
            RACTuple *tuple = [RACTuple tupleWithObjects:@(NO), IMYString(@"距离今天不能超过280天"), [[NSDate imy_today] dateBySubtractingDays:280], nil];
            [subscriber sendNext:tuple];
        } else if (daydiff > 1) {
            RACTuple *tuple = [RACTuple tupleWithObjects:@(NO), IMYString(@"不能超过今天"), [NSDate imy_today], nil];
            [subscriber sendNext:tuple];
        } else {
            RACTuple *tuple = [RACTuple tupleWithObjects:@(YES), @"", date, nil];
            [subscriber sendNext:tuple];
        }
        [subscriber sendCompleted];
        return nil;
    }];
}

- (RACSignal *)caculateDueDateWithLastMensesDate:(NSDate *)lastMensesDate circleDay:(NSInteger)circleDay {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        NSDate *date = [lastMensesDate dateByAddingDays:(280 + (circleDay - 28))];
        [subscriber sendNext:date];
        [subscriber sendCompleted];
        return nil;
    }];
}

@end
