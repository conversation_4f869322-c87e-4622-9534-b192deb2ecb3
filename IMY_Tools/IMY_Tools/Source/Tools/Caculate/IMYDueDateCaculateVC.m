//
//  IMYDueDateCaculateVC.m
//  YunQi
//
//  Created by ljh on 14/12/5.
//  Copyright (c) 2014年 linggan. All rights reserved.
//

#import "IMYDueDateCaculateVC.h"
#import "IMYDueDateCaculateViewModel.h"
#import "IMYToolsInner.h"

@interface IMYDueDateCaculateVC () <UITableViewDataSource, UITableViewDelegate>
@property (strong, nonatomic) UITableView *tableView;
@property (strong, nonatomic) IMYPickerView *pickerView;
@property (strong, nonatomic) IMYPickerView *datePickerView;

@property (weak, nonatomic) UITextField *dateTextField;
@property (weak, nonatomic) UITextField *dayTextField;

@property (strong, nonatomic) IMYDueDateCaculateViewModel *viewModel;

@end

@implementation IMYDueDateCaculateVC

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        self.title = IMYString(@"计算预产期");
        self.isWhiteNavigationBar = YES;
    }
    return self;
}

- (IMYDueDateCaculateViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[IMYDueDateCaculateViewModel alloc] init];
    }
    return _viewModel;
}

- (void)loadView {
    [super loadView];

    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
    _tableView.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    _tableView.scrollEnabled = NO;
    _tableView.rowHeight = 44;
    [self.view addSubview:self.tableView];

    UIView *headView = [UIView new];
    headView.imy_height = 20;
    self.tableView.tableHeaderView = headView;

    UIView *footView = [UIView new];
    self.tableView.tableFooterView = footView;

    [self.tableView imy_makeTransparent];
}

- (void)setFootString:(NSString *)string {
    UIView *footView = self.tableView.tableFooterView;
    UILabel *label = (UILabel *)[footView viewWithTag:2001];
    if (!label) {
        label = [[UILabel alloc] initWithFrame:CGRectMake(20, 10, SCREEN_WIDTH - 40, 0)];
        label.font = [UIFont systemFontOfSize:14];
        [label imy_setTextColorForKey:kIMY_Grey];
        label.backgroundColor = [UIColor clearColor];
        label.numberOfLines = 0;
        label.tag = 2001;
        [footView addSubview:label];
    }
    label.text = string;
    [label sizeToFit];

    footView.imy_height = label.imy_bottom + 20;
    self.tableView.tableFooterView = footView;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setFootString:IMYString(@"预产期推算只是一个大概数，仅供孕妈妈参考。以医生的产检结果为准哦！")];
}

- (void)datePickerViewOk {
    NSString *currentSelectDate = [self.datePickerView resultWithType:IMYPickerViewTypeDate].resultString;
    self.viewModel.lastMensesDate = [[NSDateFormatter imy_getDateFormater] dateFromString:currentSelectDate];
    if (self.viewModel.lastMensesDate == nil) {
        return;
    }

    [[self.viewModel checkLastMenses:self.viewModel.lastMensesDate] subscribeNext:^(RACTuple *tuple) {
        BOOL valid = [tuple.first boolValue];
        if (!valid) {
            [UIWindow imy_showTextHUD:tuple.second];
            [self.datePickerView setSelectWithDate:tuple.third];
        } else {
            self.dateTextField.text = [self.viewModel.lastMensesDate imy_getOnlyShortDateString];
            [self checkInputIsFinished];
        }
    }];
}

- (void)hidePickerView {
    [self.tableView endEditing:YES];
}

- (void)checkInputIsFinished {
    if (self.dateTextField.text.length == 0) {
        [self.datePickerView show];
    } else if (self.dayTextField.text.length == 0) {
        [self.pickerView show];
    } else {
        [self.tableView endEditing:YES];

        [[self.viewModel caculateDueDateWithLastMensesDate:self.viewModel.lastMensesDate circleDay:self.viewModel.circleDayCount] subscribeNext:^(id x) {
            self.viewModel.birthDate = x;
            NSString *dueDateString = [[NSDateFormatter imy_getCN_DateFormater] stringFromDate:self.viewModel.birthDate];

            [UIAlertView imy_showAlertViewWithTitle:nil
                                            message:[NSString stringWithFormat:IMYString(@"经过小柚子的计算，您的预产期时间为：\n%@\n该数据仅供参考哦~"), dueDateString]
                                  cancelButtonTitle:IMYString(@"我知道了")
                                  otherButtonTitles:nil
                                            handler:^(UIAlertView *alertView, NSInteger buttonIndex){

                                            }];
        }];
    }
}

#pragma mark - UITableView DataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 2;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];

    UILabel *lb_title = nil;
    UITextField *textField = nil;
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        [cell imy_setupWhiteBackground];

        lb_title = [[UILabel alloc] initWithFrame:CGRectMake(15, 0, SCREEN_WIDTH - 15, 44)];
        lb_title.font = [UIFont systemFontOfSize:17];
        [lb_title imy_setTextColorForKey:kCK_Black_A];
        lb_title.backgroundColor = [UIColor clearColor];
        lb_title.tag = 1001;
        [cell.contentView addSubview:lb_title];


        UIImageView *arrowImgV = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 8, 13)];
        [arrowImgV imy_setImage:@"all_rightarrow"];
        arrowImgV.imy_centerY = 22;
        arrowImgV.imy_right = SCREEN_WIDTH - 15;
        [cell.contentView addSubview:arrowImgV];


        textField = [[UITextField alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 33, 44)];
        textField.contentVerticalAlignment = 0;
        [UIMenuController sharedMenuController].menuVisible = NO;
        textField.tag = 1002;
        textField.font = [UIFont systemFontOfSize:16];
        [textField imy_setTextColorForKey:kCK_Black_D];
        [textField imy_setPlaceholderColorForKey:kCK_Black_D];
        textField.textAlignment = NSTextAlignmentRight;
        textField.userInteractionEnabled = NO;
        [cell.contentView addSubview:textField];
        [cell imy_showLineForRow:indexPath.row leftMargin:lb_title.imy_left rowCount:2];
    } else {
        lb_title = (UILabel *)[cell.contentView viewWithTag:1001];
        textField = (UITextField *)[cell.contentView viewWithTag:1002];
    }

    if (indexPath.row == 0) {
        lb_title.text = IMYString(@"末次经期");
        textField.placeholder = IMYString(@"是什么时候开始的?");
        self.dateTextField = textField;
        if (self.datePickerView == nil) {
            NSDate *minDate = [[NSDate imy_today] dateBySubtractingDays:365 * 10];
            NSDate *maxDate = [NSDate imy_today];
            @weakify(self);
            self.datePickerView = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                dataArray:@[minDate, maxDate]
                pickerViewTpe:IMYPickerViewTypeDate
                confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                    @strongify(self);
                    [self datePickerViewOk];
                }
                cancelBlock:^{
                    @strongify(self);
                    [self hidePickerView];
                }];

            self.datePickerView.backCoverViewColor = [UIColor colorWithWhite:0 alpha:0.4];
            self.datePickerView.title = IMYString(@"末次经期");

            IMYVKUserMode userMode = [IMYPublicAppHelper shareAppHelper].userMode;

            NSDate *dueDate = (userMode == IMYVKUserModeForPregnant ? nil : [IMYPublicAppHelper shareAppHelper].pregnancy.imy_getOnlyDate);
            NSDate *lastMenses = [IMYToolsUserService lastPeriodDate];

            if (lastMenses) {
                [self.datePickerView setSelectWithDate:lastMenses];
            } else if (dueDate) {
                NSDate *lastMensesDate = [dueDate dateBySubtractingDays:280];
                [self.datePickerView setSelectWithDate:lastMensesDate];
            } else {
                NSDate *lastMensesDate = [[NSDate imy_today] dateBySubtractingDays:14];
                [self.datePickerView setSelectWithDate:lastMensesDate];
            }
        }
    } else if (indexPath.row == 1) {
        lb_title.text = IMYString(@"周期天数");
        textField.placeholder = IMYString(@"两次月经第一天的间隔天数");
        self.dayTextField = textField;
        if (self.pickerView == nil) {
            NSMutableArray *dataArray = [[NSMutableArray alloc] initWithCapacity:46];
            for (NSInteger i = 0; i < 46; i++) {
                [dataArray addObject:[NSString stringWithFormat:IMYString(@"%ld天"), i + 15]];
            }
            @weakify(self);
            self.pickerView = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                dataArray:@[dataArray]
                pickerViewTpe:IMYPickerViewTypeCustom
                confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                    @strongify(self);
                    NSInteger index = [resultArray[0] integerValue];
                    self.viewModel.circleDayCount = index + 15;
                    self.dayTextField.text = [NSString stringWithFormat:IMYString(@"%ld天"), (long)self.viewModel.circleDayCount];
                    [self checkInputIsFinished];
                }
                cancelBlock:^{
                    @strongify(self);
                    [self hidePickerView];
                }];

            self.pickerView.backCoverViewColor = [UIColor colorWithWhite:0 alpha:0.4];
            self.pickerView.title = IMYString(@"周期天数");
            NSString *selectDateString = [NSString stringWithFormat:IMYString(@"%ld天"), [IMYToolsUserService mensesPeriodDays]];
            [self.pickerView setSelectWithString:selectDateString];
        }
        self.dayTextField.inputView = self.pickerView;
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

    if (indexPath.row == 0) {
        [self.datePickerView show];
    } else if (indexPath.row == 1) {
        [self.pickerView show];
    }
}

@end
