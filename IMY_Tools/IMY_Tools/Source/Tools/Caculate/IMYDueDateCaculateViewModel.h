//
//  IMYDueDateCaculateViewModel.h
//  IMY_Tools
//
//  Created by 施东苗 on 16/9/9.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <IMYBaseKit/IMYViewKit.h>

@interface IMYDueDateCaculateViewModel : NSObject
@property (strong, nonatomic) NSDate *lastMensesDate;
@property (assign, nonatomic) NSInteger circleDayCount;
@property (strong, nonatomic) NSDate *birthDate;

// 检查未次经期的合法性
- (RACSignal *)checkLastMenses:(NSDate *)date;

// 利用末次经期和周期天数，计算预产期
- (RACSignal *)caculateDueDateWithLastMensesDate:(NSDate *)lastMensesDate circleDay:(NSInteger)circleDay;

@end
