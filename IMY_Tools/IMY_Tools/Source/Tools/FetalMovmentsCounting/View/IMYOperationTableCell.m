//
//  IMYOperationTableCell.m
//  IMYTools
//
//  Created by meetyou on 2025/8/8.
//

#import "IMYOperationTableCell.h"

@interface IMYOperationTableCell()

@property (nonatomic, strong) UILabel * titleLabel;

@property (nonatomic, strong) UIImageView * arrowImageView;

@property (nonatomic, strong) UIView * bgView;

@end

@implementation IMYOperationTableCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setInitUI];
    }
    return self;
}

- (void)setInitUI {
    self.contentView.backgroundColor = [UIColor clearColor];
    self.backgroundColor = [UIColor clearColor];
    [self.contentView addSubview:self.bgView];
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.arrowImageView];
    
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.contentView);
        make.left.mas_equalTo(@(12));
        make.right.mas_equalTo(@(-12));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(@(12));
        make.right.mas_equalTo(@(-31));
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(@(-12));
        make.size.mas_equalTo(CGSizeMake(14, 14));
        make.centerY.equalTo(self.contentView);
    }];
    
}

- (void)config:(NSString*) title {
    self.titleLabel.text = title;
}

#pragma mark - get

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
        [_bgView imy_setBackgroundColorForKey:kCK_White_AN];
        [_bgView imy_drawAllCornerRadius:12];
    }
    return _bgView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont imy_regularWith:17];
        [_titleLabel imy_setTextColorForKey:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _titleLabel;
}

- (UIImageView *)arrowImageView {
    if (!_arrowImageView) {
        _arrowImageView = [UIImageView new];
        [_arrowImageView imy_setImage:@"fm_right_icon"];
    }
    return _arrowImageView;
}

@end
