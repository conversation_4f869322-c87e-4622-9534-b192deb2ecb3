//
//  IMYToolsMainFMCountingChildVC.m
//  IMYTools
//
//  Created by ponyo on 2021/6/28.
//

#import "IMYToolsFMCountingChildVC.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYToolsFMCountingHeaderView.h"
#import "IMYToolsHelpInfoViewController.h"
#import "IMYFMCountSuspensionView.h"
#import "IMYToolsFMCChildView.h"
#import <IMYAdvertisement/IMYAdvertisementSDK.h>
#import "IMYCommonPushTipsView_V2.h"
#import "IMYToolsFMCountingSkinModel.h"
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
#import <IMYEBPublic/IMYEBYoubiTaskManager.h>
#endif
#import "IMYToolsFMCountingExplainAlterView.h"
#import "IMYToolsFMCountingThisDayFinshView.h"
#import "IMYToolsFMCountingThisTimeFinshView.h"
#import "IMYToolsFMFetalHeartCell.h"
#import "IMYToolsFMHowToCountingCell.h"
#import "IMYToolsFMCountingChildViewModel.h"
#import "IMYOperationTableCell.h"
#import "IMYRequestOperateService.h"
#import <IMYYQBasicServices/IMYWidgetFMCountDataManager.h>

static IMYToolsFMCountModel *staticCurrentModel; // 静态属性，当前记录的胎动的model

@interface IMYToolsFMCountingChildVC ()<UITableViewDelegate, UITableViewDataSource, IMYTimerRuningProtocol>

@property (nonatomic, strong) IMYToolsFMCountModel *currentModel; // 当前记录的胎动的model
@property (nonatomic, strong) NSArray *todayModels;               // 今天的所有model
@property (nonatomic, strong) NSArray<IMYReportOperateModel*> *operationModels;           // 运营位
@property (nonatomic, strong) IMYRequestOperateService *operateService;
@property (nonatomic, strong) IMYToolsFMCountSynchornous *synchornous; //数据同步器
@property (nonatomic, assign) BOOL isNotFirstUse;                      //是否第一次使用
@property (nonatomic, assign) BOOL initing; // 是否初始化中。。
@property (nonatomic, assign) BOOL inited;  // 是否初始化完毕
@property (nonatomic, strong) NSString *forecastText;  // 预测文本

@property (nonatomic, strong) UIImageView *gradientImageView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) IMYToolsFMCountingHeaderView *headerView; //头部View
/// 广告皮肤相关
@property (nonatomic, assign) BOOL skinAd;
@property (nonatomic, strong) UIView *adNavView;
@property (nonatomic, strong) UIImageView *adNavImageView;
@property (nonatomic, strong) UIView *adNavColorView;
@property (nonatomic, strong) UIScrollView *adScrollView;
@property (nonatomic, strong) UIView *adHeaderView;
@property (nonatomic, strong) UIImageView *adFooterImageView;
@property (nonatomic, strong) IMYToolsFMCountingSkinModel *skinModel;

@property (nonatomic, strong) IMYCommonPushTipsView_V2 *pushTipsView;//通知，提醒
@property (nonatomic, assign) BOOL shouldCheckNoti;

@property (nonatomic, strong) id<IMYITableViewAdManager> adManager;//广告
@property (nonatomic, strong) IMYAdSignal *adShowViewSignal; // 回调方法
@property (nonatomic, assign) BOOL isViewDidDisappear;
@property (nonatomic, assign) BOOL stopPlayAdVideo;

@property (nonatomic, assign) BOOL is_show_fetal_movement_heart_entrance;//"【小工具】数胎动底部测胎心入口",
@property (nonatomic, strong) IMYToolsFMCountingChildViewModel *viewModel;

@end

#define kIsNotFirstUse @"kIsNotFirstUseFMCounting"
const int MeetyouToolsValidCountIntervalInSecond = 5 * 60;

@implementation IMYToolsFMCountingChildVC

-(void)dealloc {
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
    [[IMYEBYoubiTaskManager shareManager] removeTaskPendantWithKey:@"record_fetal_movement"];
#endif
    [[IMYRequestOperateService sharedManager] clearAllCacheData];
}

#pragma mark - life
- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self prepareData];
    [self prepareUI];
    [self initAds];
    [self addNotification];
    //获取下当前正在数胎动问题
    if (self.currentModel == nil || self.currentModel.totalFMTimes == 0) {
        [IMYToolsFMCountSynchornous getServerMovementLastWithStartat:@"" completed:nil];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.isCounting) {
        // check is record has been removed in other view
        if (self.currentModel = [[IMYToolsFMCountDataManager sharedManager] getCountingRecord]) {
            [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];// 添加计时器
            [self onTick];
            [self.headerView cancelButton:NO animation:NO];
            [self.headerView setTapCount:_currentModel.totalFMTimes];
        } else {
            // abort counting
            self.currentModel = nil;
            [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
        }
    } else {
        [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
    }
    
    if (!_inited) {
        [self initData];
    } else if (![self sameToModels]) {
        /// 数据不一致的时候需要刷新
        self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
        [self.tableView reloadData];
    }
    [self showTopTipIfNeed];
    [[IMYFMCountSuspensionView sharedInstance] dismiss];
    [IMYFMCountSuspensionView sharedInstance].forceDismiss = YES;
}


- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    [IMYFMCountSuspensionView sharedInstance].forceDismiss = NO;
    if (self.isCounting) {
        [UIApplication sharedApplication].idleTimerDisabled = YES;
    }
    
    if (self.isViewDidDisappear && _adManager) {//切换tab,二级页面返回要请求广告7901
        [_adManager.adInfo unlock];
        _adManager.adInfo.position = IMYADPositionFetalMovement;
        [_adManager.adInfo lock];
        [_adManager refreshData];
    }
    self.isViewDidDisappear = NO;
    
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
    [[IMYEBYoubiTaskManager shareManager] showTaskPendantWithKey:@"record_fetal_movement"];
#endif
    
    [self chechShowFinshAlterVeiw];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [[IMYCacheHelper sharedCacheManager] setObject:@(YES) forKey:kIsNotFirstUse];
    
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
    [[IMYEBYoubiTaskManager shareManager] hiddenTaskPendantWithKey:@"record_fetal_movement"];
#endif
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    
    // 移除计时器
    [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    if (self.isCounting) {
        // save process
        dispatch_async(dispatch_get_global_queue(0, 0), ^() {
            [[IMYToolsFMCountDataManager sharedManager] updateRecord:self.currentModel];
        });
        [[IMYFMCountSuspensionView sharedInstance] show];
        [self.headerView saveTipLabelText];

    } else {
        [[IMYFMCountSuspensionView sharedInstance] destory];
        [self.headerView clearTipLabelText];
    }

    [UIApplication sharedApplication].idleTimerDisabled = NO;
    self.isViewDidDisappear = YES;
}

- (void)chechShowFinshAlterVeiw{
    ///判断弹窗逻辑
    if (!self.isCounting) {
        NSArray *todayRecords = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
        if (todayRecords.count > 0) {
            NSArray *todayEndRecords = [IMYToolsFMCountDataManager sharedManager].getTodayEndRecords;
            IMYToolsFMCountModel *lastRecord = todayEndRecords.lastObject;
            BOOL canShow = [IMYToolsFMCountingThisTimeFinshView canShowFinshViewWithModel:lastRecord];
            if (canShow && lastRecord && lastRecord.beginTime) {
                if (!self.showIntroduceVC) {
                    [IMYToolsFMCountingThisTimeFinshView showFMCountingThisTimeWithModel:lastRecord];
                }
                [IMYToolsFMCountingThisTimeFinshView saveHasShowFinshViewWithModel:lastRecord];
            }
        } else {
            NSMutableArray *lastValidIn7DaysItems = [NSMutableArray new];
            for (int i = 0; i < 7; i++) {
                NSDate *date = [NSDate dateWithDaysBeforeNow:i + 1];
                NSString *dateString = date.imy_getOnlyDateString;
                NSArray *items = [[IMYToolsFMCountDataManager sharedManager] getRecords:dateString];
                NSMutableArray *endedItems = [NSMutableArray new];
                for (IMYToolsFMCountModel *model in items) {
                    if (model.isEned) {
                        [endedItems addObject:model];
                    }
                }
                if (endedItems.count > 0) {
                    lastValidIn7DaysItems = endedItems;
                    break;
                }
            }
            IMYToolsFMCountModel *lastRecord = lastValidIn7DaysItems.lastObject;
            BOOL canShow = [IMYToolsFMCountingThisTimeFinshView canShowFinshViewWithModel:lastRecord];
            if (canShow && lastRecord && lastRecord.beginTime) {
                [IMYToolsFMCountingThisTimeFinshView saveHasShowFinshViewWithModel:lastRecord];
                NSString *dateString = lastRecord.beginTime.imy_getOnlyDateString;
                BOOL show = [IMYToolsFMCountingThisDayFinshView canShowFinshViewWithDay:dateString];
                if (show) {
                    if (!self.showIntroduceVC) {
                        [IMYToolsFMCountingThisDayFinshView showFMCountingThisTimeWithDay:dateString];
                    }
                    [IMYToolsFMCountingThisDayFinshView saveHasShowFinshViewWithDay:dateString];
                }
            }
        }
    }
}

#pragma mark - private
- (void)initData {
    if (_inited || _initing) {
        return;
    }
    self.is_show_fetal_movement_heart_entrance = [[IMYToolsFMFetalHeartCell getConfigsCenterWithKey:@"show"] boolValue];
    _initing = YES;
    
    @weakify(self);
    
    dispatch_async(dispatch_get_global_queue(0, 0), ^() {
        IMYToolsFMCountDataManager *db = [IMYToolsFMCountDataManager sharedManager];
        self.todayModels = db.getTodayRecords;
        [self configValidModelToCurrentModel];
        
        self.inited = YES;
        self.initing = NO;
        
        dispatch_async(dispatch_get_main_queue(), ^() {
            @strongify(self);
            if (self.isCounting) {
                if (self.delegate && [self.delegate respondsToSelector:@selector(FMCChilVC:isCounting:)]) {
                    [self.delegate FMCChilVC:self isCounting:YES];
                }
                [self onTick];
                [self getShowMotherCounting];
                [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];// 添加计时器
                
                [self.headerView cancelButton:NO animation:NO];
                [self.headerView setCountingText:self.currentModel.validFMTimes];
                [self.headerView setTapCount:self.currentModel.totalFMTimes];
                if (self.currentModel.totalFMTimes == 0) {
                    [self refreshHeaderViewWithText:IMYString(@"建议左侧躺下或坐下，双手放在肚子两侧\n安静放松地数")];
                }
                //2.2之前升级上来的没有isNotFirstUse -->fix : 5465 数胎动，首次进入时点击底部空白处弹出说明页，说明页无法向下滑动查看更多
                if (!self.isNotFirstUse) {
                    [self reSetIsNotFirstUse];
                }
            }
            [self.headerView loadLastTipLabelText:self.isCounting];
            [self.tableView reloadData];
        });
    });
}

- (void)prepareData {
    self.synchornous = [[IMYToolsFMCountSynchornous alloc] init];
    if ([IMYToolsFMCountDataManager sharedManager].hasLoadData) {
        [self oldSynchornousDatas];
    } else {
        imy_asyncBlock(1, ^{
            [self oldSynchornousDatas];
        });
    }
    
    self.isNotFirstUse = NO;
    self.skinAd = NO;
    
    [self requestOperationData];
}

- (void)requestOperationData {
    /// 青少年模式不显示
    if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
        return;
    }
    @weakify(self);
    IMYVKUserMode mode = [IMYPublicAppHelper shareAppHelper].userMode;
    NSDictionary *params = @{@"page_key":MODULE_FETAL_MOVEMENT, @"mode":@(mode)};
    [self.operateService requestWith:params completeBlock:^(NSError * _Nullable error, NSArray<IMYReportOperateModel *> * _Nullable models, NSDictionary * _Nonnull dict, BOOL isCache) {
        @strongify(self);
        if (models.count <= 0) {
            return;
        }
        self.operationModels = models;
        imy_asyncMainBlock(^{
            [self.tableView reloadData];
        });
    }];
}

- (void)prepareUI {
    self.hideNavBarBottomLine = YES;
    if (self.skinAd) {
        [self prepareSkinUI];
    } else {
        [self prepareDefaultUI];
    }
    [self.view addSubview:self.adScrollView];
    [self.adScrollView addSubview:self.tableView];
    [self addTableHeaderView];
    [self addTableFooterView];
}

- (void)prepareDefaultUI {
    [self.view addSubview:self.gradientImageView];
    self.tableView.frame = CGRectMake(0, 0, SCREEN_WIDTH, self.adScrollView.imy_height);
}

- (void)prepareSkinUI {
    /// 移除默认样式的UI
    [self.gradientImageView removeFromSuperview];
    /// 创建新的
    [self.view addSubview:self.adNavView];
    [self.adNavView addSubview:self.adNavImageView];
    [self.adNavView addSubview:self.adNavColorView];
    /// 因为footer需要透出，所以没办法加在tableView 的footer上，因为footer的层级是最高的
    [self.adScrollView addSubview:self.adHeaderView];
    [self.adScrollView addSubview:self.adFooterImageView];
    /// 背景颜色
    [self.adNavColorView imy_setBackgroundColorForKey:self.skinModel.adSkinNavColor];
    [self.adScrollView imy_setBackgroundColorForKey:self.skinModel.adSkinBgColor];
    [self.adScrollView bringSubviewToFront:self.tableView];
    [self updateTop:0];
    self.tableView.frame = CGRectMake(0, self.tableView.imy_top, SCREEN_WIDTH, self.adScrollView.imy_height);
}

- (void)addContentObserve {
    /// 监听contentSize
    @weakify(self);
    [[RACObserve(self.tableView, contentSize) deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        if (self.skinAd) {
            /// 头部高度计算，减去往下透的距离
            CGFloat offset = [IMYToolsFMCountingSkinModel imageOffset];
            CGFloat imageTop = self.tableView.imy_top + self.tableView.contentSize.height - offset - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
            self.adFooterImageView.imy_top = imageTop;
            self.adScrollView.contentSize = CGSizeMake(self.adScrollView.imy_width, imageTop + self.adFooterImageView.imy_height);
            self.tableView.imy_height = self.tableView.contentSize.height;
        } else {
            self.adScrollView.contentSize = CGSizeMake(self.adScrollView.imy_width, self.tableView.contentSize.height + self.tableView.imy_top);
            self.tableView.imy_height = self.tableView.contentSize.height;
        }
    }];
    
    [[RACObserve(self.adScrollView, contentOffset) deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        if (!self.skinAd) {
            if (!self.adScrollView.bounces) {
                self.adScrollView.bounces = YES;
            }
            return;
        }
        ///有广告不需要弹性
        self.adScrollView.bounces = NO;
        ///滑动区域满20pt时，标题栏背景色透明度100%
        if (self.adScrollView.contentOffset.y > 20) {
            self.adNavColorView.alpha = 1;
        } else {
            if (self.adScrollView.contentOffset.y < 0) {
                self.adNavColorView.alpha = 0;
            } else {
                self.adNavColorView.alpha = self.adScrollView.contentOffset.y/20;
            }
        }
    }];
    [[[RACObserve(self.headerView, isShowHowManyMother) takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id hasDataValue) {
        @strongify(self);
        if ([hasDataValue boolValue]) {
            self.currentModel.motherCountingLastFMTime = [NSDate date];
        } else {
            self.currentModel.motherCountingLastFMTime = nil;
        }
    }];

}

- (void)addNotification {
    [self addContentObserve];
    @weakify(self);
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil] takeUntil: self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self);
        [self saveDataToDB];
    }];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillEnterForegroundNotification object:nil] takeUntil: self.rac_willDeallocSignal] subscribeNext:^(id x) {
        @strongify(self);
        [self refreshUIWhenEnterForeground];
    }];
    
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillTerminateNotification object:nil] subscribeNext:^(id x) {
        @strongify(self);
        if (self.isCounting) {
            [[IMYFMCountSuspensionView sharedInstance] appExit];
        }
    }];
    
    [[NSNotificationCenter.defaultCenter rac_addObserverForName:UIApplicationDidBecomeActiveNotification object:nil] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        if (self.shouldCheckNoti) {
            self.shouldCheckNoti = NO;
            BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
            if (enabledSystemNotification) {
                IMYURI *uri = [IMYURI uriWithPath:@"localRemind/open" params:@{@"type":@(33)} info:nil];
                [IMYURIManager.shareURIManager runActionWithURI:uri completed:^(IMYURIActionBlockObject *actionObject) {
                    @strongify(self);
                    [self showTopTipIfNeed];
                    if (!self.isCounting) {
                        [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态") ];
                    }
                    [UIView imy_showTextHUD:@"数胎动提醒设置成功"];
                }];
            }
        }
        if (self.isCounting && self.isViewLoaded) {//从后台进入数胎动页面（正在计时）时，刷新数据
            [self getShowMotherCounting];
        }
        if (self.currentModel.beginTime && self.isViewLoaded) {//从后台进入数胎动页面（正在计时）时，刷新数据
            [self.headerView resetNormalAnimtion];
        }
    }];
    //刷新 UI
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYToolsFMCountDataManager_ReloadUI_Notify" object:nil] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        self.currentModel = [[IMYToolsFMCountDataManager sharedManager] getCountingRecord];
        [self beginCountingUIHandle];
        [self handleCount:YES];
        [self refreshUIWhenEnterForeground];
    }];
     
    
}

-(void)refreshHeaderViewWithText:(NSString *)content {
    [self refreshHeaderViewWithText:content motherCounting:-1];
}

-(void)refreshHeaderViewWithText:(NSString *)content motherCounting:(NSInteger)motherCounting{
    NSDictionary *params = @{@"type":@(33)};
    __block BOOL isPushOn;
    [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/hasOpenPush" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
        isPushOn = [result boolValue];
    }];
    BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
    __block NSDate *date;
    [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/nextRemindTime" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
        date = result;
    }];
    
    if (!self.isCounting && [[IMYToolsFMCountDataManager sharedManager].allData count] == 0 ) {
        NSString *dateString;
        content = @"每天早、中、晚数一数胎动吧\n如何正确数胎动？";
        NSString *uriString = @"meiyou:///tools/encyclopedia/detail?params=eyJlbnRyeV9pZCI6IjE1OTEifQ==";
#ifdef DEBUG
        uriString = @"meiyou:///tools/encyclopedia/detail?params=eyJlbnRyeV9pZCI6IjE1ODUifQ==";
#endif
        [self.headerView setTipText:content linkText:@"arrow" linkUrl:uriString];
    } else {
        [self.headerView setTipText:content motherCounting:motherCounting];
    }
}

- (void)refreshUIWhenEnterForeground {
    if (!self.isCounting) {
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        // 移除计时器
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
        [[IMYToolsFMCountDataManager sharedManager].giManager cancel];
        self.currentModel = nil;
        [self endRecordUIAction:NO];
        [self.tableView reloadData];
        [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
    } else {
        [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
        [self onTick];
    }
}

- (void)addTableHeaderView {
    CGFloat containerViewT = 12;
    CGFloat headerViewH = 379;
    UIView *containerView = [UIView new];
    containerView.frame = CGRectMake(0, 0, self.view.imy_width, headerViewH + containerViewT + 8);
    
    CGFloat margin = 12;
    CGFloat headerViewW = containerView.imy_width - 2*margin;
    self.headerView = [[IMYToolsFMCountingHeaderView alloc] initWithFrame:CGRectMake(margin, containerViewT, headerViewW, headerViewH)];
    [self.headerView imy_setBackgroundColorForKey:kCK_White_AN];
    self.headerView.layer.cornerRadius = 12;
    self.headerView.layer.masksToBounds = YES;
    [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
    @weakify(self);
    self.headerView.cancelBlcok = ^(UIButton *button) {
        @strongify(self);
        [self cancelAction:button];
    };
    
    self.headerView.onRingBlock = ^() {
        @strongify(self);
        [self onRingTaped];
    };
    [containerView addSubview:self.headerView];
    self.tableView.tableHeaderView = containerView;
}

- (void)updateTableFooterView {
    [self addTableFooterView];
}

- (void)addTableFooterView {
    /// 根据状态判断是否需要显示
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
}

#pragma mark 取消数胎动 点击事件
- (void)cancelAction:(UIButton *)buttn {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_qxtc_ryqx",@"action":@1} headers:nil completed:nil];
    @weakify(self);
    [IMYActionMessageBox showBoxWithTitle:nil
                                  message:IMYString(@"不足1小时的记录不准，取消后不会保存哦")
                                    style:IMYMessageBoxStyleFlat
                        isShowCloseButton:NO
                            textAlignment:NSTextAlignmentCenter
                        cancelButtonTitle:IMYString(@"继续计时")
                         otherButtonTitle:IMYString(@"仍要取消")
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        [messageBox dismiss];
        @strongify(self);
        if (sender == messageBox.rightButton) {
            [self cancelRecord];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_qxtc_ryqx",@"action":@2} headers:nil completed:nil];
        }
    }];
}

- (void)cancelRecord {
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    self.currentModel.status = IMYToolsFMCountModelStatusCacnel;
    [[IMYToolsFMCountDataManager sharedManager].giManager cancel];
    self.currentModel.totalFMTimes++;//点取消，实际次数 +1,用于服务端判断先后顺序
    [[IMYToolsFMCountDataManager sharedManager] removeRecord:self.currentModel];
    [IMYToolsFMCountSynchornous postFetalMovementSyncWithModel:self.currentModel position:3 completeBlock:nil];
    
    self.currentModel = nil;
    self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
    [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];  // 移除计时器
    [self endRecordUIAction:YES];
    if (self.delegate && [self.delegate respondsToSelector:@selector(FMCChilVC:isCounting:)]) {
        [self.delegate FMCChilVC:self isCounting:NO];
    }
    [self.tableView reloadData];
    [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
    [self.headerView clearTipLabelText];
}

#pragma mark 数据是否一致
- (BOOL)sameToModels {
    NSInteger index = 0;
    NSArray *tmp_todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
    if (tmp_todayModels.count != self.todayModels.count) {
        return NO;//前后数据不相等 则需要重新获取数据
    }
    for (IMYToolsFMCountModel *model in tmp_todayModels) {
        if (model.isEned) {
            if (index < self.todayModels.count && [[self.todayModels imy_objectAtIndex:index] isEqualTo:model]) {
                index ++;
            } else {
                return NO;
            }
        }
    }
    return YES;
}

#pragma mark 帮助说明跳转
- (void)helpInfoAction {
    [IMYToolsFMCountingExplainAlterView showFMCountingExplainView];
}

- (void)showTopTipIfNeed {
    imy_asyncMainBlock(^{
        NSDictionary *params = @{@"type":@(33)};
        __block BOOL isPushOn;
        [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/hasOpenPush" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
            isPushOn = [result boolValue];
        }];
        
        __block BOOL isRemindUsed;
        [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/haveUsed" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
            isRemindUsed = [result boolValue];
        }];
        
        BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
        NSInteger closeCount = [self closePushTipsCount];
        NSDate *lastCloseDate = [self lastCloseDate];
        BOOL moreThan24Hour = (NSDate.date.timeIntervalSince1970 - lastCloseDate.timeIntervalSince1970) > 24 *60 *60;
        if ((isPushOn || !isRemindUsed)&& !enabledSystemNotification &&
            closeCount < 3 && moreThan24Hour &&
            self.isNotFirstUse) {
            if (!self.pushTipsView.superview) {
                NSDictionary *params = @{@"event": @"xgj_stdy_kqtzhf", @"action": @(1)};
                [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:NULL];
                
                [self.adScrollView addSubview:self.pushTipsView];
                [self resetContentOffset];
            } else {
                [self.pushTipsView refreshWithBlock:^(IMYCommonPushTipsBuilder_V2 * _Nonnull builder) {
                    builder.actionTitle = IMYString(@"去开启");
                    builder.title = IMYString(@"打开美柚通知，及时获取数胎动提醒");
                }];
            }
        } else if ((!isPushOn && !isRemindUsed) && enabledSystemNotification && moreThan24Hour &&
                   closeCount < 3 && self.isNotFirstUse) {
            [self.pushTipsView refreshWithBlock:^(IMYCommonPushTipsBuilder_V2 * _Nonnull builder) {
                builder.actionTitle = IMYString(@"去开启");
                builder.title = IMYString(@"打开美柚通知，及时获取数胎动提醒");
            }];
            if (!self.pushTipsView.superview) {
                NSDictionary *params = @{@"event": @"xgj_stdy_kqtxhf", @"action": @(1)};
                [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:NULL];
                
                [self.adScrollView addSubview:self.pushTipsView];
                [self resetContentOffset];
            }
        } else {
            [self hiddenTopTip];
        }
    });
}

- (void)resetContentOffset {
    CGFloat top = self.pushTipsView.imy_height;
    [self updateTop:top];
}

- (void)resetContentOffsetZero {
    [self updateTop:0];
}

- (void)updateTop:(CGFloat)top {
    if (self.skinAd) {
        CGFloat offset = [IMYToolsFMCountingSkinModel imageOffset];
        self.adHeaderView.imy_top = top;
        /// 需要透出40 + 原先tableView headerView距离顶部12
        self.tableView.imy_top = self.adHeaderView.imy_bottom - (offset + 12);
        CGFloat imageTop = self.tableView.imy_top + self.tableView.contentSize.height - offset - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        self.adFooterImageView.imy_top = imageTop;
        self.adScrollView.contentSize = CGSizeMake(self.adScrollView.imy_width, imageTop + self.adFooterImageView.imy_height);
    } else {
        CGFloat tableViewT = top;
        if (top > 0) {
            /// 因为UI要求间距是8，headerView距离顶部12，故需要减4
            tableViewT = top - 4;
        }
        self.tableView.imy_top = tableViewT;
        self.adScrollView.contentSize = CGSizeMake(self.adScrollView.imy_width, self.tableView.contentSize.height + self.tableView.imy_top);
    }
}

- (void)hiddenTopTip {
    if (self.pushTipsView.superview) {
        [_pushTipsView removeFromSuperview];
        _pushTipsView = nil;
        [self resetContentOffsetZero];
    }
}

- (NSInteger)closePushTipsCount {
    NSDictionary *params = @{@"type":@(33)};
    __block BOOL isPushOn;
    [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/hasOpenPush" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
        isPushOn = [result boolValue];
    }];
    BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
    if (!isPushOn && enabledSystemNotification) {
        NSArray *dateArr = [IMYCacheHelper.sharedCacheManager objectForKey:@"kUserClosedFMRenindTipCount"];
        return dateArr.count;
    } else {
        NSArray *dateArr = [IMYCacheHelper.sharedCacheManager objectForKey:@"kUserClosedFMPushTipCount"];
        return dateArr.count;
    }
}

- (void)addClosePushTipsCount {
    NSDictionary *params = @{@"type":@(33)};
    __block BOOL isPushOn;
    [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/hasOpenPush" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
        isPushOn = [result boolValue];
    }];
    BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
    if (!isPushOn && enabledSystemNotification) {
        NSArray *dateArr = [IMYCacheHelper.sharedCacheManager objectForKey:@"kUserClosedFMRenindTipCount"];
        if (!dateArr) {
            dateArr = @[];
        }
        NSMutableArray *arr = dateArr.mutableCopy;
        [arr addObject:NSDate.date];
        [IMYCacheHelper.sharedCacheManager setObject:arr.copy forKey:@"kUserClosedFMRenindTipCount"];
    } else {
        NSArray *dateArr = [IMYCacheHelper.sharedCacheManager objectForKey:@"kUserClosedFMPushTipCount"];
        if (!dateArr) {
            dateArr = @[];
        }
        NSMutableArray *arr = dateArr.mutableCopy;
        [arr addObject:NSDate.date];
        [IMYCacheHelper.sharedCacheManager setObject:arr.copy forKey:@"kUserClosedFMPushTipCount"];
    }
}

-(NSDate *)lastCloseDate {
    NSDictionary *params = @{@"type":@(33)};
    __block BOOL isPushOn;
    [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"localRemind/hasOpenPush" params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
        isPushOn = [result boolValue];
    }];
    BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
    NSArray *dateArr;
    if (!isPushOn && enabledSystemNotification) {
        dateArr = [IMYCacheHelper.sharedCacheManager objectForKey:@"kUserClosedFMRenindTipCount"];
    } else {
        dateArr = [IMYCacheHelper.sharedCacheManager objectForKey:@"kUserClosedFMPushTipCount"];
    }
    [dateArr sortedArrayUsingComparator:^NSComparisonResult(NSDate *obj1, NSDate *obj2) {
        return [obj1 compare:obj2];
    }];
    return dateArr.lastObject;
}

#pragma mark - 数据相关
- (void)getShowMotherCounting {
    @weakify(self);
    [self.viewModel requestUsersActiveCountDataWithCompleteBlock:^(BOOL bSuccess, NSInteger count) {
        @strongify(self);
        if (bSuccess
            && (self.headerView.isShowHowManyMother || self.currentModel.totalFMTimes > 0) ) {
            NSString *content = [NSString stringWithFormat:@"此刻有%ld位妈妈正在数胎动\n一起努力守护宝宝吧",count];
            if (!self.headerView.totosTipLabel.hidden){
                content = [NSString stringWithFormat:@"此刻有%ld位妈妈正在数胎动",count];
            }
            if (count <= 0) {
                content = @"宝宝开始活动咯，\n宝妈可以听听胎教音乐和胎教故事，放松心情";
            }
            //超过半个小时未记数
            if (self.currentModel.lastFMTime.timeIntervalSinceNow < -30 * 60) {
                content = @"亲爱的妈妈，宝宝睡着了吗？\n你可以站起来走一走，把宝宝从美梦中唤醒";
            }
            [self refreshHeaderViewWithText:content];
        }
    }];
}

#pragma mark 数据同步
- (void)synchornousDatas {
    IMYToolsFMCountModel *tempModel = self.currentModel;
    @weakify(self,tempModel);
    //1.同步数据
    if ([[staticCurrentModel.beginTime imy_getDateTimeString] isEqualToString:[self.currentModel.beginTime imy_getDateTimeString]]
        && staticCurrentModel.totalFMTimes > self.currentModel.totalFMTimes) {
        //数据异常了
        tempModel = staticCurrentModel;
    }
    [IMYToolsFMCountSynchornous postFetalMovementSyncWithModel:tempModel position:4 completeBlock:^(BOOL success, NSError *error) {
        @strongify(self,tempModel);
        if (success) {
            //同步到本地数据
            [[IMYToolsFMCountDataManager sharedManager] setSynYes:@[tempModel]];
            self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
            [self configValidModelToCurrentModel];
            
            imy_asyncMainBlock(1, ^{
                //2、同步完后，拉取所有数据
                @weakify(self);
                [self.synchornous synchornousNetToLocal:^(BOOL success, NSError *error) {
                    @strongify(self);
                    if (success) {
                        [self reSetIsNotFirstUse];
                        self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
                        [self configValidModelToCurrentModel];
                        [self.tableView reloadData];
                    }else if (error && [error isKindOfClass:[NSError class]]){
                        [IMYErrorTraces postWithType:IMYErrorTraceTypeLocalDataFails
                                            pageName:@"IMYToolsMainFMCountingVC"
                                            category:IMYErrorTraceCategoryYunyu
                                             message:@"数胎动 同步本地数据 error"
                                              detail:@{
                            @"code":@(error.code),
                            @"reason" : error.localizedFailureReason ?: error.localizedDescription
                        }];
                    }
                }];

            });
        }else if (error && [error isKindOfClass:[NSError class]]){
            [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                pageName:@"IMYToolsMainFMCountingVC"
                                category:IMYErrorTraceCategoryYunyu
                                 message:@"数胎动 同步网络 error"
                                  detail:@{
                @"code":@(error.code),
                @"reason" : error.localizedFailureReason ?: error.localizedDescription
            }];
        }
    }];
}

/// 869 之前 异步同步逻辑
- (void)oldSynchornousDatas {
    @weakify(self);
    [self.synchornous synchornousLocalToNet:^(BOOL success, NSError *error) {
        @strongify(self);
        if (success) {
            self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
            [self configValidModelToCurrentModel];

            imy_asyncMainBlock(1, ^{
                //2、同步完后，拉取所有数据
                @weakify(self);
                [self.synchornous synchornousNetToLocal:^(BOOL success, NSError *error) {
                    @strongify(self);
                    if (success) {
                        [self reSetIsNotFirstUse];
                        self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
                        [self configValidModelToCurrentModel];
                        [self.tableView reloadData];
                    }else if (error && [error isKindOfClass:[NSError class]]){
                        [IMYErrorTraces postWithType:IMYErrorTraceTypeLocalDataFails
                                            pageName:@"IMYToolsMainFMCountingVC"
                                            category:IMYErrorTraceCategoryYunyu
                                             message:@"数胎动 同步本地数据 error"
                                              detail:@{
                                                       @"code":@(error.code),
                                                       @"reason" : error.localizedFailureReason ?: error.localizedDescription
                                                     }];
                    }
                }];

            });
        }else if (error && [error isKindOfClass:[NSError class]]){
            [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                pageName:@"IMYToolsMainFMCountingVC"
                                category:IMYErrorTraceCategoryYunyu
                                 message:@"数胎动 同步网络 error"
                                  detail:@{
                                           @"code":@(error.code),
                                           @"reason" : error.localizedFailureReason ?: error.localizedDescription
                                         }];
        }
    }];
}

- (void)configValidModelToCurrentModel {
    self.currentModel = [[IMYToolsFMCountDataManager sharedManager] getCountingRecord];
}

- (void)reSetIsNotFirstUse {
    if (self.isNotFirstUse) {
        return;
    }

    BOOL isAnyEnded = [[IMYToolsFMCountDataManager sharedManager] isAnyEnded];
    BOOL hasAnyTodayRecord = [[IMYToolsFMCountDataManager sharedManager] getTodayRecords].count > 0 ? YES : NO;
    BOOL isNotFirstUse = isAnyEnded || hasAnyTodayRecord;
    if (self.isNotFirstUse != isNotFirstUse) {
        self.isNotFirstUse = isNotFirstUse;
        [[IMYCacheHelper sharedCacheManager] setObject:@(self.isNotFirstUse) forKey:kIsNotFirstUse];
        [self updateTableFooterView];
    }
}

- (BOOL)isCounting {
    if (self.currentModel && ![self.currentModel isEned]) {
        return YES;
    } else {
        return NO;
    }
}

- (void)saveDataToDB {
    if (self.isCounting) {
        [[IMYToolsFMCountDataManager sharedManager] updateRecord:_currentModel];
    }
}

#pragma mark - 计数
- (void)onTick {
//#ifdef DEBUG
//    NSInteger left = 660 + self.currentModel.beginTime.timeIntervalSinceNow;
//#else
    NSInteger left = 3600 + self.currentModel.beginTime.timeIntervalSinceNow;
//#endif
    if (left < 0) {
        [self finishCounting];
        [self showEndRecordTip];
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];

    } else {
        // update tick visual
        NSString *countingStr = [NSString stringWithFormat:@"%02d:%02ld", left / 60, left % 60];
        self.headerView.timingLabel.text = countingStr;

        if (_currentModel.lastFMTime.timeIntervalSinceNow < -30 * 60) {
            [self refreshHeaderViewWithText:IMYString(@"亲爱的妈妈，宝宝睡着了吗？\n你可以站起来走一走，把宝宝从美梦中唤醒")];
        }

        //距离上一次有效点击大于5min，隐藏5min提醒。
        if (_currentModel.lastFMTime.timeIntervalSinceNow < -MeetyouToolsValidCountIntervalInSecond) {
            if (self.headerView.totosTipLabel.hidden != YES) {
                NSString *content = [NSString stringWithFormat:@"此刻有%ld位妈妈正在数胎动\n一起努力守护宝宝吧",self.viewModel.motherCounting];
                if (self.viewModel.motherCounting <= 0) {
                    content = @"宝宝开始活动咯，\n宝妈可以听听胎教音乐和胎教故事，放松心情";
                }
                [self refreshHeaderViewWithText:content];
            }
            self.headerView.totosTipLabel.hidden = YES;
            [self.headerView checkBgViewHeight];
        }
    }
    //说明大于 1 分钟了，重置，去请求接口
    if (self.isViewActived
        && self.currentModel.totalFMTimes >= 0
        && _currentModel.motherCountingLastFMTime
        && _currentModel.motherCountingLastFMTime.timeIntervalSinceNow < -60) {
        [self getShowMotherCounting];
        _currentModel.motherCountingLastFMTime = [NSDate date];
    } else if (self.currentModel.totalFMTimes > 0 && 
               self.currentModel.motherCountingLastFMTime == nil
               && self.currentModel.lastFMTime.timeIntervalSinceNow > -30 * 60){
        self.currentModel.motherCountingLastFMTime = [NSDate date];
    }

}

- (void)onRingTaped {
    if (!_inited)
        return;

    if (self.isCounting) {
        [self handleCount];
        [IMYEventHelper event:@"std-td"];
    } else {
        [self beginCounting];
        [IMYEventHelper event:@"std-ks"];
    }
}

#pragma - mark 开始计数
- (void)beginCounting {
    if (!self.isCounting) {
        [self getShowMotherCounting];
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_start",@"action":@2} headers:nil completed:nil];

        [UIApplication sharedApplication].idleTimerDisabled = YES;
        [self beginCountingDataHandle];
        [self beginCountingUIHandle];
    }
}

- (void)beginCountingDataHandle {
    //获取下当前正在数胎动问题
    self.currentModel = [[IMYToolsFMCountModel alloc] init];
    self.currentModel.beginTime = [NSDate date];
    self.currentModel.lastFMTime = [NSDate date];
    self.currentModel.status = IMYToolsFMCountModelStatusDoing;
    [[IMYToolsFMCountDataManager sharedManager] addRecord:_currentModel];
    [[IMYToolsFMCountDataManager sharedManager].giManager startWithBeginTime:_currentModel.beginTime];
    self.todayModels = [IMYToolsFMCountDataManager sharedManager].getTodayRecords;
 
    if (!self.isNotFirstUse) {
        self.isNotFirstUse = YES;
        [[IMYCacheHelper sharedCacheManager] setObject:@(YES) forKey:kIsNotFirstUse];
        [self updateTableFooterView];
    }
    ///上报接口
    [IMYToolsFMCountSynchornous postFetalMovementSyncWithModel:self.currentModel position:5 completeBlock:nil];
    [IMYToolsFMCountSynchornous saveStartFMActionWithModel:self.currentModel];
}

- (void)beginCountingUIHandle {
    if (self.delegate && [self.delegate respondsToSelector:@selector(FMCChilVC:isCounting:)]) {
        [self.delegate FMCChilVC:self isCounting:YES];
    }
    [self.headerView beginCountingUIHandle];
    //第一次点击开始的时候，不允许气泡动画。
    self.headerView.enableBubble = NO;

    // 添加计时器
    [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
    [self onTick]; // simulate a tick for update visual immediately.

    [self.tableView reloadData];
}

#pragma - mark 计数中,过程点击事件
- (void)handleCount {
    [self handleCount:NO];
}
- (void)handleCount:(BOOL)isSever {
    if (self.isCounting) {
        [self refreshUI:isSever];
        [self uploadCurrentModel];
    }
}

- (void)refreshUI:(BOOL)isSever {
    if (!self.isCounting) {
        return;
    }
    if (self.viewModel.motherCounting == 0) {
        [self getShowMotherCounting];
    }
    NSString *content = [NSString stringWithFormat:@"此刻有%ld位妈妈正在数胎动\n一起努力守护宝宝吧",self.viewModel.motherCounting];
    if (!isSever) {
        self.currentModel.totalFMTimes++;
    }
    if (self.currentModel.lastFMTime.timeIntervalSinceNow < -MeetyouToolsValidCountIntervalInSecond || self.currentModel.validFMTimes == 0) {
        // if 5 min passed
        //第一次点击，特殊处理一下。
        self.currentModel.lastFMTime = [NSDate date];
        if (!isSever) {
            self.currentModel.validFMTimes++;
        }
        self.headerView.enableBubble = YES;
        self.headerView.totosTipLabel.hidden = YES;
    } else {
        self.headerView.totosTipLabel.hidden = NO;
        //5分钟之内的不允许气泡动画
        self.headerView.enableBubble = YES;
        content = [NSString stringWithFormat:@"此刻有%ld位妈妈正在数胎动",self.viewModel.motherCounting];
    }
    [self.headerView checkBgViewHeight];
    if (self.viewModel.motherCounting <= 0) {
        content = @"宝宝开始活动咯，\n宝妈可以听听胎教音乐和胎教故事，放松心情";
    }
    [self refreshHeaderViewWithText:content motherCounting:self.viewModel.motherCounting];
    // update visual
    [self.headerView setCountingText:_currentModel.validFMTimes];
    [self.headerView setTapCount:_currentModel.totalFMTimes];
}

- (void)uploadCurrentModel {
    [[IMYToolsFMCountDataManager sharedManager] updateRecord:_currentModel];
#if __has_include(<IMYEBPublic/IMYEBYoubiTaskManager.h>)
    [[IMYEBYoubiTaskManager shareManager] oprationTaskFinishedWithKey:@"record_fetal_movement" uploadParams:nil];
#endif
    ///上报接口
    [IMYToolsFMCountSynchornous postFetalMovementSyncWithModel:self.currentModel position:6 completeBlock:nil];
}

#pragma mark 结束计数
- (void)finishCounting {
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    // 移除计时器
    [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    
    [[IMYToolsFMCountDataManager sharedManager].giManager cancel];
    self.currentModel.status = IMYToolsFMCountModelStatusDone;
    [[IMYToolsFMCountDataManager sharedManager] updateRecord:_currentModel];
    
    [self synchornousDatas];

    [self showFinshView];
    _currentModel = nil;
    [self endRecordUIAction:YES];
    [self.tableView reloadData];

}

- (void)showFinshView{
    ///本次数胎动已完成 -是否弹窗逻辑判断
    if ([_currentModel.beginTime isToday]) {
        BOOL canShow = [IMYToolsFMCountingThisTimeFinshView canShowFinshViewWithModel:_currentModel];
        if (canShow) {
            [IMYToolsFMCountingThisTimeFinshView showFMCountingThisTimeWithModel:_currentModel];
            [IMYToolsFMCountingThisTimeFinshView saveHasShowFinshViewWithModel:_currentModel];
        }
    } else {
        BOOL canShow = [IMYToolsFMCountingThisTimeFinshView canShowFinshViewWithModel:_currentModel];
        if (canShow && _currentModel && _currentModel.beginTime) {
            [IMYToolsFMCountingThisTimeFinshView saveHasShowFinshViewWithModel:_currentModel];
            NSString *dateString = _currentModel.beginTime.imy_getOnlyDateString;
            BOOL show = [IMYToolsFMCountingThisDayFinshView canShowFinshViewWithDay:dateString];
            if (show) {
                [IMYToolsFMCountingThisDayFinshView showFMCountingThisTimeWithDay:dateString];
                [IMYToolsFMCountingThisDayFinshView saveHasShowFinshViewWithDay:dateString];
            }
        }
    }
}

- (void)endRecordUIAction:(BOOL)needPullUp {
    [self.headerView endCountingUIHandle];
    if (self.delegate && [self.delegate respondsToSelector:@selector(FMCChilVC:isCounting:)]) {
        [self.delegate FMCChilVC:self isCounting:NO];
    }
}

- (void)showEndRecordTip {
//    [UIWindow imy_showTextHUD:IMYString(@"真棒！要继续坚持记录宝宝健康状况哦~")];
    [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
}

#pragma mark - IMYTimerRuningProtocol
- (void)imy_timerRuning {
    [self onTick];
}

#pragma mark - UITableViewDelegate, UITableViewDataSource

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0 && self.operationModels.count > 0) {
        // 跳转
        IMYReportOperateModel * model = self.operationModels.firstObject;
        if (model && model.uri.length > 0) {
            IMYURI * uri = [IMYURI uriWithURIString: model.uri];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        }
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 3 + self.is_show_fetal_movement_heart_entrance;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return self.operationModels.count;
    }
    
    if (section == 1) {
        /// 没数据展示空值图，有数据需要加头加尾
        if (self.todayModels.count == 0) {
            return 1 ;
        }
        return 1 + self.todayModels.count + 1 ;
    } else {
        return 1 ;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
        if (indexPath.section == 0) {
            static NSString *cellIdentifier = @"IMYOperationTableCell";
            IMYOperationTableCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
            if (!cell) {
                cell = [[IMYOperationTableCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
            }
            [cell config:self.operationModels[indexPath.row].content];
            return cell;
        } else if (indexPath.section == 1) {
            
            if (self.todayModels.count == 0) {
                static NSString *cellIdentifier = @"IMYToolsFMCChildEmptyCell";
                IMYToolsFMCChildEmptyCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
                if (!cell) {
                    cell = [[IMYToolsFMCChildEmptyCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
                }
                return cell;
            }
            if (indexPath.row == 0) {
                static NSString *cellIdentifier = @"IMYToolsFMCChildHeaderCell";
                IMYToolsFMCChildHeaderCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
                if (!cell) {
                    cell = [[IMYToolsFMCChildHeaderCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
                }
                return cell;
            } else if (indexPath.row == (self.todayModels.count + 1)) {
                static NSString *cellIdentifier = @"IMYToolsFMCChildFooterCell";
                IMYToolsFMCChildFooterCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
                if (!cell) {
                    cell = [[IMYToolsFMCChildFooterCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
                }
                NSInteger count = 0;
                CGFloat totalTimes = 0.0;
                CGFloat totalHours = 0.0;
                for (IMYToolsFMCountModel *item in self.todayModels) {
                    if (item.isEned) {
                        ++totalHours;
                        totalTimes += item.validFMTimes;
                    }
                }
                if (totalHours > 0) {
                    count = roundf(totalTimes / totalHours * 12);
                }
                [cell setupCellWithTotal:count];
                @weakify(self);
                cell.footerView.helpBlock = ^{
                    @strongify(self);
                    [self helpInfoAction];
                };
                cell.footerView.titleLabel.text = self.forecastText;
                return cell;
            } else {
                static NSString *cellIdentifier = @"IMYToolsFMCChildCell";
                IMYToolsFMCChildCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
                if (!cell) {
                    cell = [[IMYToolsFMCChildCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
                }
                cell.recordModel = [self.todayModels imy_objectAtIndex:indexPath.row - 1];
                return cell;
            }
    } else if (indexPath.section == 2) {
        static NSString *cellIdentifier = @"IMYToolsFMHowToCountingCell";
        IMYToolsFMHowToCountingCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
        if (!cell) {
            cell = [[IMYToolsFMHowToCountingCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
        }
        cell.imyut_eventInfo.ableToClean = YES;
        cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"IMYToolsFMHowToCountingCell_%@_%ld", [IMYPublicAppHelper shareAppHelper].userid, indexPath.row];
        @weakify(self);
        cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_wzmkz",@"action":@1} headers:nil completed:nil];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_wzmky",@"action":@1} headers:nil completed:nil];
        };
        return cell;

    } else {
        static NSString *cellIdentifier = @"IMYToolsFMFetalHeartCell";
        IMYToolsFMFetalHeartCell *cell = [self.tableView dequeueReusableCellWithIdentifier:cellIdentifier];
        if (!cell) {
            cell = [[IMYToolsFMFetalHeartCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
        }
        cell.imyut_eventInfo.ableToClean = YES;
        cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"IMYToolsFMFetalHeartCell_%@_%ld", [IMYPublicAppHelper shareAppHelper].userid, indexPath.row];
        @weakify(self);
        cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_txyjc",@"action":@1} headers:nil completed:nil];
        };
        return cell;

    }
   
    
    return [UITableViewCell new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
     if (indexPath.section == 0) {
         return 48;
     } else if (indexPath.section == 1) {
        if (self.todayModels.count == 0) {
            return 197;
        }
        if (indexPath.row == 0) {
            return 88;
        } else if (indexPath.row == (self.todayModels.count + 1)) {
            return 44 + 12;
        } else {
            return 44;
        }
    } else if (indexPath.section == 2) {
        return 94;

    } else {
        return 108;
    }
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 8;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *view = [UIView new];
    view.backgroundColor = [UIColor clearColor];
    return view;
}

#pragma mark - setter or getter

- (IMYRequestOperateService *)operateService {
    if (!_operateService) {
        _operateService = [IMYRequestOperateService sharedManager];
        IMYVKUserMode mode = [IMYPublicAppHelper shareAppHelper].userMode;
        _operateService.cacheKey = [NSString stringWithFormat:@"v3/operation_guide_module_%@_mode_%@",MODULE_FETAL_MOVEMENT,@(mode)];
    }
    return _operateService;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) style:UITableViewStylePlain];
        _tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.scrollEnabled = NO;
        
        [_tableView registerClass:[IMYOperationTableCell class] forCellReuseIdentifier:@"IMYOperationTableCell"];
    }
    return _tableView;
}

- (UIImageView *)gradientImageView {
    if (!_gradientImageView) {
        _gradientImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, self.adNavView.imy_top, SCREEN_WIDTH, 261)];
        [_gradientImageView imy_setImageForKey:@"ctx_img_bg_top"];
    }
    return _gradientImageView;
}

- (IMYCommonPushTipsView_V2 *)pushTipsView {
    if (!_pushTipsView) {
        _pushTipsView = [IMYCommonPushTipsView_V2 tipsViewWithBlock:^(IMYCommonPushTipsBuilder_V2 * _Nonnull builder) {
            builder.frame = CGRectMake(0, 0, SCREEN_WIDTH, 36.f);
            builder.title = IMYString(@"打开美柚通知，及时获取数胎动提醒");
            builder.margin = 12;
        }];
        @weakify(self);
        _pushTipsView.closeAction = ^{
            @strongify(self);
            BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
            if (enabledSystemNotification) {
                NSDictionary *params = @{@"event": @"xgj_stdy_kqtxhf", @"action": @(2),@"public_type":@"取消"};
                [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:NULL];
            } else {
                NSDictionary *params = @{@"event": @"xgj_stdy_kqtzhf", @"action": @(2),@"public_type":@"取消"};
                [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:NULL];
            }
            
            [UIView animateWithDuration:0.15 animations:^{
                self.pushTipsView.alpha = 0;
                [self resetContentOffsetZero];
            } completion:^(BOOL finished) {
                [self.pushTipsView removeFromSuperview];
                self.pushTipsView.alpha = 1.0;
            }];
            [self addClosePushTipsCount];
        };
        _pushTipsView.tapAction = ^{
            @strongify(self);
            NSDictionary *params = @{@"type":@(33)};
            BOOL enabledSystemNotification = IMYPublicAppHelper.shareAppHelper.enabledNotification;
            if (enabledSystemNotification) {
                NSDictionary *biParams = @{@"event": @"xgj_stdy_kqtxhf", @"action": @(2),@"public_type":@"立即开启"};
                [IMYGAEventHelper postWithPath:@"event" params:biParams headers:nil completed:NULL];
                
                IMYURI *uri = [IMYURI uriWithPath:@"localRemind/open" params:params info:nil];
                [IMYURIManager.shareURIManager runActionWithURI:uri completed:^(IMYURIActionBlockObject *actionObject) {
                    @strongify(self);
                    if (!self.isCounting) {
                        [self refreshHeaderViewWithText:IMYString(@"每天早、中、晚数一数胎动吧\n关注宝宝在子宫内的健康状态")];
                    }
                    [UIView imy_showTextHUD:@"数胎动提醒设置成功"];
                    [self showTopTipIfNeed];
                }];
            } else {
                NSDictionary *params = @{@"event": @"xgj_stdy_kqtzhf", @"action": @(2),@"public_type":@"去开启"};
                [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:NULL];
                self.shouldCheckNoti = YES;
                NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                UIApplication *application = [UIApplication sharedApplication];
                if ([application canOpenURL:url]) {
                    [application openURL:url options:@{} completionHandler:nil];
                }
            }
        };
    }
    return _pushTipsView;
}

- (UIView *)adNavView {
    if (!_adNavView) {
        _adNavView = [[UIView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        _adNavView.clipsToBounds = YES;
    }
    return _adNavView;
}

- (UIImageView *)adNavImageView {
    if (!_adNavImageView) {
        _adNavImageView = [UIImageView new];
        _adNavImageView.contentMode = UIViewContentModeScaleAspectFill;
        /// 因为分为上下2部分
        /// 状态栏高度各个机型不一样，为了简化，配置最大的，故需要偏移
        CGFloat adNavImageViewT = [self maxStatusBarHeight] - SCREEN_STATUSBAR_HEIGHT;
        _adNavImageView.frame = CGRectMake(0, -adNavImageViewT, [self.skinModel adSkinHeaderIconSize].width, [self.skinModel adSkinHeaderIconSize].height);
        if (self.skinModel.adSkinHeaderImage) {
            _adNavImageView.image = self.skinModel.adSkinHeaderImage;
        } else if ([self.skinModel.adSkinHeaderIcon hasPrefix:@"http"]) {
            [_adNavImageView imy_setImageURL:[NSURL URLWithString:self.skinModel.adSkinHeaderIcon]];
        } else {
            [_adNavImageView imy_setImage:self.skinModel.adSkinHeaderIcon];
        }
    }
    return _adNavImageView;
}

- (UIView *)adNavColorView {
    if (!_adNavColorView) {
        _adNavColorView = [[UIView alloc] initWithFrame:self.adNavImageView.frame];
        _adNavColorView.alpha = 0;
    }
    return _adNavColorView;
}

- (UIScrollView *)adScrollView {
    if (!_adScrollView) {
        _adScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, self.adNavView.imy_bottom, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        _adScrollView.delegate = self;
        _adScrollView.backgroundColor = [UIColor clearColor];
    }
    return _adScrollView;
}

- (UIView *)adHeaderView {
    if (!_adHeaderView) {
        _adHeaderView = [UIView new];
        _adHeaderView.clipsToBounds = YES;
        CGFloat imageHeight = [self.skinModel adSkinHeaderIconSize].height;
        /// 因为分为上下2部分，所以会以最大的导航栏高度来设计
        CGFloat adSkinImageViewT = SCREEN_NAVIGATIONBAR_HEIGHT + [self maxStatusBarHeight];
        UIImageView *adSkinImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, - adSkinImageViewT, [self.skinModel adSkinHeaderIconSize].width, imageHeight)];
        adSkinImageView.contentMode = UIViewContentModeScaleAspectFill;
        if (self.skinModel.adSkinHeaderImage) {
            adSkinImageView.image = self.skinModel.adSkinHeaderImage;
        } else if ([self.skinModel.adSkinHeaderIcon hasPrefix:@"http"]) {
            [adSkinImageView imy_setImageURL:self.skinModel.adSkinHeaderIcon];
        } else {
            [adSkinImageView imy_setImage:self.skinModel.adSkinHeaderIcon];
        }
        _adHeaderView.frame = CGRectMake(0, 0, SCREEN_WIDTH, adSkinImageView.imy_bottom);
        [_adHeaderView addSubview:adSkinImageView];
    }
    return _adHeaderView;
}

- (UIImageView *)adFooterImageView {
    if (!_adFooterImageView) {
        _adFooterImageView = [[UIImageView alloc] init];
        _adFooterImageView.contentMode = UIViewContentModeScaleAspectFill;
        _adFooterImageView.frame = CGRectMake(0, 0, [self.skinModel adSkinFooterIconSize].width, [self.skinModel adSkinFooterIconSize].height);
        if (self.skinModel.adSkinFooterImage) {
            _adFooterImageView.image = self.skinModel.adSkinFooterImage;
        } else if ([self.skinModel.adSkinFooterIcon hasPrefix:@"http"]) {
            NSURL *url = [NSURL URLWithString:[NSString qiniuURL:self.skinModel.adSkinFooterIcon type:IMY_QiNiu_WEBP]];
            [_adFooterImageView imy_setImageURL:url];
        } else {
            [_adFooterImageView imy_setImage:self.skinModel.adSkinFooterIcon];
        }
        if (imy_isNotEmptyString(self.skinModel.adSkinTagTitle)) {
            IMYPaddingLabel *tagLabel = [self tagLabel];
            [_adFooterImageView addSubview:tagLabel];
            CGFloat tagLabelB = 8 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
            CGFloat tagLabelH = 11;
            [tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(12);
                make.height.mas_equalTo(tagLabelH);
                make.bottom.mas_equalTo(-tagLabelB);
            }];
        }
    }
    return _adFooterImageView;
}

- (IMYPaddingLabel *)tagLabel {
    IMYPaddingLabel *label = [IMYPaddingLabel new];
    label.font = [UIFont imy_regularWith:8];
    [label imy_setTextColorForKey:kCK_White_A];
    label.text = self.skinModel.adSkinTagTitle;
    label.cornerRadius = 2;
    label.horizontalPadding = 2;
    label.roundedRectColor = [[UIColor imy_colorForKey:@"000000"] colorWithAlphaComponent:0.2];
    label.backgroundColor = UIColor.clearColor;
    return label;
}

- (IMYToolsFMCountingChildViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [IMYToolsFMCountingChildViewModel new];
    }
    return _viewModel;
}

- (CGFloat)maxStatusBarHeight {
    return 59;
}

- (void)setTodayModels:(NSArray *)todayModels {
    NSInteger count = 0;
    NSMutableArray *tempModels = [NSMutableArray array];
    if (todayModels.count > 0) {
        CGFloat totalTimes = 0.0;
        CGFloat totalHours = 0.0;
        
        for (IMYToolsFMCountModel *model in todayModels) {

            if (model.isEned) {
                ++totalHours;
                totalTimes += model.validFMTimes;
                [tempModels addObject:model];
            }
        }
        if (totalHours > 0) {
            count = roundf(totalTimes / totalHours * 12);
        }
    }
    self.forecastText = [NSString stringWithFormat:@"%@: %ld", IMYString(@"预测胎动"), (long)count];
    _todayModels = [tempModels copy];
}

- (NSString *)ga_pageName {
    return @"IMYToolsMainFMCountingVC";
}

- (void)setCurrentModel:(IMYToolsFMCountModel *)currentModel {
    /*
     1 为空的时候
     2 startTime不等的时候
     3 赋值
     */
  
    if (currentModel
        && (!staticCurrentModel
        || ![[staticCurrentModel.beginTime imy_getDateTimeString] isEqualToString:[currentModel.beginTime imy_getDateTimeString]]
        || staticCurrentModel.totalFMTimes < currentModel.totalFMTimes)) {
        staticCurrentModel = currentModel;
    }
    
    _currentModel = currentModel;
    
    if (currentModel) {
        [IMYWidgetFMCountDataManager saveToUserDefault:currentModel];
    }
}
#pragma mark - 广告
- (void)initAds {
    if (self.isShouldShowLoading && self.delegate && [self.delegate respondsToSelector:@selector(FMCChilVC:isLoading:)]) {
        // 开关控制是否需要loading
        [self.delegate FMCChilVC:self isLoading:YES];
    }
    NSDictionary *userInfo = @{
        @"adShowViewSignal":self.adShowViewSignal,
        @"isRealQuick":@(self.isRealQuick),
        @"isShouldShowLoading":@(self.isShouldShowLoading),
        @"force_stop_play_video":@(self.stopPlayAdVideo)
    };
    IMYAdvertiserInfo *adInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageFetalMovement position:0 userInfo:userInfo viewController:self];
    self.adManager = [[IMYAdFactories getAdManagerFactory] getTableViewAdManagerWithADInfo:adInfo];
    self.adManager.tableView = self.tableView;
    [self.adManager refreshData];
}
//停止播放广告视频
- (void)stopPlayAdVideo:(BOOL)isStop {
    _stopPlayAdVideo = isStop;
    if (_adManager) {
        [_adManager.adInfo unlock];
        [_adManager.adInfo appendUserInfo:@{@"force_stop_play_video":@(self.stopPlayAdVideo)}];
        [_adManager.adInfo lock];
        if (_tableView) {
            CGPoint contentOffset = _tableView.contentOffset;
            //重置 contentOffset 去通知广告视频播放/暂停
            [_tableView setContentOffset:CGPointMake(contentOffset.x, contentOffset.y+1)];
            [_tableView setContentOffset:contentOffset];
        }
    }
}

- (IMYAdSignal *)adShowViewSignal {
    if (!_adShowViewSignal) {
        _adShowViewSignal = [IMYAdSignal adSignalWithPage:IMYADPageFetalMovement userInfo:nil];
        @weakify(self);
        [_adShowViewSignal setRefreshBlock:^NSDictionary *(id data) {
            @strongify(self);
            // 设置背景
            if (self.isShouldShowLoading) {
                // 解析数据
                [self parseADSkinData:data];
                
                if (self.delegate && [self.delegate respondsToSelector:@selector(FMCChilVC:isLoading:)]) {
                    // 隐藏loading
                    [self.delegate FMCChilVC:self isLoading:NO];
                }
            }
            return nil;
        }];
    }
    return _adShowViewSignal;
}

/// 解析皮肤数据
- (void)parseADSkinData:(id)data {
    self.skinModel = [data toModel:[IMYToolsFMCountingSkinModel class]];
    self.skinAd = self.skinModel.isSuccess;
    if (self.skinAd) {
        /// 刷新UI
        imy_asyncMainBlock(^{
            [self prepareSkinUI];
            if (self.pushTipsView.superview) {
                self.tableView.contentInset = UIEdgeInsetsZero;
                self.adScrollView.contentInset = UIEdgeInsetsZero;
                [self.view bringSubviewToFront:self.pushTipsView];
                [self resetContentOffset];
            }
        });
    }
}

// 是否需要加loading
- (BOOL)isShouldShowLoading {
    IMYABTestVariables *model = [[IMYCommonConfig sharedInstance] configForKey:@"quickening_loading"];
    return [model integerForKey:@"loading_switch"] == 1;
}

@end
