//
//  SYFMCountingView.m
//  YunQi
//
//  Created by linsiyuan on 15/1/19.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import "IMYFMCountSuspensionView.h"
#import "IMYFHRIntroduceViewController.h"
#import "IMYToolsFMCountingChildVC.h"
#import "IMYToolsMainFMCountingVC.h"
#import "IMYToolsFMCountSynchornous.h"
#import "IMYToolsFMNavBar.h"
#import "IMYToolsPresentDelegate.h"
#import <sys/ucred.h>
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYFHRRecordViewController.h"
#import "IMYFHRChartView.h"
#import "IMYToolsFMCountDataManager.h"
#import "IMYFHRAllRecordVC.h"
#import <IMYBaseKit/IMYNotificationActionBox.h>

@interface IMYToolsMainFMCountingVC () <UIGestureRecognizerDelegate, IMYFakeNavigationBarTransitionDelegate, IMYPageViewControllerDelegate, IMYPageViewControllerDataSource, IMYToolsFMCountingChildVCDelegate>

@property (nonatomic, assign) BOOL isBack; //记住最后一次设置leftBarButton的值

@property (nonatomic, strong) IMYToolsFMNavBar *navBar;
@property (nonatomic, strong) IMYToolsFMCountPercentDerivenInteractive *interactiveTransition;
@property (nonatomic, strong) IMYPageViewController *pageViewController;
@property (nonatomic, strong) IMYToolsFMCountingChildVC *fmcChildVC;
/// 数胎心
@property (nonatomic, strong) IMYFHRRecordViewController *fhrChildVC;
@property (nonatomic, strong) IMYFHRIntroduceViewController *introduceVC;
/// 数胎心是否可用
@property (nonatomic, assign) BOOL fhrEnable;
@property (nonatomic, copy) NSString *fhrTips;

// loading
@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) UIButton *captionLeftBtn;

@end

@implementation IMYToolsMainFMCountingVC

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        self.navigationBarHidden = YES;
    }

    return self;
}

- (BOOL)isWhiteNavigationBar {
    return NO;
}
- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if([self isBeingDismissed] || [self isMovingFromParentViewController]) {
        // pop / dismiss
        if (self.pageViewController.currentPageIndex == 0) {
            //返回计数按钮 且不在计数中-
            if (!self.fmcChildVC.isCounting) {
                IMYURI *uri = [IMYURI uriWithPath:@"notify/permission/dialog" params:@{@"position":@(IMYNABoxShowPosition_fmCount)} info:nil];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            }
        }
    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.fhrEnable = YES;
    self.fhrTips = IMYString(@"功能即将开放，敬请期待");
    IMYABTestVariables *fhrEnableConfig = [[IMYCommonConfig sharedInstance] configForKey:@"mytxy_gnkg"];
    if (fhrEnableConfig) {
        self.fhrEnable = [fhrEnableConfig boolForKey:@"mytxy_gnkg"];
        self.fhrTips = [fhrEnableConfig stringForKey:@"mytxy_tswa"]?:IMYString(@"功能即将开放，敬请期待");
    }
    self.view.userInteractionEnabled = YES;

    self.pageViewController = [[IMYPageViewController alloc] init];
    self.pageViewController.delegate = self;
    self.pageViewController.dataSource = self;
    [self addChildViewController:self.pageViewController];
    [self.view addSubview:self.pageViewController.view];
    /// 如果y = -SCREEN_STATUSBAR_HEIGHT，上面会有留白
    self.pageViewController.view.frame = CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT);
    self.pageViewController.scrollView.bounces = NO;
    /// 禁用滚动
    self.pageViewController.scrollView.scrollEnabled = NO;
    
    if (self.isQuick) {
        if ([self.navigationController.transitioningDelegate isKindOfClass:[IMYToolsPresentDelegate class]]) {
            IMYToolsPresentDelegate *tmp = (IMYToolsPresentDelegate *)self.navigationController.transitioningDelegate;
            UIScreenEdgePanGestureRecognizer *edgePan = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(panAction:)];
            edgePan.edges = UIRectEdgeLeft;
            self.interactiveTransition = [[IMYToolsFMCountPercentDerivenInteractive alloc] initWithGestureRecognizer:edgePan];
            tmp.interactiveDelegate = self.interactiveTransition;
            [self.view addGestureRecognizer:edgePan];
        }
    }
    /// 导航栏的背景颜色由子vc决定
    [self initNavigateBar];
    // 添加loading
    [self.view addSubview:self.captionView];
    [self.view addSubview:self.captionLeftBtn];
    
    if (self.isFromTaixin) {
        [self locateToTaixinPage];
    }
}

- (void)panAction:(UIPanGestureRecognizer *)gestureRecognizer {
    if (self.pageViewController.currentPageIndex != 0) {
        return;
    }
    if (gestureRecognizer.state == UIGestureRecognizerStateBegan) {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    // 修复 iOS 10 系统「数胎动」从悬浮窗Present时, 页面整个向上偏移的问题
    // https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001059043
    if (IMYSystem.intVersion == 10) {
        [self.view setNeedsLayout];
        [self.view layoutIfNeeded];
    }
    
    BOOL needShowIntroduceVC = [self needShowIntroduceVC];
    if (needShowIntroduceVC) {
        self.fmcChildVC.showIntroduceVC = YES;
        [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:@"imy_fhc_used"];
        [self addChildViewController:self.introduceVC];
        [self.view addSubview:self.introduceVC.view];
        self.introduceVC.view.frame = CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT);
        imy_asyncMainBlock(0.1, ^{
            [self.introduceVC showWithAnimation];
        });
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_xkptc",@"action":@1} headers:nil completed:nil];
    }
}

- (BOOL)needShowIntroduceVC {
    BOOL usedFHC = [[IMYUserDefaults standardUserDefaults] boolForKey:@"imy_fhc_used"];
    return (!usedFHC && self.fhrEnable);
}
- (void)imy_topLeftButtonTouchupInside {
    NSLog(@"left");
    if (self.pageViewController.currentPageIndex == 1) {
        [self.fhrChildVC endRecordWhenDisappear:^(BOOL leavePage) {
            if (leavePage) {
                [self imy_pop:YES];
            }
        }];
    } else {
        [self imy_pop:YES];
    }
}

#pragma mark - NavigateBar
- (void)initNavigateBar {
//    UIImageView *backgroundImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, 261)];
//    [backgroundImageView imy_setImageForKey:@"ctx_img_bg_top"];
//    [self.view addSubview:backgroundImageView];
    
    self.navBar = [[IMYToolsFMNavBar alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_NAVIGATIONBAR_HEIGHT)];
    self.navBar.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.navBar];
    [self.navBar.rightBtn setTitle:IMYString(@"全部记录") forState:UIControlStateNormal];

    @weakify(self);
    self.navBar.changeTab = ^(NSInteger index) {
        @strongify(self);
        if (index == 0 && self.pageViewController.currentPageIndex != index) {
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_qhstd",@"action":@2} headers:nil completed:nil];
            [self.pageViewController setViewControllerAtIndex:0 animated:YES];
            return YES;
        } else if (index == 1 && self.pageViewController.currentPageIndex != index) {
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_qhctx",@"action":@2} headers:nil completed:nil];
            if (self.fmcChildVC.isCounting) {
                @weakify(self);
                [IMYActionMessageBox showBoxWithTitle:nil
                                              message:IMYString(@"正在数胎动中，确定要结束吗？结束后本次记录不保存")
                                                style:IMYMessageBoxStyleFlat
                                    isShowCloseButton:NO
                                        textAlignment:NSTextAlignmentCenter
                                    cancelButtonTitle:IMYString(@"继续计时")
                                     otherButtonTitle:IMYString(@"结束")
                                               action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                    [messageBox dismiss];
                    @strongify(self);
                    if (sender == messageBox.rightButton) {
                        [self.fmcChildVC cancelRecord];
                        [self.pageViewController setViewControllerAtIndex:1 animated:YES];
                    }
                }];
                
                return NO;
            } else {
                [self.pageViewController setViewControllerAtIndex:1 animated:YES];
                return YES;
            }
        }
        return YES;
    };
    
    [self.navBar setShouldChangeTabBlock:^BOOL(NSInteger index) {
        @strongify(self);
        [IMYGAEventHelper postWithPath:@"event" params:@{@"event":index == 0 ? @"std_qhstd" : @"std_qhctx",@"action":@2} headers:nil completed:nil];

        if (index == 0 && self.pageViewController.currentPageIndex != index) {
            /// 胎心仪正在测量，不可以切换
            [self.fhrChildVC endRecordWhenDisappear:^(BOOL leavePage) {
                if (leavePage) {
                    [self.navBar changeToTabWithIndex:0];
                    self.navBar.changeTab(0);
                }
            }];
            return NO;
        } else if (index == 1 && !self.fhrEnable) {
            [UIView imy_showTextHUD:self.fhrTips];
            return NO;
        } else if (index == 1 && self.pageViewController.currentPageIndex != index && [[IMYToolsFMCountDataManager sharedManager] getCountingRecord]) {
            /// 数胎动正在测量，不可切换
            @weakify(self);
            [IMYActionMessageBox showBoxWithTitle:nil
                                          message:IMYString(@"正在数胎动中，确定要结束吗？结束后本次记录不保存")
                                            style:IMYMessageBoxStyleFlat
                                isShowCloseButton:NO
                                    textAlignment:NSTextAlignmentCenter
                                cancelButtonTitle:IMYString(@"继续计时")
                                 otherButtonTitle:IMYString(@"结束")
                                           action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                [messageBox dismiss];
                @strongify(self);
                if (sender == messageBox.rightButton) {
                    [self.fmcChildVC cancelRecord];
                    [self.navBar changeToTabWithIndex:1];
                    self.navBar.changeTab(1);
                }
            }];
            return NO;
        } else {
            return YES;
        }

    }];
    self.navBar.tapLeftBack = ^{
        @strongify(self);
        self.interactiveTransition = nil;
        [self imy_topLeftButtonTouchupInside];
    };

    self.navBar.tapRightBack = ^{
        @strongify(self);
        [self onTopRightBtnTaped:nil];
    };

    [self reSetLeftBarButton:!self.isQuick];
}

- (void)reSetLeftBarButton:(BOOL)isBack {
    if (isBack) {
        if (self.isQuick || self.isRealQuick) {
            [self.navBar.leftBtn imy_setTitle:IMYString(@"关闭") state:UIControlStateNormal];
            [self.navBar.leftBtn imy_setImage:nil state:UIControlStateNormal];

            if ([self.navigationController.transitioningDelegate isKindOfClass:[IMYToolsPresentDelegate class]]) {
                IMYToolsPresentDelegate *tmp = (IMYToolsPresentDelegate *)self.navigationController.transitioningDelegate;
                tmp.isNormal = YES;
            }

        } else {
            [self.navBar.leftBtn imy_setTitle:nil state:UIControlStateNormal];
            [self.navBar.leftBtn imy_setImage:@"tools_nav_btn_back" state:UIControlStateNormal];
        }

    } else {
        [self.navBar.leftBtn imy_setTitle:IMYString(@"收起") state:UIControlStateNormal];
        [self.navBar.leftBtn imy_setImage:nil state:UIControlStateNormal];

        if ([self.navigationController.transitioningDelegate isKindOfClass:[IMYToolsPresentDelegate class]]) {
            IMYToolsPresentDelegate *tmp = (IMYToolsPresentDelegate *)self.navigationController.transitioningDelegate;
            tmp.isNormal = NO;
        }
    }

    self.isBack = isBack;
}

#pragma mark - page view controller

- (nullable UIViewController *)pageViewController:(IMYPageViewController *)pageViewController controllerAtIndex:(NSUInteger)index {
    if (index == 0) {
        return self.fmcChildVC;
    } else {
        return self.fhrChildVC;
    }
}

- (NSUInteger)numberOfControllersInPageViewController:(IMYPageViewController *)pageViewController {
    return 2;
}

- (void)pageViewController:(IMYPageViewController *)pageViewController willTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    [self.navBar changeToTabWithIndex:toIndex];
}


#pragma mark - child vc delegate

- (void)FMCChilVC:(IMYToolsFMCountingChildVC *)childVC isCounting:(BOOL)couting {
    if (couting) {
        self.pageViewController.scrollView.scrollEnabled = NO;
        [self reSetLeftBarButton:NO];
    } else {
        self.pageViewController.scrollView.scrollEnabled = NO;
        self.isQuick = NO;
        self.isRealQuick = NO;
        [self reSetLeftBarButton:YES];
    }
}

- (void)FMCChilVC:(IMYToolsFMCountingChildVC *)childVC isLoading:(BOOL)isLoading {
    __block BOOL isLoading_block = isLoading;
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        if (isLoading_block && self.isRealQuick == NO && self.pageViewController.currentPageIndex == 0) {
//            self.captionView.state = IMYCaptionViewStateLoading;
//            self.captionLeftBtn.hidden = NO;
//            [self.view bringSubviewToFront:self.captionView];
//            [self.view bringSubviewToFront:self.captionLeftBtn];
        } else {
            self.captionView.state = IMYCaptionViewStateHidden;
            self.captionLeftBtn.hidden = YES;
        }
    });
    
}

#pragma - mark 所有记录跳转
- (void)onTopRightBtnTaped:(id)sender {
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"std_cltxy_qbjl",@"action":@2} headers:nil completed:nil];
    IMYFHRAllRecordVC *vc = [[IMYFHRAllRecordVC alloc] init];
    vc.typeInteger = self.pageViewController.currentPageIndex;
    if (self.pageViewController.currentPageIndex == 0) {
        [vc.fmaAllVC setValue:self.fmcChildVC.synchornous forKey:@"syschornous"];
        [self.navigationController pushViewController:vc animated:YES];
        [IMYFMCountSuspensionView sharedInstance].forceDismiss = YES;
    } else {
        [self.fhrChildVC endRecordWhenDisappear:^(BOOL leavePage) {
            if (leavePage) {
                [self.navigationController pushViewController:vc animated:YES];
            }
        }];
    }
}


#pragma - mark fake navigation bar

- (BOOL)shouldFakeNavigationBarTransition {
    return YES;
}

//MARK: 定位到测胎心
- (void)locateToTaixinPage {
    if (self.pageViewController.currentPageIndex != 1) {
        //!< 用户使用数胎动工具的同时，从金刚区或全部工具页进入数胎心小工具时不触发结束提醒。
        [self.pageViewController setViewControllerAtIndex:1 animated:NO];
    }
}

#pragma mark - 右滑退出

- (BOOL)fullPopGestureRecognizerShouldBegin:(UIPanGestureRecognizer *)gestureRecognizer {
    if (self.pageViewController.currentPageIndex == 1) {
        CGPoint location = [gestureRecognizer locationInView:self.view];
        CGRect chatViewFrame = self.fhrChildVC.chartView.frame;
        chatViewFrame.origin.y = chatViewFrame.origin.y + self.navBar.imy_height;
        /// 加30高度，避免跟进度条冲突
        chatViewFrame.size.height = chatViewFrame.size.height + 30;

        if (CGRectContainsPoint(chatViewFrame, location)) {
            return NO;
        }
    }
    if (self.fhrChildVC.recordStage == IMYFHRRecordStageRecording) {
        return NO;
    }
    return YES;
}

#pragma mark - getter

- (IMYToolsFMCountingChildVC *)fmcChildVC {
    if (!_fmcChildVC) {
        _fmcChildVC = [[IMYToolsFMCountingChildVC alloc] init];
        _fmcChildVC.isRealQuick = self.isRealQuick;
        _fmcChildVC.delegate = self;
        [_fmcChildVC stopPlayAdVideo:[self needShowIntroduceVC]];
    }
    return _fmcChildVC;
}


- (IMYFHRRecordViewController *)fhrChildVC {
    if (!_fhrChildVC) {
        _fhrChildVC = [IMYFHRRecordViewController new];
//        @weakify(self);
//        [RACObserve(_fhrChildVC, recordStage) subscribeNext:^(id  _Nullable x) {
//            @strongify(self);
//            /// 在记录数据的时候禁止通过手势拖动回到数胎动；
//            if ([x intValue] == IMYFHRRecordStageRecording) {
//                self.pageViewController.scrollView.scrollEnabled = NO;
//            } else {
//                self.pageViewController.scrollView.scrollEnabled = YES;
//            }
//        }];
    }
    return _fhrChildVC;
}

- (IMYFHRIntroduceViewController *)introduceVC {
    if (!_introduceVC) {
        _introduceVC = [[IMYFHRIntroduceViewController alloc] init];
        _introduceVC.pageParams = self.fromURI.params;
        _introduceVC.index = self.isFromTaixin ? 1 : 0;
        @weakify(self);
        [_introduceVC setGoPageHandler:^(NSInteger index) {
            @strongify(self);
            self.fmcChildVC.showIntroduceVC = NO;
            if (index == 0 && self.pageViewController.currentPageIndex != index) {
                [self.pageViewController setViewControllerAtIndex:0 animated:YES];
            } else if (index == 1 && self.pageViewController.currentPageIndex != index) {
                [self.pageViewController setViewControllerAtIndex:1 animated:YES];
            }
            [_fmcChildVC stopPlayAdVideo:NO];
        }];
    }
    return _introduceVC;
}

- (IMYCaptionView *)captionView {
    if (!_captionView) {
        _captionView = [[IMYCaptionView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT)];
//        _captionView = [IMYCaptionView addToView:self.view show:NO];
        _captionView.state = IMYCaptionViewStateHidden;
    }
    return _captionView;
}

- (UIButton *)captionLeftBtn {
    if (!_captionLeftBtn) {
        _captionLeftBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _captionLeftBtn.imageEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 50);
        _captionLeftBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 40);
        _captionLeftBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        [_captionLeftBtn imy_setImage:@"nav_btn_back_black" state:UIControlStateNormal];
        [self.view addSubview:_captionLeftBtn];
        [_captionLeftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.navBar);
            make.left.mas_equalTo(15);
            make.width.mas_equalTo(80);
        }];
        _captionLeftBtn.hidden = YES;
        @weakify(self);
        [_captionLeftBtn bk_whenTapped:^{
            @strongify(self);
            self.interactiveTransition = nil;
            [self imy_topLeftButtonTouchupInside];
        }];
    }
    return _captionLeftBtn;
}

@end
