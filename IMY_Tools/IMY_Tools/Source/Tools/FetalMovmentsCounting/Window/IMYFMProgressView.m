//
//  IMYFMProgressView.m
//  AFNetworking
//
//  Created by meetyou on 2025/8/8.
//

#import "IMYFMProgressView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYFMProgressView()

@property (nonatomic, strong) NSArray<UIColor*> *bgColors;

@property (nonatomic, strong) NSArray<UIColor*> *progressColors;

@property (nonatomic, assign) CGFloat lineWidth;

@property (nonatomic, assign) CGFloat radius;

@property (nonatomic, assign) CGPoint center;

@property (nonatomic, strong) NSArray *bgLocations;

@property (nonatomic, strong) NSArray *progressLocations;

@property (nonatomic, strong) CAGradientLayer *bgGradientLayer;

@property (nonatomic, strong) CAShapeLayer *progressLayer;

@property (nonatomic, strong) CAGradientLayer *progressGradientLayer;

@property (nonatomic, strong) CAShapeLayer *endRLayer;

@end

@implementation IMYFMProgressView


- (instancetype)init:(CGRect) frame
           lineWidth:(CGFloat) lineWidth
            bgColors:(NSArray<UIColor*>*) bgColors
      progressColors:(NSArray<UIColor*>*) progressColors
   progressLocations:(NSArray*) progressLocations
         bgLocations:(NSArray*) bgLocations {
    if (self = [super initWithFrame:frame]) {
        self.lineWidth = lineWidth;
        self.bgColors = bgColors;
        self.progressColors = progressColors;
        self.progressLocations = progressLocations;
        self.bgLocations = bgLocations;
        self.progress = 1;
        [self.layer addSublayer:self.endRLayer];
    }
    return self;
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];
    
    [self.bgGradientLayer removeFromSuperlayer];
    [self.progressGradientLayer removeFromSuperlayer];
    
    self.bgGradientLayer.frame = self.bounds;
    self.bgGradientLayer.frame = self.bounds;
    self.bgGradientLayer.colors = self.bgCGColors;
    self.bgGradientLayer.locations = self.bgLocations;
    self.bgGradientLayer.startPoint = CGPointMake(0, 0);
    self.bgGradientLayer.endPoint = CGPointMake(0,1);
    
    [self.layer addSublayer:self.bgGradientLayer];
    
    self.center = CGPointMake(rect.size.width/2,rect.size.height/2);
    self.radius = (rect.size.width - self.lineWidth)/2;
    
    UIBezierPath * bezierPath = [UIBezierPath bezierPathWithArcCenter:self.center radius:self.radius startAngle:self.startAngle endAngle:self.endAngle clockwise:YES];
    
    CAShapeLayer * bgLayer = [CAShapeLayer new];
    bgLayer.frame = self.bounds;
    bgLayer.lineWidth = self.lineWidth;
    bgLayer.fillColor = [[UIColor clearColor] CGColor];
    bgLayer.strokeColor = [[UIColor redColor] CGColor];
    bgLayer.opacity = 0.2;
    
    bgLayer.lineCap = kCALineCapRound; //设置画笔
    bgLayer.path = bezierPath.CGPath; //将曲线添加到layer层
    //修改渐变layer层的遮罩，关键代码
    self.bgGradientLayer.mask = bgLayer;
    
    self.progressGradientLayer.frame = self.bounds;
    self.progressGradientLayer.colors = self.progressCGColors;
    self.progressGradientLayer.locations = self.progressLocations;
    self.progressGradientLayer.startPoint = CGPointMake(0,0);
    self.progressGradientLayer.endPoint = CGPointMake(0,1);
    
    [self.layer addSublayer:self.progressGradientLayer];
    
    self.progressLayer.frame = self.bounds;
    self.progressLayer.lineWidth = self.lineWidth;
    self.progressLayer.fillColor = [[UIColor clearColor] CGColor];
    self.progressLayer.strokeColor = [[UIColor redColor] CGColor];
    
    self.progressLayer.lineCap = kCALineCapRound; //设置画笔
    self.progressLayer.path = bezierPath.CGPath;
    
    //修改渐变layer层的遮罩，关键代码
    self.progressGradientLayer.mask = self.progressLayer;
}

// 计算指定进度对应的尾部坐标
- (CGRect)positionForProgress:(CGFloat)progress {
    // 限制进度范围0~1
    progress = MAX(0, MIN(1, progress));
    // 当前进度对应的角度 = 起始角度 + 进度比例 * 总角度（2π）
    CGFloat currentAngle = self.startAngle + progress * 2*M_PI;
    // 计算坐标（三角函数：x=圆心x + 半径*cos(角度)，y=圆心y + 半径*sin(角度)）
    CGFloat x = self.center.x + self.radius * cos(currentAngle);
    CGFloat y = self.center.y + self.radius * sin(currentAngle);
    return CGRectMake(x-self.endR, y-self.endR, self.endR*2, self.endR*2);
}

#pragma mark - get/set

- (CGFloat)startAngle {
    return -M_PI_2;
}

- (CGFloat)endAngle {
    return 1.5*M_PI;
}

- (void)setProgress:(CGFloat)progress {
    
    if (progress >= 1 && _progress >= 1) {
        return;
    }
    
    if (progress <= 0 && _progress <= 0) {
        return;
    }
    
    CABasicAnimation * animation = [CABasicAnimation new];
    animation.keyPath = @"strokeEnd";
    animation.duration = 0.1;
    animation.fromValue = @(_progress);
    animation.toValue = @(progress);
    [self.progressLayer addAnimation:animation forKey:nil];
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    // 在这里进行属性更改，例如设置 progressLayer 的 strokeEnd
    self.progressLayer.strokeEnd = progress;
    
    self.endRLayer.frame = [self positionForProgress:progress];
    [CATransaction commit];
    
    _progress = progress;
}

- (void)setEndR:(CGFloat)endR {
    _endR = endR;
    self.endRLayer.frame = CGRectMake(self.endRLayer.frame.origin.x, self.endRLayer.frame.origin.y, self.endR*2, self.endR*2);
    self.endRLayer.cornerRadius = self.endR;
}

- (void)setEndRColor:(UIColor *)endRColor {
    self.endRLayer.backgroundColor = [endRColor CGColor];
    _endRColor = endRColor;
}

- (NSArray *)progressCGColors {
    NSMutableArray * array = [NSMutableArray new];
    for (UIColor * color in self.progressColors) {
        [array addObject:((__bridge id)color.CGColor)];
    }
    return [array copy];
}

- (NSArray*)bgCGColors {
    NSMutableArray * array = [NSMutableArray new];
    for (UIColor * color in self.bgColors) {
        [array addObject:((__bridge id)color.CGColor)];
    }
    return [array copy];
}

- (CAShapeLayer *)endRLayer {
    if (!_endRLayer) {
        _endRLayer = [CAShapeLayer new];
        _endRLayer.frame = CGRectMake(0, 0, 3, 3);
        _endRLayer.cornerRadius = 1.5;
        CGColorRef bgColor = [[UIColor imy_colorWithHexString:@"#FFE1EB"] CGColor];
        _endRLayer.backgroundColor = bgColor;
    }
    return _endRLayer;
}

- (CAGradientLayer *)bgGradientLayer {
    if (!_bgGradientLayer) {
        _bgGradientLayer = [CAGradientLayer new];
    }
    return _bgGradientLayer;
}

- (CAShapeLayer *)progressLayer {
    if (!_progressLayer) {
        _progressLayer = [CAShapeLayer new];
        _progressLayer.strokeStart = 0.0;
        _progressLayer.strokeEnd = 0.0;
    }
    return _progressLayer;
}

- (CAGradientLayer *)progressGradientLayer {
    if (!_progressGradientLayer) {
        _progressGradientLayer = [CAGradientLayer new];
    }
    return _progressGradientLayer;
}

@end
