//
//  IMYFMProgressView.h
//  AFNetworking
//
//  Created by meetyou on 2025/8/8.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYFMProgressView : UIView

// 进度
@property (nonatomic, assign) CGFloat progress;

// 尾部的圆圈大小
@property (nonatomic, assign) CGFloat endR;
// 尾部圆圈的颜色
@property (nonatomic, strong) UIColor *endRColor;

- (instancetype)init:(CGRect) frame
           lineWidth:(CGFloat) lineWidth
            bgColors:(NSArray<UIColor*>*) bgColors
      progressColors:(NSArray<UIColor*>*) progressColors
   progressLocations:(NSArray*) progressLocations
         bgLocations:(NSArray*) bgLocations;

@end

NS_ASSUME_NONNULL_END
