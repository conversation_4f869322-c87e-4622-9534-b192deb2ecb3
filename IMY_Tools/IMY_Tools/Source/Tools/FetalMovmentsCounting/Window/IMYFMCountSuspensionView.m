//
//  IMYFMCountSuspensionView.m
//  AFNetworking
//
//  Created by meetyou on 2025/8/8.
//

#import "IMYFMCountSuspensionView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <AudioToolbox/AudioToolbox.h>
#import "IMYFMProgressView.h"
#import "IMYToolsFMCountDataManager.h"
#import "IMYToolsFMCountSynchornous.h"

@import IMYSwift;

FOUNDATION_EXPORT NSString *const FM_COUNT_TAP_FROM_WINDOW = @"FM_COUNT_TAP_FROM_WINDOW";

FOUNDATION_EXPORT NSString *const FM_COUNT_WINDOW_REFRESH = @"FM_COUNT_WINDOW_REFRESH";

FOUNDATION_EXPORT NSString *const FM_COUNT_WINDOW_SHOW = @"FM_COUNT_WINDOW_SHOW";

#define kIsNotFirstUseSuspensionView @"kIsNotFirstUseSuspensionView"
#define kIsExitAbNormal @"kIsExitAbNormal"

const int ValidCountIntervalInSecond = 5 * 60;

#define K_FMCOUNT_SIZE 52.0

@interface IMYFMCountSuspensionView()<IMYTimerRuningProtocol>

// 载体
@property (nonatomic, strong) UIView * contentView;

@property (nonatomic, strong) UIImageView *bgImageView;

/// 进度条
@property (nonatomic, strong) IMYFMProgressView * progressView;

@property (nonatomic, strong) UIView *progressContentView;

// 胎动次数
@property (nonatomic, strong) UILabel * countLabel;

/// 动画数值label
@property (nonatomic, strong) UILabel * animationLabel;

// 提示
@property (nonatomic, strong) UILabel * tipsLabel;

// 结果
@property (nonatomic, strong) UILabel * resultLabel;

// 震动反馈
@property (nonatomic, strong) UISelectionFeedbackGenerator * feedBack;

@property (nonatomic, strong) IMYTipsLabelView *tipsView;

@property (nonatomic, strong) IMYToolsFMCountModel *currentModel;

@property (nonatomic, strong) IMYToolsFMCountSynchornous *synchornous; //数据同步器

//user 却换账号，身份
@property (nonatomic, copy) NSString *userID;
@property (nonatomic, assign) IMYVKUserMode userMode;
@property (nonatomic, assign) BOOL isTempUser; //是否为临时用户

@property (nonatomic, assign) BOOL isAbNormalExit;  //是否为异常退出
@property (nonatomic, assign) BOOL isNotFirstUse;   //是否为第一使用悬浮view

/// 是否正在计数
@property (nonatomic, assign) BOOL isCounting;

@end

@implementation IMYFMCountSuspensionView

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYFMCountSuspensionView *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[IMYFMCountSuspensionView alloc] initWithFrame:CGRectMake(0, 0, K_FMCOUNT_SIZE, K_FMCOUNT_SIZE)];
    });
    return instance;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setup];
        [self addUserChangeObserve];
        [self setAction];
    }
    return self;
}

- (void)setup {
    [self imy_setBackgroundColorForKey:kCK_Clear_A];
    
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.bgImageView];
    [self.contentView addSubview:self.progressContentView];
    [self.contentView addSubview:self.countLabel];
    [self.contentView addSubview:self.tipsLabel];
    [self.contentView addSubview:self.resultLabel];
    
    [self.countLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(8);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(24);
    }];
    
    [self.tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(28);
        make.centerX.equalTo(self.contentView);
    }];
    
    [self.resultLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.contentView);
    }];
    
    [self imy_timerRuning];
    
    [self setNeedsLayout];
    [self layoutIfNeeded];
}

- (void)setAction {
    @weakify(self);
    self.beginDragBlock = ^{
        @strongify(self);
       
    };
    
    self.clickBlock = ^{
        @strongify(self);
        if (self.isCounting) {
            [self clickCount];
        } else {
            IMYURI *uri = [IMYURI uriWithURIString:@"tools/fmCount"];
            [[IMYURIManager shareURIManager] runActionWithURI:uri];
        }
    };
}

- (void)reflash {
    self.userID = [IMYPublicAppHelper shareAppHelper].userid;
    self.userMode = [IMYPublicAppHelper shareAppHelper].userMode;
    self.isTempUser = ![IMYPublicAppHelper shareAppHelper].hasLogin;

    self.isAbNormalExit = [[[IMYUserDefaults standardUserDefaults] objectForKey:kIsExitAbNormal] boolValue];
    self.isNotFirstUse = [[[IMYUserDefaults standardUserDefaults] objectForKey:kIsNotFirstUseSuspensionView] boolValue];
}

#pragma - mark 切换账号，退出账号，切换身份--> 销毁自身
- (void)addUserChangeObserve {
    @weakify(self);
    [[[IMYPublicAppHelper shareAppHelper].useridChangedSignal skip:1] subscribeNext:^(NSString *userId) {
        @strongify(self);
        if (![userId isEqualToString:self.userID]) {
            if (!self.isTempUser && [IMYPublicAppHelper shareAppHelper].hasLogin) {
                //切换账号
                [self destory];
            } else {
                [NSObject imy_asyncMainBlock:^{
                        NSArray *todays = [[IMYToolsFMCountDataManager sharedManager] getTodayRecords];
                        if (!todays || !todays.count) {
                            [self destory];
                        } else {
                            IMYToolsFMCountModel *model = todays.lastObject;
                            if (model && model.isEned) {
                                [self destory];
                            }
                        }
                    } afterSecond:0.5];
            }
        }
    }];

    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"friendVersionLoginSuccess" object:nil] subscribeNext:^(id x) {
        @strongify(self);
        if ([self is_friend_version]) {
            IMYToolsFMCountModel *record = [[IMYToolsFMCountDataManager sharedManager] getTodayRecords].lastObject;
            if (record && !record.isEned) {
                [self dismiss];
                [record deleteToDB];
            }
        }
    }];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:FM_COUNT_WINDOW_REFRESH object:nil] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self configValidModelToCurrentModel];
        [self reflash];
        self.count = self.currentModel.totalFMTimes;
    }];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:FM_COUNT_WINDOW_SHOW object:nil] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self show];
    }];
    
}

- (BOOL)is_friend_version {

    if ([IMYPublicAppHelper isYunqi]) {
        IMYPublicAppHelper *helper = [IMYPublicAppHelper shareAppHelper];
        NSNumber *n = [helper valueForKey:@"is_friend_version"];
        return [n boolValue];
    } else {
        return NO;
    }
}

/// 异常退出
- (void)appExit {
    
}

- (void)showTips {
    NSString * tipsKey = @"IMYFMCountSuspensionViewTips";
    BOOL showed = [[IMYUserDefaults standardUserDefaults] boolForKey:tipsKey];
    if (showed) {
        return;
    }
    
    [[IMYUserDefaults standardUserDefaults] setObject:@(YES) forKey:tipsKey];
    [[IMYUserDefaults standardUserDefaults] synchronize];
    
    IMYTipsLabelView *tipsView = [IMYTipsLabelView showOn:self
                                                direction: IMYBubbleViewDirectionLeft
                                        needJumpAnimation:NO
                                                      tips:@"动一下，点一下"
                                               enableBgBtn:YES
                                             contentHeight:45
                                         leftAndRightSpace:12
                                               onViewSpace:8
                                                      font:[UIFont imy_regularWith:15]
                                                 textColor:[UIColor imy_colorForKey:kCK_White_A]
                                                   bgColor:[[UIColor blackColor] colorWithAlphaComponent:0.6]];
    
    [tipsView.tipsLabel imy_setTextColorForKey:kCK_White_A];
    [tipsView.contentView imy_addThemeChangedBlock:^(UIView * weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.backgroundColor = [UIColor imy_colorWithHexString:@"#FF4D88"];
        } else {
            weakObject.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        }
    }];
    [tipsView.arrowLayer imy_addThemeChangedBlock:^(CAShapeLayer *weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.fillColor = [UIColor imy_colorWithHexString:@"#FF4D88"].CGColor;
        } else {
            weakObject.fillColor = [[UIColor blackColor] colorWithAlphaComponent:0.6].CGColor;
        }
    }];
    @weakify(self)
    tipsView.dismissBlock = ^{
        @strongify(self)
        self.tipsView = nil;
    };
    self.tipsView = tipsView;
}

- (void)show {
    if (self.forceDismiss) {
        return;
    }
    
    [self configValidModelToCurrentModel];
    
    self.resultLabel.hidden = YES;
    self.progressContentView.hidden = NO;
    self.countLabel.hidden = NO;
    self.tipsLabel.hidden = NO;
    self.animationLabel.hidden = NO;
    
    self.count = self.currentModel.totalFMTimes;
    [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
    [self showWithAnimation:YES];
    
    imy_asyncMainBlock(0.3, ^{
        [self showTips];
    });
}

- (void)dismiss {
    if (!self.superview) {
        return;
    }
    [super dismissWithAnimation:NO];
    [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
}

- (void)destory {
    [self removeFromSuperview];
    [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
}

- (void)finishCounting {
    // 移除计时器
    [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    
    [[IMYToolsFMCountDataManager sharedManager].giManager cancel];
    self.currentModel.status = IMYToolsFMCountModelStatusDone;
    [[IMYToolsFMCountDataManager sharedManager] updateRecord:self.currentModel];
    
    [self synchornousDatas];
    self.currentModel = nil;
    
    self.resultLabel.hidden = NO;
    self.progressContentView.hidden = YES;
    self.countLabel.hidden = YES;
    self.tipsLabel.hidden = YES;
    self.animationLabel.hidden = YES;
}

- (void)clickCount {
    [self addAnimation];
    
    // 点击次数
    self.currentModel.totalFMTimes++;
    
    // 大于五分钟或首次点击
    if (self.currentModel.lastFMTime.timeIntervalSinceNow < -ValidCountIntervalInSecond || self.currentModel.validFMTimes == 0) {
        self.currentModel.validFMTimes++;
        self.currentModel.lastFMTime = [NSDate date];
    }
    
    self.count = self.currentModel.totalFMTimes;
    
    /// 更新本地数据
    [[IMYToolsFMCountDataManager sharedManager] updateRecord:self.currentModel];
    
    // 发送点击通知
    [[NSNotificationCenter defaultCenter] postNotificationName:FM_COUNT_TAP_FROM_WINDOW object:nil];
}

- (void)addAnimation {
    [self.feedBack prepare];
    [self.feedBack selectionChanged]; // 触发选择反馈
    
    self.animationLabel.alpha = 1;
    [self.contentView addSubview:self.animationLabel];
    CGFloat x = self.countLabel.frame.origin.x + self.countLabel.frame.size.width;
    self.animationLabel.frame = CGRectMake(x, 12, 11, 11);
    
    [UIView animateWithDuration:0.6 animations:^{
        self.animationLabel.alpha = 0;
        self.animationLabel.frame = CGRectMake(x, 0, 11, 11);
    }];
    
    // 动画步骤：原大小 -> 放大到1.05倍 -> 恢复原大小
    [UIView animateWithDuration:0.3 animations:^{
        // 第一阶段：放大
        self.contentView.transform = CGAffineTransformMakeScale(1.05, 1.05);
    } completion:^(BOOL finished) {
        // 第二阶段：恢复
        [UIView animateWithDuration:0.3 animations:^{
            self.contentView.transform = CGAffineTransformIdentity; // 重置为原大小
        }];
    }];
    
}

- (void)synchornousDatas {
    IMYToolsFMCountModel *tempModel = self.currentModel;
    @weakify(self,tempModel);
    //1.同步数据
    [IMYToolsFMCountSynchornous postFetalMovementSyncWithModel:tempModel position:4 completeBlock:^(BOOL success, NSError *error) {
        @strongify(self,tempModel);
        if (success) {
            //同步到本地数据
            [[IMYToolsFMCountDataManager sharedManager] setSynYes:@[tempModel]];
            [self configValidModelToCurrentModel];
            
            imy_asyncMainBlock(1, ^{
                //2、同步完后，拉取所有数据
                @weakify(self);
                [self.synchornous synchornousNetToLocal:^(BOOL success, NSError *error) {
                    @strongify(self);
                    if (success) {
                        [self configValidModelToCurrentModel];
                    }else if (error && [error isKindOfClass:[NSError class]]){
                        [IMYErrorTraces postWithType:IMYErrorTraceTypeLocalDataFails
                                            pageName:@"IMYToolsMainFMCountingVC"
                                            category:IMYErrorTraceCategoryYunyu
                                             message:@"数胎动 同步本地数据 error"
                                              detail:@{
                            @"code":@(error.code),
                            @"reason" : error.localizedFailureReason ?: error.localizedDescription
                        }];
                    }
                }];

            });
        }else if (error && [error isKindOfClass:[NSError class]]){
            [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                pageName:@"IMYToolsMainFMCountingVC"
                                category:IMYErrorTraceCategoryYunyu
                                 message:@"数胎动 同步网络 error"
                                  detail:@{
                @"code":@(error.code),
                @"reason" : error.localizedFailureReason ?: error.localizedDescription
            }];
        }
    }];
}

- (void)configValidModelToCurrentModel {
    self.currentModel = [[IMYToolsFMCountDataManager sharedManager] getCountingRecord];
}

#pragma mark - IMYTimerRuningProtocol
- (void)imy_timerRuning {
    if (!self.currentModel) {
        [self configValidModelToCurrentModel];
    }
    
    NSInteger left = 3600 + self.currentModel.beginTime.timeIntervalSinceNow;
    if (left < 0) {
        [self finishCounting];
    } else {
        self.progress = left/3600.0f;
    }
}

#pragma mark - set

- (void)setCount:(NSInteger)count {
    _count = count;
    self.countLabel.text = [NSString stringWithFormat:@"%ld", count];
}

- (void)setProgress:(CGFloat)progress {
    _progress = progress;
    self.progressView.progress = progress;
}

#pragma mark - get

- (IMYToolsFMCountModel *)currentModel {
    if (!_currentModel) {
        [self configValidModelToCurrentModel];
    }
    return _currentModel;
}

- (BOOL)isCounting {
    IMYToolsFMCountModel *currentModel = [[IMYToolsFMCountDataManager sharedManager] getCountingRecord];
    if (currentModel && ![currentModel isEned]) {
        return YES;
    } else {
        return NO;
    }
}

- (UISelectionFeedbackGenerator *)feedBack {
    if (!_feedBack) {
        _feedBack = [[UISelectionFeedbackGenerator alloc] init];
    }
    return _feedBack;
}

- (UIView *)progressContentView {
    if (!_progressContentView) {
        CGRect rect = CGRectMake(1.5, 1.5, K_FMCOUNT_SIZE - 1.5*2, K_FMCOUNT_SIZE - 1.5*2);
        _progressContentView = [[UIView alloc] initWithFrame:rect];
        [_progressContentView addSubview:self.progressView];
    }
    return _progressContentView;
}

- (IMYFMProgressView *)progressView {
    if (!_progressView) {
        CGRect rect = CGRectMake(0, 0, K_FMCOUNT_SIZE - 1.5*2, K_FMCOUNT_SIZE - 1.5*2);
        
        NSArray *progressColors = @[[[UIColor imy_colorWithHexString:@"#FFE1EB"] colorWithAlphaComponent:0.6],
                                    [[UIColor imy_colorWithHexString:@"#FFE1EB"] colorWithAlphaComponent:0.6]];
        NSArray *bgColors = @[[UIColor clearColor],
                              [UIColor clearColor]];
        _progressView = [[IMYFMProgressView alloc] init:rect lineWidth:1.5 bgColors:bgColors progressColors:progressColors progressLocations:@[@0,@1] bgLocations:@[@0,@1]];
        _progressView.endR = 1.5;
        _progressView.endRColor = [UIColor imy_colorWithHexString:@"#FFE1EB"];
        _progressView.center = CGPointMake((K_FMCOUNT_SIZE - 3)/2, (K_FMCOUNT_SIZE - 3)/2);
    }
    return _progressView;
}

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, K_FMCOUNT_SIZE, K_FMCOUNT_SIZE)];
        [_bgImageView imy_setImage:@"fm_count_window_bg"];
    }
    return _bgImageView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, K_FMCOUNT_SIZE, K_FMCOUNT_SIZE)];
    }
    return _contentView;
}

- (UILabel *)animationLabel {
    if (!_animationLabel) {
        _animationLabel = [UILabel new];
        [_animationLabel imy_setTextColor:[UIColor imy_colorWithHexString:@"#FFFFFF"]];
        _animationLabel.font = [UIFont imy_regularWith:8];
        _animationLabel.text = @"+1";
    }
    return _animationLabel;
}

- (UILabel *)countLabel {
    if (!_countLabel) {
        _countLabel = [UILabel new];
        _countLabel.font = [UIFont imy_regularWith:17];
        [_countLabel imy_setTextColor:[UIColor imy_colorWithHexString:@"#FFFFFF"]];
    }
    return _countLabel;
}

- (UILabel *)tipsLabel {
    if (!_tipsLabel) {
        _tipsLabel = [UILabel new];
        _tipsLabel.font = [UIFont imy_regularWith:10];
        [_tipsLabel imy_setTextColor:[UIColor imy_colorWithHexString:@"#FFFFFF"]];
        _tipsLabel.text = @"胎动 +1";
    }
    return _tipsLabel;
}

- (UILabel *)resultLabel {
    if (!_resultLabel) {
        _resultLabel = [UILabel new];
        [_resultLabel imy_setTextColor:[UIColor imy_colorWithHexString:@"#FFFFFF"]];
        _resultLabel.font = [UIFont imy_regularWith:11];
        _resultLabel.text = @"查看\n结果";
        _resultLabel.numberOfLines = 0;
        _resultLabel.hidden = YES;
    }
    return _resultLabel;
}

@end
