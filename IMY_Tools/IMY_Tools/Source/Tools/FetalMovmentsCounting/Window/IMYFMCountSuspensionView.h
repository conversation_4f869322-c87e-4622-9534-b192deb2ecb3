//
//  IMYFMCountSuspensionView.h
//  AFNetworking
//
//  Created by meetyou on 2025/8/8.
//

#import <UIKit/UIKit.h>
#import "IMYToolsTimeSuspensionView.h"

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSString *const FM_COUNT_TAP_FROM_WINDOW;

/**悬浮球计数刷新*/
FOUNDATION_EXPORT NSString *const FM_COUNT_WINDOW_REFRESH;

/**悬浮球展示*/
FOUNDATION_EXPORT NSString *const FM_COUNT_WINDOW_SHOW;

@interface IMYFMCountSuspensionView : IMYToolsTimeSuspensionView

@property (nonatomic, assign) NSInteger count;

@property (nonatomic, assign) CGFloat progress;

@property (nonatomic, assign) BOOL forceDismiss;

+ (instancetype)sharedInstance;

- (void)show;

- (void)dismiss;

- (void)destory;

/// 异常退出
- (void)appExit;

@end

NS_ASSUME_NONNULL_END
