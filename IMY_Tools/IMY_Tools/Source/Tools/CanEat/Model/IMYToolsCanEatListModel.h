//
//  IMYToolsCanEatListModel.h
//  IMYTools
//
//  Created by angBiu on 2020/11/3.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYToolsCanEatListModel : NSObject

@end


@interface IMYToolsCanEatRankModel : NSObject

@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *share_url;
@property (nonatomic, copy) NSString *list_id;

@property (nonatomic, copy) NSArray *taboo_list;

@end

NS_ASSUME_NONNULL_END
