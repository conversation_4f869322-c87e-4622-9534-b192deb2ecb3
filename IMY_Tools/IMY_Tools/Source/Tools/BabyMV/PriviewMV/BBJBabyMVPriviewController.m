//
//  BBJBabyMVPriviewController.m
//  BBJBabyHome
//
//  Created by meetyou on 2024/11/21.
//

#import "BBJBabyMVPriviewController.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYTools/IMYBabyMVPAGPlayerView.h>
#import <IMYTools/IMYBabyMVTemplateListModel.h>
#import "IMYBabyMakeWeekMVViewModel.h"

@interface BBJBabyMVPriviewController ()

/// 顶部渐变视图层
@property (nonatomic, strong) UIView * topGradientView;

@property (nonatomic, strong) IMYTouchEXButton * backButton;

@property (nonatomic, strong) IMYBabyMVPAGPlayerView * playerView;

/// 领取日历
@property (nonatomic, strong) UIButton * receiveBtn;

@property (nonatomic, strong) IMYCaptionView *captionView;

@property (nonatomic, strong) UIImageView *captionLoadingView;

@property (nonatomic, strong) IMYBabyMakeWeekMVViewModel * viewModel;

@property (nonatomic, strong) NSMutableArray<NSString *> *replaceTexts;
@property (nonatomic, strong) NSMutableArray<UIImage *> *replaceImages;
@property (nonatomic, copy) NSString *pagName;

@property (nonatomic, assign) UIStatusBarStyle barStyle;

@property (nonatomic, assign) BOOL willDismiss;

/// 曝光
@property (nonatomic, assign) BOOL exposure;

/// 位置信息
@property (strong, nonatomic) NSArray<IMYPAGPositionModel*> * pagPositions;

@end

@implementation BBJBabyMVPriviewController

- (void)dealloc {
    _playerView = nil;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.barStyle = [UIApplication sharedApplication].statusBarStyle;
    // Do any additional setup after loading the view.
    [self setInitUI];
    [self requestData];
    [self setAction];
}

- (void)setAction {
    self.barStyle = [UIApplication sharedApplication].statusBarStyle;
    [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleLightContent];
    
    @weakify(self);
    [[[UIApplication sharedApplication] rac_valuesForKeyPath:@"statusBarStyle" observer:self] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if ([UIApplication sharedApplication].statusBarStyle != UIStatusBarStyleLightContent && self.willDismiss == false) {
            [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleLightContent];
        }
    }];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear: animated];
    self.willDismiss = true;
}

- (void)setInitUI {
    
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    [self.view imy_setBackgroundColor:[UIColor imy_colorWithRGBA:@"#000000"]];
    
    [self.view addSubview:self.playerView];
    [self.playerView addSubview:self.captionLoadingView];
    
    [self.view addSubview:self.topGradientView];
    [self.view addSubview:self.backButton];
    [self.view addSubview:self.receiveBtn];
    
    [self.captionLoadingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.topGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.view);
        make.bottom.mas_equalTo(self.backButton.mas_bottom).mas_offset(-12);
    }];
    
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).mas_offset(16);
        make.width.height.mas_equalTo(22);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(SCREEN_STATUSBAR_HEIGHT + 14);
    }];
    
    [self.receiveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-SCREEN_TABBAR_SAFEBOTTOM_MARGIN-12);
        make.left.mas_equalTo(12);
        make.right.mas_equalTo(-12);
        make.height.mas_equalTo(46);
    }];
    
    if (!self.btnUri || !self.btnTitle) {
        [self.receiveBtn setHidden:YES];
    } else {
        [self.receiveBtn setHidden:NO];
        [self.receiveBtn setTitle:self.btnTitle forState:UIControlStateNormal];
    }
    
}

/// 初始化数据
- (void)requestData {
    
    @weakify(self);
    [self.captionLoadingView setHidden:NO];
    [self.captionView setHidden:YES];
    self.captionLoadingView.alpha = 1;
    [self.viewModel requestDataWithBabyId:self.babyId mvId:self.mvId commonBabyId: self.commonBabyId completeWithRequestBlock:^(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel * _Nonnull model) {
        @strongify(self);
        [self.captionLoadingView imy_setImageURL:model.tModel.first_cover];
    } completeWithRequestResourcesBlock:^(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel * _Nonnull model) {
        @strongify(self);
        if (bSuccess) {
            self.pagName = model.tModel.local_pag_file_path;
            self.playerView.isReverseEditOrder = model.tModel.isReverseEditOrder;
            self.replaceTexts = [model displayTextList:NO];
            [self updatePositionModelsWithModel:model];
            [self reloadPagData];
        } else {
            [self.captionView setHidden:NO];
            [self.captionView setTitle: IMYNetState.networkEnable ? @"加载失败，请点击重新加载试试" : @"网络不见了，请检查网络"
                              forState:IMYCaptionViewStateRetry];
            self.captionView.state = IMYCaptionViewStateRetry;
            self.captionLoadingView.alpha = 0;
        }
        
        /// 按键曝光
        [self receiveEvent:1];
    }];
    
}

- (void)updatePositionModelsWithModel:(IMYBabyMVTemplateForMaterialModel*) model {
    NSString * pagFilePath = model.tModel.local_pag_file_path;
    
    NSMutableArray * positions = [[NSMutableArray alloc] initWithArray:[IMYPAGPositionModel getModelsWithFilePath:pagFilePath]];
    
    NSInteger index = 0;
    for (IMYBabyMVMixMaterialAssetModel * asset in model.materialsMix.medias) {
        if (index >= positions.count) {
            break;
        }
        IMYPAGPositionModel * positionModel = positions[index];
        if (asset.type == 1) {
            positionModel.image = asset.image;
        } else {
            positionModel.duration = asset.duration * 1000000;
            positionModel.path = asset.localFilePath;
        }
        index ++;
    }
    self.pagPositions = positions;
}

- (void)reloadPagData {
    // 替换图片
    @weakify(self);
    [self.playerView loadPAGAndPlay:self.pagName completeBlock:^(BOOL bSuccess) {
        @strongify(self);
        [UIView animateWithDuration:0.1 animations:^{
            self.captionLoadingView.alpha = 0;
        }];
    }];
    [self.playerView replaceText:self.replaceTexts];
    [self.playerView replace:self.pagPositions];
}


#pragma mark - action

- (void)receiveBtnAction {
    [self receiveEvent:2];
    IMYURI * uri = [IMYURI uriWithURIString:self.btnUri];
    [[IMYURIManager sharedInstance] runActionWithURI:uri completed:nil];
}

- (void)backButtonAction {
    self.willDismiss = true;
    [[UIApplication sharedApplication] setStatusBarStyle:self.barStyle];
    
//    UIViewController *controller = self;
//    while(controller.presentingViewController != nil){
//       controller = controller.presentingViewController;
//    }
//    [controller dismissViewControllerAnimated:NO completion:^{
//        
//    }];
    [self dismissViewControllerAnimated:YES completion:^{
        
    }];
    
}

- (void)receiveEvent:(NSInteger) action {
    if (self.receiveBtn.isHidden) {
        return;
    }
    
    if (self.exposure && action == 1) {
        return;
    }
    
    self.exposure = YES;
    NSMutableDictionary * params = [NSMutableDictionary new];
    [params setObject:@"yy_mvbfy_lqan" forKey:@"event"];
    [params setObject:@(action) forKey:@"action"];
    if (self.viewModel.changeModel.tModel.tid) {
        [params setObject:@(self.viewModel.changeModel.tModel.tid) forKey:@"public_type"];
    }
    if (self.babyId) {
        [params setObject:@(self.babyId) forKey:@"baby_id"];
    }
    
    if (self.viewModel.changeModel.tModel.title) {
        [params setObject:self.viewModel.changeModel.tModel.title forKey:@"public_info"];
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

#pragma mark - get

- (void)setReplaceImages:(NSMutableArray<UIImage *> *)replaceImages {
    NSMutableArray<UIImage *> *compressedArray = [[NSMutableArray alloc] initWithCapacity:replaceImages.count];
    for (UIImage *image in replaceImages) {
        [compressedArray addObject:[IMYBabyMVPAGPlayerView compressedImage:image]];
    }
    _replaceImages = compressedArray;
}

- (IMYBabyMakeWeekMVViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [IMYBabyMakeWeekMVViewModel new];
    }
    return _viewModel;
}

- (UIView *)topGradientView {
    if (!_topGradientView) {
        _topGradientView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_HEIGHT + 14 + 22 + 12)];
        _topGradientView.userInteractionEnabled = NO;
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        id color1 = (__bridge id)[[UIColor blackColor] colorWithAlphaComponent:0.4].CGColor;
        id color2 = (__bridge id)[[UIColor blackColor] colorWithAlphaComponent:0].CGColor;
        gradientLayer.colors = @[color1, color2];
        gradientLayer.locations =  @[@0.0, @1.0];
        gradientLayer.startPoint = CGPointMake(0.5, 0.0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        gradientLayer.frame = _topGradientView.bounds;
        [_topGradientView.layer addSublayer:gradientLayer];
    }
    return _topGradientView;
}

- (IMYTouchEXButton *)backButton {
    if (!_backButton) {
        _backButton = [[IMYTouchEXButton alloc] init];
        [_backButton imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
        [_backButton setExtendTouchInsets:UIEdgeInsetsMake(10, 16, 10, 4)];
        [_backButton imy_setImage:@"bbj_all_navibar_icon_close_white"];
        [_backButton setExtendTouchAllValue:20];
        
        @weakify(self);
        [[_backButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            [self backButtonAction];
        }];
    }
    return _backButton;
}

- (IMYBabyMVPAGPlayerView *)playerView {
    if (!_playerView) {
        CGFloat scale = 16.0/9.0;//708.0/375.0;
        CGFloat width = SCREEN_WIDTH;
        CGFloat height = width * scale;

        CGFloat offsetY = SCREEN_STATUSBAR_HEIGHT;
        if (height >= SCREEN_HEIGHT) {
            offsetY = 0;
        }
        _playerView = [[IMYBabyMVPAGPlayerView alloc] initWithFrame:CGRectMake(0, offsetY, SCREEN_WIDTH, height)];
        _playerView.userInteractionEnabled = TRUE;
    }
    return _playerView;
}

- (UIButton *)receiveBtn {
    if (!_receiveBtn) {
        _receiveBtn = [[UIButton alloc] init];
        [_receiveBtn imy_drawAllCornerRadius:23];
        [_receiveBtn imy_setTitle:@"领取实体台历"];
        UIColor * titleColor = [UIColor imy_colorForKey:kCK_White_A];
        [_receiveBtn imy_setTitleColor:titleColor];
        _receiveBtn.titleLabel.font = [UIFont imy_mediumWith:17];
        
        [_receiveBtn imy_setBackgroundColor:[UIColor imy_colorWithRGBA:@"#FF4D88"]];
        @weakify(self);
        [[_receiveBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            [self receiveBtnAction];
        }];
    }
    return _receiveBtn;
}

- (IMYCaptionView *)captionView {
    if (!_captionView) {
        _captionView = [IMYCaptionView addToView:self.playerView show:YES];
        _captionView.state = IMYCaptionViewStateLoading;
        [_captionView imy_setBackgroundColorForKey:kCK_Black_F];
        [_captionView.imageView setHidden:YES];
        @weakify(self);
        _captionView.retryBlock = ^() {
            @strongify(self);
//            [self requestData];
            [self dismissViewControllerAnimated:NO completion:^{
                [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                
                IMYURI * uri = [IMYURI uriWithURIString:self.btnUri];
                [[IMYURIManager sharedInstance] runActionWithURI:uri completed:nil];
            }];
        };
    }
    return _captionView;
}

- (UIImageView *)captionLoadingView {
    if (!_captionLoadingView) {
        _captionLoadingView = [[UIImageView alloc] initWithFrame:self.playerView.bounds];
        [_captionLoadingView imy_setPlaceholderImage:[UIImage imageWithColor:[UIColor imy_colorForKey:kCK_Black_FN] ]];
        [_captionLoadingView imy_setBackgroundColorForKey:kCK_Black_F];
        UIActivityIndicatorView *indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        [_captionLoadingView addSubview:indicatorView];
        [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_equalTo(self.captionLoadingView);
            make.width.height.mas_equalTo(26);
        }];
        [indicatorView startAnimating];
        [_captionLoadingView setHidden:YES];
    }
    return _captionLoadingView;
}

@end
