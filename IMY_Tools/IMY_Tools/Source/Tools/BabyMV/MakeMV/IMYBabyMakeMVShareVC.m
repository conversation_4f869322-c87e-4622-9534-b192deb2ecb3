//
//  IMYBabyMakeMVShareVC.m
//  IMYTools
//
//  Created by ss on 2023/9/25.
//

#import "IMYBabyMakeMVShareVC.h"
#import "BBJUtil.h"
#import "BBJServerRequest.h"
#import <BBJViewKit/BBJViewKit.h>
#import <IMYBaseKit/IMYCustomNavigationBarManager.h>
#import "IMYBabyMVVedioPlayerView.h"
#import "IMYPAGView.h"
#if __has_include(<BBJBabyHome/BBJBabyModel.h>)
#import <BBJBabyHome/BBJBabyModel.h>
#import <BBJBabyHome/BBJBabyRecordModel.h>
#import "BBJInviteManager.h"
#endif
#import "IMYRecordPregnancyBabyManager.h"
#import "IMYCoolShareSheet.h"

@interface IMYBabyMakeMVShareVC () <UIGestureRecognizerDelegate>{
    UIInterfaceOrientation _statusBarOrientationBefore;
}

@property (nonatomic, strong) IMYBabyMVVedioPlayerView *playerView;
@property (nonatomic, copy) NSString *videoPath;
@property (nonatomic, assign) NSInteger babyId;
@property (nonatomic, assign) NSInteger recordId;
#if __has_include(<BBJBabyHome/BBJBabyModel.h>)
@property (nonatomic, strong) BBJBabyModel *babyModel;
@property (nonatomic, strong) BBJBabyRecordModel *recordModel;
#endif
@property (nonatomic, strong) IMYButton *shareButton; ///< 分享按钮
@property (nonatomic, strong) IMYButton *backButton;

@property (nonatomic, strong) IMYPAGView *bgPAGImageView;
@property (nonatomic, strong) UIView *shareBgView;

@end


@implementation IMYBabyMakeMVShareVC

- (void)dealloc {
    [_playerView removeFromSuperview];
    _playerView = nil;
    _bgPAGImageView = nil;
}
- (instancetype)initWithVideoPath:(NSString *)videoPath babyId:(NSInteger)babyId recordId:(NSInteger)record {
    if (self = [super init]) {
        _videoPath = videoPath;
        _babyId = babyId;
        _recordId = record;
    }
    return self;
}
 
- (void)viewDidLoad {
    [super viewDidLoad];

    [self.view imy_setBackgroundColorForKey:kCK_Black_F];
    [self commonInit];
    /// 进入页面弹出分享
    if (!self.shareBgView.hidden) {
        [self shareAction];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    if (!iPhoneX) {
        [[UIApplication sharedApplication] setStatusBarHidden:YES withAnimation:UIStatusBarAnimationNone];
    }
}
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (!iPhoneX) {
        [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
    }
    [self.playerView pause];
}
- (BOOL)prefersStatusBarHidden {
    return YES;
}

- (void)commonInit {
    [self setupNavigationBar];
    [self createPlayerView];
    [self createBottomView];
}
- (void)setupNavigationBar {
    UIImageView *bgImageView = [[UIImageView alloc] init];
    [self.view addSubview:bgImageView];
    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).mas_offset(0);
        make.left.right.mas_equalTo(self.view);
        make.height.mas_equalTo(280);
    }];
    [bgImageView imy_setImage:@"BabyMakeMVBg"];
    
    [self.view addSubview:self.bgPAGImageView];
    [self.bgPAGImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).mas_offset(SCREEN_HEIGHT <= 667 ? 0 : SCREEN_STATUSBAR_HEIGHT);
        make.left.right.mas_equalTo(self.view);
        make.height.mas_equalTo(236);
    }];
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    
    [self.view addSubview:self.backButton];
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).mas_offset(16);
        make.width.height.mas_equalTo(22);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(SCREEN_STATUSBAR_HEIGHT + 14);
    }];
}

- (void)createPlayerView {
    CGFloat scale = 1280.0/720;
    CGFloat width = SCREEN_WIDTH  - 68 * 2;
    CGFloat height = width * scale;
    CGFloat offsetY = (SCREEN_HEIGHT <= 667 ? 92 : 112) + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT ;
    CGFloat offsetX = 68;
    if ( (height + offsetY + 72 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN) > SCREEN_HEIGHT) {
        offsetY -= ((height + offsetY + 72 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN) - SCREEN_HEIGHT + 12);
    }
    CGRect frame = CGRectMake(68,
                              offsetY,
                              width,
                              height);
    UIView *bgView = [[UIView alloc] initWithFrame:frame];
    bgView.clipsToBounds = YES;
    bgView.layer.cornerRadius = 12;
    [bgView imy_addThemeChangedBlock:^(UIView *weakObject) {
        weakObject.layer.borderColor = [UIColor imy_colorForKey:kCK_White_AN].CGColor;
    }];
    bgView.layer.borderWidth = 1;
    
    _playerView = [[IMYBabyMVVedioPlayerView alloc] initWith:[NSURL fileURLWithPath:self.videoPath] repeat:YES];
    _playerView.frame = bgView.bounds;
    _playerView.curViewController = self;
    _playerView.clipsToBounds = YES;
    [_playerView imy_drawAllCornerRadius:12];
    @weakify(self);
    _playerView.progressBlock = ^(CGFloat duration, CGFloat currentTime) {
        @strongify(self);
//            [self.progressView updateProgress:currentTime * 100 / (duration ?: 1)];
    };
    [self.view addSubview:bgView];
    [bgView addSubview:_playerView];
}

- (void)createBottomView {
    UIView *shareBgView = [[UIView alloc] init];
    self.shareBgView = shareBgView;
    [shareBgView imy_setBackgroundColorForKey:kCK_White_AN];
    [self.view addSubview:shareBgView];
    [shareBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.mas_equalTo(self.view);
        make.height.mas_equalTo(72 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
    }];
    [shareBgView addSubview:self.shareButton];

    [self.shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(shareBgView).mas_offset(12);
        make.left.mas_equalTo(shareBgView).mas_offset(12);
        make.right.mas_equalTo(shareBgView).mas_offset(-12);
        make.height.mas_equalTo(48);
    }];
    if (!self.baby_follower.can_record) {
        [self.shareBgView setHidden:YES];
    } else {
        [self.shareBgView setHidden:!self.baby_follower.can_share];
    }
}

- (void)setBaby_follower:(IMYBabyMVTemplateBabyFollowerModel *)baby_follower {
    _baby_follower = baby_follower;
    if (!baby_follower.can_record) {
        [self.shareBgView setHidden:YES];
    } else {
        [self.shareBgView setHidden:!baby_follower.can_share];
    }
    if (!baby_follower.can_record && self.noCheckSaveVideo == NO) {//没有分享权限
        [self saveVedioToAlbum:self.videoPath];
    }
}

- (void)saveVedioToAlbum:(NSString *)videoPath {
    if (UIVideoAtPathIsCompatibleWithSavedPhotosAlbum(videoPath)) {
        NSError *error = nil;
        __block PHObjectPlaceholder *placeholder;
        [[PHPhotoLibrary sharedPhotoLibrary] performChangesAndWait:^{
            PHAssetChangeRequest *request = [PHAssetChangeRequest creationRequestForAssetFromVideoAtFileURL:[NSURL URLWithString:videoPath]];
            placeholder = [request placeholderForCreatedAsset];
        } error:&error];
        if (error) {
            [UIWindow imy_showTextHUD:IMYString(@"保存至手机失败")];
        }else{
            [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"已保存至手机")];
        }
    }else{
        [UIWindow imy_showTextHUD:@"视频保存失败,请设置软件读取相册权限"];
    }
}
#pragma makr - 分享
- (void)shareAction {
    NSMutableArray<IMYCoolShareItem *> *items = [NSMutableArray array];
    [items addObject:[IMYCoolShareItem itemWithName:IMYString(@"微信好友") icon:@"all_share_btn_wechat" tag:IMYCoolShareSheetTypeWeixiSession]];
    [items addObject:[IMYCoolShareItem itemWithName:IMYString(@"微信朋友圈") icon:@"all_share_btn_moments" tag:IMYCoolShareSheetTypeWeixiTimeline]];
    if ([IMYShareSDK isShareTypeAppInstalled:IMYShareSDKTypeDouYin]) {
        [items addObject:[IMYCoolShareItem itemWithName:IMYString(@"抖音") icon:@"all_share_btn_douyin" tag:IMYCoolShareSheetTypeDouYin]];
    }
    if ([IMYShareSDK isShareTypeAppInstalled:IMYShareSDKTypeXiaoHongShu]) {
        [items addObject:[IMYCoolShareItem itemWithName:IMYString(@"小红书") icon:@"all_share_btn_xiaohongshu" tag:IMYCoolShareSheetTypeXiaoHongShu]];
    }
    @weakify(self);
    [IMYCoolShareSheet customShareInViewController:self.navigationController title:IMYString(@"分享到") configList:@[items] indexBlock:^(IMYCoolShareSheetType itemType, NSInteger shareType) {
        if (shareType == -1) {
            return;
        }
        @strongify(self);
        if (shareType <= 0) {
            return;
        }
        [self.playerView pause];
        if (itemType == IMYCoolShareSheetTypeWeixiSession) {
            [self shareWithType:IMYShareSDKTypeWeChatSession];;
        } else if (itemType == IMYCoolShareSheetTypeWeixiTimeline) {
            [self shareWithType:IMYShareSDKTypeWeChatTimeline];
        } else if (itemType == IMYCoolShareSheetTypeDouYin) {
            [self shareWithType:IMYShareSDKTypeDouYin];
        } else if (itemType == IMYCoolShareSheetTypeXiaoHongShu) {
            [self shareWithType:IMYShareSDKTypeXiaoHongShu];
        }
    }];
}

- (void)requestRecordDetailCompletion:(BBJCompletionBlk)completion{
    if (self.babyId == 0 || self.recordId == 0) {
        !completion?:completion(@(NO),nil);
        return ;
    }
    
#if __has_include(<BBJBabyHome/BBJBabyModel.h>)
    if (self.babyModel && self.recordModel) {
        !completion?:completion(@(YES),nil);
    } else {
        NSDictionary *param = @{@"baby_id":@(self.babyId), @"record_id":@(self.recordId)};
        @weakify(self);
        [BBJServerRequest getPath:@"/album" params:param completion:^(id  _Nullable data, NSError * _Nullable error) {
             @strongify(self);
            if (error) {
                !completion?:completion(@(NO),error);
                return ;
            }
            imy_syncMainExecuteBlock(^{
                self.babyModel = [data[@"baby"] toModel:[BBJBabyModel class]];
                self.recordModel = [data[@"record"] toModel:[BBJBabyRecordModel class]];
                !completion?:completion(@(YES),nil);
            });
        }];
    }
#endif
}

- (void)shareWithType:(IMYShareSDKType)type{
#if __has_include(<BBJBabyHome/BBJBabyModel.h>)
    @weakify(self);
    [self requestRecordDetailCompletion:^(id  _Nullable resData, NSError * _Nullable error) {
        @strongify(self);
        BOOL result = [resData boolValue];
        if (result) {
            if (type == IMYShareSDKTypeWeChatSession && [BBJUtil bbj_record_share_switch]) {
                NSString *videoPicture = self.recordModel.record_detail.firstObject.picture;
                NSString *ageString = [self getShareMiniProgresssTitleWithBabyBirthDay:self.babyModel.birthday due:self.babyModel.due isBirth:self.babyModel.is_birth recordDate:self.recordModel.record_date];
                [[BBJInviteManager shareInstance] handleShareToMiniProgranWithBabyId:self.babyId commonBabyId:self.babyModel.common_baby_id category:1 recordId:self.recordId albumId:0 ageString:ageString mvShareUrl:nil source:2 image:nil imageUrl:videoPicture shareType:type];
            } else {
                //分享视频
                __block NSString *content = self.recordModel.content;
                if (content.length == 0) {
                    content = [NSString stringWithFormat:@"快来看看%@的新变化吧~",self.babyModel.nickname];
                } else if (content.length > 140) {
                    content = [content substringToIndex:140];
                }
                NSString *urlString = [NSString stringWithFormat:@"%@/share/record?sign=%@&app_id=%@",bbj_meiyou_com,self.recordModel.guid,[IMYPublicAppHelper shareAppHelper].app_id];
                if (type == IMYShareSDKTypeWeibo) {
                    if (content.length > 110) {
                        content = [content substringToIndex:110];
                    }
                    content = [content stringByAppendingString:urlString];
                    [self shareWithContent:content urlString:urlString shareType:type];
                } else {
                    [self shareWithContent:content urlString:urlString shareType:type];
                }
            }
        } else {
            [self.view imy_showTextHUD:MT_Request_NoNetToast];
        }
    }];
#endif
    
    //bi report
    NSMutableDictionary *params = [NSMutableDictionary new];
    if (type == IMYShareSDKTypeWeChatTimeline) {
        [params imy_setNonNilObject:@"yy_mvwcy_fxpyq" forKey:@"event"];
    } else if (type == IMYShareSDKTypeDouYin) {
        [params imy_setNonNilObject:@"yy_mvzzwcy_fxdy" forKey:@"event"];
    } else if (type == IMYShareSDKTypeXiaoHongShu) {
        [params imy_setNonNilObject:@"yy_mvzzwcy_fxxhs" forKey:@"event"];
    } else {
        [params imy_setNonNilObject:@"yy_mvwcy_fxwx" forKey:@"event"];
    }
    [params imy_setNonNilObject:@(2) forKey:@"action"];// 2 点击
    [params imy_setNonNilObject:@(self.tid) forKey:@"public_type"];
    [params imy_setNonNilObject:@(self.common_baby_id) forKey:@"common_baby_id"];
    [params imy_setNonNilObject:@(self.babyId) forKey:@"baby_id"];
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (void)shareWithContent:(NSString *)content urlString:(NSString *)urlString shareType:(IMYShareSDKType)shareType {
#if __has_include(<BBJBabyHome/BBJBabyModel.h>)
    NSString *title = [self getShareTitleWithBabyBirthDay:self.babyModel.birthday due:self.babyModel.due isBirth:self.babyModel.is_birth nickname:self.babyModel.nickname recordDate:self.recordModel.record_date];
    id<IMYIAttachment> attachment = [IMYShareSDK attachmentWithURL:[NSURL URLWithString:self.recordModel.record_detail.firstObject.picture]];
    
    NSDictionary *ext = nil;
    if (shareType == IMYShareSDKTypeDouYin || shareType == IMYShareSDKTypeXiaoHongShu) {
        NSString *babyName = IMYString(@"宝宝");
        if (self.babyModel.nickname.length > 0 && self.babyModel.nickname.length <= 3) {
            babyName = self.babyModel.nickname;
        }
        if (self.mv_type == 1) {
            title = [NSString stringWithFormat:@"%@的周MV", babyName];
        } else {
            title = [NSString stringWithFormat:@"%@的成长MV", babyName];
        }
        NSArray<NSDictionary *> *mediaResources = @[@{
            @"type" : @4,
            @"url" : self.recordModel.record_detail.firstObject.video,
        }];
        ext = @{@"hashtags": @"#美柚App,#美柚育儿模式", @"mediaResources": mediaResources};
        content = @"";
    }
    id<IMYIContent> shareContent = [IMYShareSDK contentWithText:content image:attachment title:title url:urlString description:nil ext:ext contentType:0];
    shareContent.contentType = IMYIContentTypeMedia;
    [self shareContent:shareContent shareType:shareType];
#endif
}

- (NSString *)getShareTitleWithBabyBirthDay:(NSDate *)birthday due:(NSDate *)due isBirth:(BOOL)isBirth nickname:(NSString *)nickname recordDate:(NSDate *)recordDate{
    NSString *babyname = nickname;
    NSMutableString *title = [[NSMutableString alloc] initWithString:babyname?:@""];
    birthday = isBirth ? birthday : nil;
    NSString *pregnancyText = [recordDate bbj_getPregnancyDayStringWithBabyBirthday:birthday dueDay:due];
    NSString *ageText = [recordDate bbj_babyBirthdayString:birthday];
    if (imy_isNotEmptyString(pregnancyText)) {
        return [title stringByAppendingFormat:@"|%@", pregnancyText];
    }else{
        return [title stringByAppendingFormat:@"|%@", ageText];
    }
    return title;
}

- (NSString *)getShareMiniProgresssTitleWithBabyBirthDay:(NSDate *)birthday due:(NSDate *)due isBirth:(BOOL)isBirth recordDate:(NSDate *)recordDate{
    birthday = isBirth ? birthday : nil;
    NSString *pregnancyText = [recordDate bbj_getPregnancyDayStringWithBabyBirthday:birthday dueDay:due];
    NSString *ageText = [recordDate bbj_babyBirthdayString:birthday];
    if (imy_isNotEmptyString(pregnancyText)) {
        return pregnancyText;
    }else{
        return ageText;
    }
}

- (void)shareContent:(id<IMYIContent>)content shareType:(IMYShareSDKType)type{
    [IMYShareSDK shareContent:content type:type completion:^(BOOL result, NSError *error) {
        if (result) {
            [UIWindow imy_showTextHUDWithDelay:2 text:IMYString(@"分享成功")];
            return ;
        }
        NSLog(@"分享失败,错误码:%ld,错误描述:%@", (long)[error code], [error localizedDescription]);
        if (error.code == IMYShareSDKErrorAppNotFound) {
            switch (type) {
                case IMYShareSDKTypeWeChatSession:
                case IMYShareSDKTypeWeChatTimeline: {
                    [UIWindow imy_showTextHUD:IMYString(@"您尚未安装微信哦~")];
                    break;
                }
                case IMYShareSDKTypeDouYin: {
                    [UIWindow imy_showTextHUD:IMYString(@"您尚未安装抖音哦~")];
                    break;
                }
                case IMYShareSDKTypeXiaoHongShu: {
                    [UIWindow imy_showTextHUD:IMYString(@"您尚未安装小红书哦~")];
                    break;
                }
                default:
                    break;
            }
        } else if ([error code] == IMYShareSDKErrorUserCancelled) {
            [UIWindow imy_showTextHUDWithDelay:2 text:IMYString(@"已取消分享")];
        } else {
            [UIWindow imy_showTextHUDWithDelay:2 text:IMYString(@"分享失败")];
        }
    }];
}


//MARK: - getter
- (IMYTouchEXButton *)backButton {
    if (!_backButton) {
        IMYTouchEXButton *button = [[IMYTouchEXButton alloc] init];
        [button imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
        [button setExtendTouchInsets:UIEdgeInsetsMake(10, 16, 10, 4)];
        [button imy_setImage:@"nav_btn_close_black"];
        [button setExtendTouchAllValue:20];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            NSInteger pregnancy_bbj_babyId = [IMYRecordPregnancyBabyManager sharedInstance].bbj_baby_id;
            if (self.babyId == pregnancy_bbj_babyId && [IMYPublicAppHelper shareAppHelper].userSecondModel.x_visit_mode == 0) {
                IMYURI *uri = [IMYURI uriWithPath:@"seeyoubaby/dismiss/babyHome" params:@{@"viewController": self, @"babyId": @(self.babyId), @"babyType": @(2)} info:nil];
                [[IMYURIManager shareURIManager] runActionWithURI:uri];
            } else {
                UIViewController *controller = self;
                while(controller.presentingViewController != nil){
                    controller = controller.presentingViewController;
                }
                [controller dismissViewControllerAnimated:NO completion:^{
                    if ([IMYPublicAppHelper shareAppHelper].userSecondModel.x_visit_mode > 0
                        && [IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeQinYou) {
                        NSArray *array = [UIViewController imy_currentViewControlloer].navigationController.viewControllers;
                        for (UIViewController *ctl in array) {
                            if ([ctl.class.description hasPrefix:@"IMYBaby"]) {
                                [ctl imy_removeSelfInNavigationController];
                                break;
                            }
                        }
                    } else {
                        [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                        [[IMYURIManager shareURIManager] runActionWithString:@"home"];
                    }
                }];
            }
        }];
        _backButton = button;
    }
    return _backButton;
}

- (IMYButton *)shareButton {
   if (!_shareButton) {
       _shareButton = [[IMYButton alloc] init];
       [_shareButton imy_setTitle:IMYString(@"立即分享")];
       _shareButton.titleLabel.font = [UIFont imy_mediumWith:17];
       [_shareButton imy_setBackgroundColorForKey:kCK_Red_B];
       [_shareButton imy_drawAllCornerRadius:24];
       @weakify(self);
       [[_shareButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
           @strongify(self);
           [self shareAction];
       }];
   }
   return _shareButton;
}

- (IMYPAGView *)bgPAGImageView{
    if (!_bgPAGImageView) {
        _bgPAGImageView = [IMYPAGView new];
        _bgPAGImageView.clipsToBounds = YES;
        [_bgPAGImageView setRepeatCount:1];
        
        [_bgPAGImageView imy_addThemeChangedBlock:^(IMYPAGView *weakObject) {
            if ([IMYPublicAppHelper shareAppHelper].isNight) {
                NSString *path = [[NSBundle mainBundle] pathForResource:@"make_vm_share_bg_night" ofType:@"pag"];
                if (imy_isNotEmptyString(path)) {
                    [weakObject loadWithURL:[NSURL fileURLWithPath:path] placeholder:nil completed:nil];
                }
            } else {
                NSString *path = [[NSBundle mainBundle] pathForResource:@"make_vm_share_bg" ofType:@"pag"];
                if (imy_isNotEmptyString(path)) {
                    [weakObject loadWithURL:[NSURL fileURLWithPath:path] placeholder:nil completed:nil];
                }
            }
        }];
    }
    return _bgPAGImageView;
}

@end
