//
//  IMYBabyMVPAGPlayerView.m
//  IMYTools
//
//  Created by ss on 2023/9/21.
//

#import "IMYBabyMVPAGPlayerView.h"
#import <libpag/PAGView.h>
#import <libpag/PAGSurface.h>
#import <libpag/PAGPlayer.h>
#import <libpag/PAGMovie.h>
#import "IMY_ViewKit.h"
#import "UIImage+IMYToolsProcess.h"

@interface IMYBabyMVPAGPlayerView ()

@property (nonatomic, strong) PAGView *pagView;
@property (nonatomic, strong) NSString *pagPath;;
@property (nonatomic, strong) UIImageView *playButton;

@end

@implementation IMYBabyMVPAGPlayerView

- (void)dealloc {
    [_pagView stop];
    [_pagView freeCache];
    [_pagView removeFromSuperview];
    _pagView = nil;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
    }
    return self;
}

- (void)loadPAGAndPlay:(NSString *)pagPath completeBlock:(void (^)(BOOL bSuccess))completeBlock {
    [self loadPAGAndPlay:pagPath];
    !completeBlock ? : completeBlock(YES);
}

- (void)loadPAGAndPlay:(NSString*)pagPath {
    if (self.pagView != nil) {
        [self.pagView stop];
        [self.pagView removeFromSuperview];
        self.pagView = nil;
    }
    self.pagPath = pagPath;
    [self initPagView];
    [self.pagView setRepeatCount:-1];
    [self.pagView play];
    [self setupPlayButton];
}

- (void)initPagView {
    self.pagView = [[PAGView alloc] init];
    PAGFile *pagFile = [PAGFile Load:self.pagPath];
    [self.pagView setComposition:pagFile];
    [self addSubview:self.pagView];
    [self.pagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

#pragma mark - 点击播放与暂停
- (void)play {
    if (![self.pagView isPlaying]) {
        if (self.isFirstDrag) {
            self.isFirstDrag = NO;
            [self restart];
            return;
        }
        [self.pagView play];
        [self.playButton setHidden:YES];
    }
}
- (void)pause {
    if ([self.pagView isPlaying]) {
        [self.pagView pause];
        [self.playButton setHidden:NO];
    }
}
- (void)stop {
    if (self.pagView != nil) {
        [self.pagView stop];
        [self.pagView removeFromSuperview];
        self.pagView = nil;
        [self.playButton setHidden:YES];
    }
}
- (void)restart {
    if (self.pagView != nil) {
        [self.pagView stop];
        [self.pagView setProgress:0];
        [self.pagView play];
        [self.playButton setHidden:YES];
    }
}
- (void)touchesBegan:(NSSet*)touches withEvent:(UIEvent*)event {
    if ([self.pagView isPlaying]) {
        [self pause];
    } else {
        [self play];
    }
}

- (PAGComposition *)getCurrentComposition {
    return [self.pagView getComposition];
}

- (void)replace:(NSArray<IMYPAGPositionModel*> *) positions {
    if (positions.count <= 0) {
        return;
    }
    imy_asyncBlock(^{
        NSArray<IMYPAGPositionModel*> *pagPositionsTemp = positions;
        if (!self.isReverseEditOrder) {
            pagPositionsTemp = [[pagPositionsTemp reverseObjectEnumerator] allObjects];
        }
        NSArray<NSNumber*> * editableImageArray = [self getEditableIndices:PAGLayerTypeImage];
        PAGFile * pagFile = (PAGFile*)[self.pagView getComposition];
        for (int i = 0; i < pagPositionsTemp.count; i++) {
            if (i >= editableImageArray.count) {
                break;
            }
            
            NSInteger index = [editableImageArray[i] intValue];
            NSString * path = pagPositionsTemp[i].path;
            
            if (path.length > 0) {
                PAGMovie* movie = [PAGMovie MakeFromFile:path];
                [pagFile replaceImage:index data:movie];
                NSLog(@"replace Movie i = %d, index = %d path = %@",i,index,path);
            } else if (pagPositionsTemp[i].image) {
                @autoreleasepool {
                    UIImage * pagImage = [IMYBabyMVPAGPlayerView compressedImage:pagPositionsTemp[i].image];
                    PAGImage* image = [PAGImage FromCGImage:pagImage.CGImage];
                    [pagFile replaceImage:index  data:image];
                    NSLog(@"replace Image i = %d index = %d",i,index);
                }
            }
        }
        imy_asyncMainBlock(^{
            [self play];
        });
    });
}

- (void)replaceMovie:(NSArray<NSString *> *)moviePaths {
    NSArray<NSNumber*> * editableImageArray = [self getEditableIndices:PAGLayerTypeImage];
    moviePaths = [[moviePaths reverseObjectEnumerator] allObjects];
    for (int i = 0; i < moviePaths.count; i++) {
        if (i >= editableImageArray.count) {
            break;
        }
        PAGMovie* movie = [PAGMovie MakeFromFile:moviePaths[i]];
        NSLog(@"replaceMovie = %d path = %@",i,moviePaths[i]);
        [(PAGFile*)[self.pagView getComposition] replaceImage:[editableImageArray[i] intValue] data:movie];
    }
}

- (void)replaceImage:(NSArray<UIImage *> *)images offetIndex:(NSInteger)offsetIndex {
    NSArray<NSNumber*> * editableImageArray = [self getEditableIndices:PAGLayerTypeImage];
    if (!self.isReverseEditOrder) {
        images = [[images reverseObjectEnumerator] allObjects];
    }
    for (int i = 0; i < images.count; i++) {
        @autoreleasepool {
            if (i >= editableImageArray.count) {
                break;
            }
            UIImage *outImage = [images objectAtIndex:i];
            PAGImage* image = [PAGImage FromCGImage:outImage.CGImage];
            NSLog(@"replaceImage = %d editableIndex = %d path = %@",i,[editableImageArray[i + offsetIndex] intValue],images[i]);
            [(PAGFile*)[self.pagView getComposition] replaceImage:[editableImageArray[i + offsetIndex] intValue]  data:image];
        }
    }
}

- (void)replaceText:(NSArray<NSString *> *)textArray  {
    NSArray<NSNumber*> * editableTextArray = [self getEditableIndices:PAGLayerTypeText];
    if (editableTextArray.count > 0 && textArray.count == 0) {
        [self cleanAllText:(PAGFile*)[self.pagView getComposition]];
        return;
    }
    if (!self.isReverseEditOrder) {
        textArray = [[textArray reverseObjectEnumerator] allObjects];
    }
    for (int i = 0; i < textArray.count; i++) {
        @autoreleasepool {
            if (i >= editableTextArray.count) {
                break;
            }
            NSString *text = [textArray objectAtIndex:i];
            PAGText *pagText = [(PAGFile*)[self.pagView getComposition] getTextData:[editableTextArray[i] intValue]];
            pagText.text = textArray[i];
            [(PAGFile*)[self.pagView getComposition] replaceText:i data:pagText];
            NSLog(@"replaceImage = %d editableIndex = %d path = %@",i,[editableTextArray[i] intValue],textArray[i]);
        }
    }
}

- (void)cleanAllText:(PAGFile *)file {
    NSArray<NSNumber*> * editableTextArray = [self getEditableIndices:PAGLayerTypeText];
    for (int i = 0; i < editableTextArray.count; i++) {
        PAGText *pagText = [file getTextData:[editableTextArray[i] intValue]];
        pagText.text = @"";
        [file replaceText:i data:pagText];
    }
}
+ (UIImage *)compressedImage:(UIImage *)image {
    id<IMYImageCompressedResult> compressedResult = [image imy_screenWidthCompressedResult];
    return compressedResult.image;
}
- (NSInteger)numImages {
    return [(PAGFile*)[self.pagView getComposition] numImages];
}
- (NSInteger)numVideos {
    return [(PAGFile*)[self.pagView getComposition] numVideos];
}
- (NSArray<NSNumber*> *)getEditableIndices:(PAGLayerType)layerType {
    return [(PAGFile*)[self.pagView getComposition] getEditableIndices:layerType];
}

#pragma mark - 播放按钮
- (void)setupPlayButton {
    self.playButton = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 52, 52)];
    [self.playButton imy_setImage:@"daduzhao_icon_play"];
    [self insertSubview:self.playButton aboveSubview:self.pagView];
    [self.playButton setHidden:YES];
    [self.playButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self);
        make.width.mas_equalTo(@(52));
        make.height.mas_equalTo(@(52));
    }];
}

/*
 创建一个和画面内容一致的PAGComposition。这里不能直接使用PAGView持有的PAGComposition实例，
 因为同一个PAGComposition不能同时在多个PAGPlayer、PAGView、PAGMovieExporter中使用。
 */
- (PAGComposition*)createComposition:(NSArray<IMYPAGPositionModel*> *) pagPositions
                       replaceImages:(NSArray *)replaceImages
                     andReplaceTexts:(NSArray *)replaceTexts {
    PAGFile* file = [PAGFile Load:self.pagPath];
    if (file == nil) {
        return nil;
    }
    
    NSArray<NSNumber*> * editableImageArray = [self getEditableIndices:PAGLayerTypeImage];
    NSArray<NSNumber*> * editableTextArray = [self getEditableIndices:PAGLayerTypeText];
    NSArray *replaceTextsTemp = replaceTexts;
    NSArray *replaceImageTemp = replaceImages;
    NSArray<IMYPAGPositionModel*> *pagPositionsTemp = pagPositions;
    if (!self.isReverseEditOrder) {
        replaceTextsTemp = [[replaceTexts reverseObjectEnumerator] allObjects];
        replaceImageTemp = [[replaceImages reverseObjectEnumerator] allObjects];
        pagPositionsTemp = [[pagPositions reverseObjectEnumerator] allObjects];
    }
    if (editableTextArray.count > 0 && replaceTextsTemp.count == 0) {
        [self cleanAllText:file];
    } else {
        for (int i = 0; i < replaceTextsTemp.count; i++) {
            if (i >= editableTextArray.count) {
                break;
            }
            PAGText *pagText = [file getTextData:[editableTextArray[i] intValue]];
            pagText.text = [replaceTextsTemp objectAtIndex:i];;
            [file replaceText:i data:pagText];
        }
    }
    
    // 优先位置数组进行
    if (pagPositions.count > 0) {
        for (int i = 0; i < pagPositionsTemp.count; i++) {
            if (i >= editableImageArray.count) {
                break;
            }
            NSString * path = pagPositionsTemp[i].path;
            NSInteger index = [editableImageArray[i] intValue];
            
            if (path.length > 0) {
                PAGMovie* movie = [PAGMovie MakeFromFile:path];
                [file replaceImage:[editableImageArray[i] intValue] data:movie];
                NSLog(@"create Movie i = %d, index = %d path = %@",i,index,path);
            } else if (pagPositionsTemp[i].image) {
                @autoreleasepool {
                    UIImage * pagImage = [IMYBabyMVPAGPlayerView compressedImage:pagPositionsTemp[i].image];
                    PAGImage* image = [PAGImage FromCGImage:pagImage.CGImage];
                    [file replaceImage:[editableImageArray[i] intValue]  data:image];
                    
                    NSLog(@"create Image i = %d index = %d",i,index);
                }
            }
        }
        return file;
    }
   
    for (int i = 0; i < replaceImageTemp.count; i++) {
        if (i >= editableImageArray.count) {
            break;
        }
        UIImage* imageData = replaceImageTemp[i];
        PAGImage* image = [PAGImage FromCGImage:imageData.CGImage];
        [file replaceImage:[editableImageArray[i] intValue] data:image];
    }
    return file;
}

@end
