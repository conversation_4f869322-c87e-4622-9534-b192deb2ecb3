//
//  IMYBabyMVVedioPlayerView.h
//  IMYTools
//
//  Created by ss on 2023/9/25.
//

#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
#import <IMYBaseKit.h>
NS_ASSUME_NONNULL_BEGIN

@interface IMYBabyMVVedioPlayerView : UIView
@property (nonatomic, strong) void(^progressBlock)(CGFloat duration, CGFloat currentTime);
@property (nonatomic, strong) void(^playerItemDidReachEndHandler)(void);
@property (nonatomic, strong) void(^playerItemDidStatus)(AVPlayerItemStatus status);
@property (nonatomic, strong, readonly) AVPlayerItem *playerItem;
@property (nonatomic, strong, readonly) AVPlayer *player;
@property (nonatomic, weak) UIViewController *curViewController;
- (instancetype)initWith:(NSURL *)videoURL repeat:(BOOL)repeat;
- (instancetype)initWithAsset:(AVAsset *)videoAsset repeat:(BOOL)repeat;
- (instancetype)initWithPlayerItem:(AVPlayerItem *)item repeat:(BOOL)repeat;
- (void)play;
- (void)pause;
- (void)finish;
@end

NS_ASSUME_NONNULL_END
