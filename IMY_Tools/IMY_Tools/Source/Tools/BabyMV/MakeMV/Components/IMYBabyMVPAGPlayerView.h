//
//  IMYBabyMVPAGPlayerView.h
//  IMYTools
//
//  Created by ss on 2023/9/21.
//

#import <UIKit/UIKit.h>
#import <libpag/PAGComposition.h>
#import "IMYPAGPositionModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYBabyMVPAGPlayerView : UIView
@property (nonatomic, assign) BOOL isFirstDrag;//拖拽后第一次暂停，从头播放
@property (nonatomic, assign) BOOL isReverseEditOrder;//编辑顺序，默认反序

- (void)loadPAGAndPlay: (NSString*)pagPath;
- (void)loadPAGAndPlay:(NSString*)pagPath completeBlock:(void (^)(BOOL bSuccess))completeBlock ;

- (void)stop;
- (void)pause;
- (void)play;

- (NSInteger)numImages;
- (NSInteger)numVideos;

- (void)replaceMovie:(NSArray<NSString*>*)moviePaths;

- (void)replaceImage:(NSArray<UIImage *> *)images offetIndex:(NSInteger)offsetIndex ;

- (void)replaceText:(NSArray<NSString *> *)textArray ;

- (void)replace:(NSArray<IMYPAGPositionModel*> *) positions;

- (PAGComposition*)getCurrentComposition;

- (NSArray<NSNumber*> *)getEditableIndices:(PAGLayerType)layerType;

+ (UIImage *)compressedImage:(UIImage *)image;

/*
 创建一个和画面内容一致的PAGComposition。这里不能直接使用PAGView持有的PAGComposition实例，
 因为同一个PAGComposition不能同时在多个PAGPlayer、PAGView、PAGMovieExporter中使用。
 */
- (PAGComposition*)createComposition:(NSArray<IMYPAGPositionModel*> *) pagPositions
                       replaceImages:(NSArray *)replaceImages
                     andReplaceTexts:(NSArray *)replaceTexts;
@end

NS_ASSUME_NONNULL_END
