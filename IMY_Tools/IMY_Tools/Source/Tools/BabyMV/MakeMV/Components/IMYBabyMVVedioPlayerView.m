//
//  IMYBabyMVVedioPlayerView.m
//  IMYTools
//
//  Created by ss on 2023/9/25.
//

#import "IMYBabyMVVedioPlayerView.h"


static NSString *const kSVRPlayerItemStatus = @"_playerItem.status";

@interface IMYBabyMVVedioPlayerView()

@property (nonatomic, strong) AVPlayer *player;
@property (nonatomic, strong) AVPlayerItem *playerItem;
@property (nonatomic, strong) AVPlayerLayer *playerLayer;
@property (nonatomic, strong) NSURL *videoURL;
@property (nonatomic, assign) BOOL repeat;
@property (nonatomic, assign, getter=isBackgrounded) BOOL backgrounded;

@property (assign, nonatomic)BOOL isPlaying;//用来判断当前视频是否准备好播放。
@property (nonatomic, strong) UIImageView *playButton;

@end

@implementation IMYBabyMVVedioPlayerView

- (void)dealloc {
    [self removeObserver:self forKeyPath:kSVRPlayerItemStatus];
    [self finish];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (instancetype)initWith:(NSURL *)videoURL repeat:(BOOL)repeat {
    if (self = [super init]) {
        _videoURL = videoURL;
        _repeat = repeat;
        [self commonInit];
        [self addObserver];
        [self addNotification];
    }
    return self;
}

- (instancetype)initWithAsset:(AVAsset *)videoAsset repeat:(BOOL)repeat {
    if (self = [super init]) {
        if (videoAsset) {
            _playerItem = [[AVPlayerItem alloc] initWithAsset:videoAsset];
        }
        _repeat = repeat;
        [self commonInit];
        [self addObserver];
        [self addNotification];
    }
    return self;
}

- (instancetype)initWithPlayerItem:(AVPlayerItem *)item repeat:(BOOL)repeat {
    if (self = [super init]) {
        _playerItem = item;
        _repeat = repeat;
        [self commonInit];
        [self addObserver];
        [self addNotification];
    }
    return self;
}

- (void)commonInit {
    if (_playerItem == nil) {
        _playerItem = [[AVPlayerItem alloc] initWithURL:_videoURL];
    }
    _player = [AVPlayer playerWithPlayerItem:_playerItem];
    _playerLayer = [AVPlayerLayer playerLayerWithPlayer:_player];
    _playerLayer.frame = self.bounds;
    _player.actionAtItemEnd = AVPlayerActionAtItemEndPause;
    [self.layer addSublayer:_playerLayer];
    //对于1分钟以内的视频就每1/30秒刷新一次页面，大于1分钟的每秒20次就行
    CGFloat duration = CMTimeGetSeconds(_playerItem.asset.duration);
    CMTime interval = duration > 60 ? CMTimeMake(1, 20) : CMTimeMake(1, 30);
    @weakify(self);
    [_player addPeriodicTimeObserverForInterval:interval queue:dispatch_get_main_queue() usingBlock:^(CMTime time) {
        @strongify(self);
        CGFloat currentTime = CMTimeGetSeconds(time);
        if (self.progressBlock) {
            self.progressBlock(duration, currentTime);
        }
    }];
    [self setupPlayButton];
    
    [self bk_whenTapped:^{
        @strongify(self);
        if (self.isPlaying) {
            [self pause];
        } else {
            [self play];
        }
    }];
}

- (void)setFrame:(CGRect)frame {
    [super setFrame:frame];
    self.playerLayer.frame = self.bounds;
}

- (void)layoutSubviews {
    self.playerLayer.frame = self.bounds;
}

#pragma mark -
- (void)addNotification {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(playerItemDidReachEnd:)
                                                 name:AVPlayerItemDidPlayToEndTimeNotification
                                               object:self.playerItem];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
}

- (void)addObserver {
    [self addObserver:self forKeyPath:kSVRPlayerItemStatus options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld context:nil];
}


#pragma mark - action

- (void)play {
    [self.player play];
    [self.playButton setHidden:YES];
    self.isPlaying = YES;
}

- (void)finish {
    [self.player pause];
    self.player = nil;
    [self.playButton setHidden:NO];
    self.isPlaying = NO;

}

- (void)pause {
    [self.player pause];
    [self.playButton setHidden:NO];
    self.isPlaying = NO;
}

#pragma mark - notification

- (void)playerItemDidReachEnd:(NSNotification *)notification {
    if (self.playerItemDidReachEndHandler) {
        self.playerItemDidReachEndHandler();
    } else if (self.repeat) { //播放结束 。回到 0 暂停状态
        AVPlayerItem *p = [notification object];
        [p seekToTime:kCMTimeZero];
        [self pause];
    }
}

- (void)appWillEnterForeground:(id)sender {
    self.backgrounded = NO;
    if (!self.curViewController || [UIViewController imy_currentViewControlloer] == self.curViewController) {
        [self play];
    }
}

- (void)appDidEnterBackground:(id)sender {
    self.backgrounded = YES;
    [self pause];
}

#pragma mark - observe
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    if ([keyPath isEqualToString:kSVRPlayerItemStatus]) {
        AVPlayerItemStatus status = self.playerItem.status;
        if (self.playerItemDidStatus) {
            self.playerItemDidStatus(status);
        } else if (status == AVPlayerItemStatusReadyToPlay) {
            if ((!self.curViewController || [UIViewController imy_currentViewControlloer] == self.curViewController)
                && !self.isBackgrounded) {
                [self pause];
            }
        }
    }
}

#pragma mark - 播放按钮
- (void)setupPlayButton {
    self.playButton = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 52, 52)];
    [self.playButton imy_setImage:@"daduzhao_icon_play"];
    [self addSubview:self.playButton ];
    [self bringSubviewToFront:self.playButton];
    [self.playButton setHidden:YES];
    [self.playButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self);
        make.width.mas_equalTo(@(52));
        make.height.mas_equalTo(@(52));
    }];
}

@end
