//
//  IMYBabyMakeMVBubbleView.m
//  IMYTools
//
//  Created by ss on 2023/9/25.
//

#import "IMYBabyMakeMVBubbleView.h"

#import <IMYBaseKit/IMYBaseKit.h>

static CGFloat labelMinWidth = 72;
static CGFloat labelMaxWidth = 186;
static CGFloat labelMinHeight = 20;
static CGFloat labelMaxHeight = 20000;

@interface IMYBabyMakeMVBubbleView ()

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIImageView *arrowIcon;
@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, assign) UIEdgeInsets contentViewMargin;
@property (nonatomic, assign) CGPoint position;

@end

@implementation IMYBabyMakeMVBubbleView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
        
        [self bk_whenTapped:^{
            
        }];
        
        [self addObserver];
    }
    return self;
}

- (void)setupSubviews {
    self.translatesAutoresizingMaskIntoConstraints = NO;
    
    self.contentViewMargin = UIEdgeInsetsMake(10, 16, 10, 16);
    
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.titleLabel];
    [self addSubview:self.arrowIcon];
}

#pragma mark - Public


- (void)showWithContent:(NSString *)content
              superView:(UIView *)superView
               position:(CGPoint)position {
    if (self.superview) {
        return;
    }
    
    self.position = position;
    [superView addSubview:self];
    [self refreshContent:content];
    
    if (self.didShowBlock) {
        self.didShowBlock();
    }
    
    [self check2Hide];
}

- (void)dismiss {
    [self removeFromSuperview];
    
    if (self.didDismissBlock) {
        self.didDismissBlock();
    }
}

- (void)refreshContent:(NSString *)content {
    self.titleLabel.text = content;
    [self adjustLayout];
}

#pragma mark - Observer

- (void)addObserver {
    @weakify(self);
    NSString *notiName = IMYPublicBaseViewController.IMYViewControllerDidActiveChangedNotification;
    RACSignal *activeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:notiName object:nil];
    [[[activeSignal takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self check2Hide];
    }];
}

#pragma mark - Layout

- (void)adjustLayout {
    [self.titleLabel sizeToFit];
    
    // label
    {
        CGFloat labelX = self.contentViewMargin.left;
        CGFloat labelY = self.contentViewMargin.top;
        CGFloat labelWidth = labelMinWidth;
        CGFloat labelHeight = labelMinHeight;
        
        CGFloat calLabelWidth = [self.titleLabel.text imy_getDrawSizeWithFont:self.titleLabel.font maxSize:CGSizeMake(CGFLOAT_MAX, 1)].width;
        if (calLabelWidth >= labelMinWidth && calLabelWidth <= labelMaxWidth) {
            labelWidth = calLabelWidth;
        } else if (calLabelWidth > labelMaxWidth) {
            labelWidth = labelMaxWidth;
        }
        
        CGFloat calLabelHeight = [self.titleLabel.text imy_getDrawSizeWithFont:self.titleLabel.font maxSize:CGSizeMake(labelWidth, CGFLOAT_MAX)].height;
        if (calLabelHeight >= labelMinHeight && calLabelHeight <= labelMaxHeight) {
            labelHeight = calLabelHeight;
        } else if (calLabelHeight > labelMaxHeight) {
            labelHeight = labelMaxHeight;
        }
        
        self.titleLabel.frame = CGRectMake(labelX, labelY, labelWidth, labelHeight);
    }
    
    // contentView
    {
        CGFloat contentViewX = 0;
        CGFloat contentViewY = 0;
        CGFloat contentViewWidth = self.contentViewMargin.left + self.titleLabel.imy_width + self.contentViewMargin.right;
        CGFloat contentViewHeight = self.contentViewMargin.top + self.titleLabel.imy_height + self.contentViewMargin.bottom;
        self.contentView.frame = CGRectMake(contentViewX, contentViewY, contentViewWidth, contentViewHeight);
    }
    
    // icon
    {
        self.arrowIcon.imy_size = CGSizeMake(14, 8);
        self.arrowIcon.imy_top = self.contentView.imy_bottom -1;
        self.arrowIcon.imy_centerX = self.contentView.imy_centerX;
    }
    
    // self
    {
        self.frame = CGRectMake(0, 0, self.contentView.imy_width, self.contentView.imy_height + self.arrowIcon.imy_height);
        self.imy_bottom = self.position.y - 8;
        self.imy_centerX = self.position.x;
    }
    
    // 防止贴边
    {
        CGFloat overRight = self.imy_right - SCREEN_WIDTH;
        if (overRight > 0) {
            self.contentView.imy_left = self.contentView.imy_left - overRight - 12;
        }

        CGFloat overLeft = self.imy_left;
        if (overLeft < 0) {
            self.contentView.imy_left = self.contentView.imy_left - overLeft + 12;
        }
    }
}

#pragma mark - check2Hide

- (void)check2Hide {
    if (self && self.superview) {
        self.hidden = NO;
    }
}

- (BOOL)isNeedHide {
    UIViewController *viewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    if (![viewController isKindOfClass:[UITabBarController class]]) {
        return NO;
    }
    
    UITabBarController *tabBarController = (UITabBarController *)viewController;
    if (![tabBarController.selectedViewController isKindOfClass:[UINavigationController class]]) {
        return NO;
    }
    
    UINavigationController *nav = tabBarController.selectedViewController;
    if (nav.viewControllers.count == 1) {
        return NO;
    }
    
    return YES;
}

#pragma mark - Get

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        [_contentView imy_setBackgroundColorForKey:kCK_Red_A];
        [_contentView imy_drawAllCornerRadius:8];
    }
    return _contentView;
}

- (UIImageView *)arrowIcon {
    if (!_arrowIcon) {
        _arrowIcon = [[UIImageView alloc] init];
        [_arrowIcon imy_setImageForKey:@"baby_mv_bubble_arrow"];
    }
    return _arrowIcon;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:14];
        [_titleLabel imy_setTextColorForKey:kCK_White_A];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.numberOfLines = 0;

    }
    return _titleLabel;
}


@end
