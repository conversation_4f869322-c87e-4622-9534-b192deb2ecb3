//
//  IMYPAGPositionModel.h
//  IMYBaseKit
//
//  Created by meetyou on 2025/5/16.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYPAGPositionModel : NSObject
@property (nonatomic, assign) NSInteger index; ///< 索引
@property (nonatomic, assign) int64_t duration; ///< 视频时长 - 微秒级别
@property (nonatomic, assign, readonly) NSTimeInterval time; ///< 时间，秒
@property (nonatomic, strong) UIImage *image; ///< 图片资源
@property (nonatomic, copy) NSString *path; ///< 资源路径

+ (NSArray<IMYPAGPositionModel*> *)getModelsWithFilePath:(NSString *)filePath;

@end

NS_ASSUME_NONNULL_END
