//
//  IMYPAGPositionModel.m
//  IMYBaseKit
//
//  Created by meetyou on 2025/5/16.
//

#import "IMYPAGPositionModel.h"
#import <libpag/PAGPlayer.h>

@implementation IMYPAGPositionModel

- (NSTimeInterval)time {
    return self.duration/1000000.f;
}

+ (NSArray<IMYPAGPositionModel*> *)getModelsWithFilePath:(NSString*) filePath {
    PAGFile* pagFile = [PAGFile Load: filePath];
    if (!pagFile) {
        return @[];
    }
    NSMutableArray<IMYPAGPositionModel*> * models = [NSMutableArray new];
    
    NSArray<NSNumber*> * layerNumbers = [pagFile getEditableIndices:PAGLayerTypeImage];
    
    for (NSNumber * number in layerNumbers) {
        
        NSArray<PAGLayer*> * layers = [pagFile getLayersByEditableIndex:[number intValue] layerType:PAGLayerTypeImage];
        PAGLayer * layer = layers.firstObject;
        if ([layer isKindOfClass:[PAGImageLayer class]]) {
            PAGImageLayer * imageLayer = layer;
            int64_t time = [imageLayer contentDuration];
            IMYPAGPositionModel * model = [IMYPAGPositionModel new];
            model.index = [number integerValue];
            model.duration = time;
            
            [models addObject:model];
        }
    }
    return models;
}

@end
