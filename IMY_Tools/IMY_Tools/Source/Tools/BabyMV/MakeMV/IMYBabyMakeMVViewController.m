//
//  IMYBabyMakeMVViewController.m
//  IMYTools
//
//  Created by ss on 2023/9/21.
//


#import "IMYBabyMakeMVViewController.h"
#import <BBJViewKit/BBJViewKit.h>
#import "IMY_ViewKit.h"
#import "IMYBabyMakeMVViewController+Bottom.h"
#import "IMYBabyMakeMVShareVC.h"
#import "IMYBabyMakeWeekMVHelper.h"
#import "IMYBabyMakeWeekMVSaver.h"

@interface IMYBabyMakeMVViewController ()<UITabBarControllerDelegate, UICollectionViewDataSource, UICollectionViewDelegate>

@property (nonatomic, assign) BOOL saveNoEnableClick; ///< 是否不可点击
@property (nonatomic, copy) NSArray<NSString *> *pagFileNames; ///< pag文件名
@property (nonatomic, copy) NSArray<IMYPAGPositionModel*> *pagPositions; ///< 位置信息
@property (nonatomic, strong) NSMutableArray<NSString *> *replaceTexts; ///< 替换文本
@property (nonatomic, strong) IMYBabyMakeWeekMVSaver *mvSaver; ///< 保存器

@end

@implementation IMYBabyMakeMVViewController

- (void)dealloc {
    _playerView = nil;
    _pagPositions = nil;
    _mvPositionModels = nil;
    [self clearTempDir];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initView];
    [self reloadPagData];
}

- (void)initView {
    self.enablePopGestureStatus = NO;
    self.enableFullPopGestureRecognizer = NO;
    [self.view imy_setBackgroundColorForKey:kCK_Black_F];
    
    [self initNavTopRightButton];
    [self registerClass];
    [self.view addSubview:self.playerView];
    [self.view addSubview:self.bottomView];
    CGFloat scale = 720/1280.0;
    CGFloat playerViewT = 12;
    CGFloat playerViewB = 12;
    [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(playerViewT);
        make.bottom.mas_equalTo(self.bottomView.mas_top).mas_offset(-playerViewB);
        make.centerX.mas_equalTo(self.view);
        make.width.mas_equalTo(self.playerView.mas_height).multipliedBy(scale);
    }];
    CGFloat height = 90 + 20 * 2 + SCREEN_LAND_TABBAR_SAFEBOTTOM_MARGIN + 8;
    if (self.isSupportAiCaption) {
        height = 90 + 56 + SCREEN_LAND_TABBAR_SAFEBOTTOM_MARGIN + 4;
    }
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.mas_equalTo(self.view);
        make.height.mas_equalTo(height);
    }];
    [self createBubbleView];
}

- (void)initNavTopRightButton {
    [self.imy_topRightButton setTitle:IMYString(@"保存") forState:UIControlStateNormal];
    self.imy_topRightButton.titleLabel.font = [UIFont systemFontOfSize:15];
    self.imy_topRightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
    [self.imy_topRightButton imy_setTitleColor:kCK_White_A];
    [self.imy_topRightButton imy_setBackgroundColorForKey:kCK_Red_A];
    self.imy_topRightButton.layer.cornerRadius = 16;
    self.imy_topRightButton.frame = CGRectMake(-10, 6.5, 62, 32);
    [self.imy_topRightButton imy_addLanguageLayoutsChangeActionBlock:^(UIButton *weakObject) {
        if (IMYISRTL) {
            weakObject.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        } else {
            weakObject.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        }
    }];

    [self.imy_topRightButton addTarget:self action:@selector(saveAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)reloadPagData {
    // 替换图片
    [self updatePagData];
    [self.playerView replace:self.pagPositions];
    [self.playerView replaceText:self.replaceTexts];
    [self.collectionView reloadData];
}

- (void)reloadPagDataForReplace {
    [self reloadPagData];
    imy_asyncMainBlock(0.1, ^{
        [self.playerView pause];
    });
    self.playerView.isFirstDrag = YES;
}

#pragma mark - ExportVideo
/*
 点击导出按钮
 */
- (void)saveAction {
    [self biReportWithEventName:@"yy_mvzzy_bc" public_type:self.changeModel.tModel.tid];
    // 无网络，不响应点击事件
    if (!IMYNetState.networkEnable) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    if (self.saveNoEnableClick) {
        return;
    }
    self.saveNoEnableClick = YES;
    imy_asyncMainBlock(0.1, ^{
        self.saveNoEnableClick = NO;
    });

    [self.playerView pause];
    [self updatePagData];
    /// 开始导出数据
    PAGComposition *exportComposition = [self.playerView createComposition:self.pagPositions replaceImages:@[] andReplaceTexts:self.replaceTexts];
    self.mvSaver.baby_id = self.bbjBabyId;
    self.mvSaver.babyType = self.babyType;
    self.mvSaver.changeModel = self.changeModel;
    [self.mvSaver exportDataWithComposition:exportComposition pagName:self.changeModel.tModel.local_pag_file_path];
}

#pragma mark - 更新 PagPositions
- (void)updatePagData {
    NSMutableArray *pagPositions = [NSMutableArray new];
    NSMutableArray *materials = [NSMutableArray array];
    for (IMYBabyMVPositionModel *model in self.mvPositionModels) {
        [pagPositions addObject:model.position];
        
        if (self.captionSwitchView.switchView.on) {
            IMYBabyMVMixMaterialAssetModel *picturesModel = [IMYBabyMVMixMaterialAssetModel new];
            picturesModel.title = model.assetModel.aiCaptionTitle;
            picturesModel.gen_text = model.assetModel.aiCaptionContent;
            [materials addObject:picturesModel];
        }
    }
    self.pagPositions = pagPositions;
    self.replaceTexts = [self.changeModel displayTextsWithMaterials:materials];
}

- (void)updatePagPositions:(NSArray<IMYBabyMVPositionModel *> *)mvPositionModels {
    dispatch_group_t group = dispatch_group_create();
    for (IMYBabyMVPositionModel *model in mvPositionModels) {
        dispatch_group_enter(group);
        if (model.assetModel.isVideo) {
            if (model.assetModel.asset) {
                [self fetchLocalFileURLForAsset:model.assetModel.asset completion:^(NSString *path, AVURLAsset * assetUrl) {
                    model.position.path = path;
                    model.assetUrl = assetUrl;
                    dispatch_group_leave(group);
                }];
            } else {
                model.position.path = model.assetModel.videoModel.localVideoPath;
                if (model.assetModel.avAsset && [model.assetModel.avAsset isKindOfClass:[AVURLAsset class]]) {
                    model.assetUrl = model.assetModel.avAsset;
                }
            }
        } else {
            if (model.thumbImage) {
                model.position.image = model.thumbImage;
            } else {
                model.position.image = model.assetModel.thumb;
            }
            dispatch_group_leave(group);
        }
    }
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        [self reloadPagData];
    });
}

- (void)fetchLocalFileURLForAsset:(PHAsset *)asset completion:(void (^)(NSString *path, AVURLAsset * assetUrl))completion {
    if (!asset) {
        completion(nil, nil);
        return;
    }
    PHImageManager *manager = [PHImageManager defaultManager];
    // 使用 requestAVAsset 获取 AVAsset
    [manager requestAVAssetForVideo:asset options:nil resultHandler:^(AVAsset * _Nullable asset, AVAudioMix * _Nullable audioMix, NSDictionary * _Nullable info) {
        if ([asset isKindOfClass:[AVURLAsset class]]) {
            AVURLAsset *urlAsset = (AVURLAsset *)asset;
            NSURL *localURL = urlAsset.URL;
            if (completion) {
                completion(localURL.path, urlAsset);
            }
        } else {
            if (completion) {
                completion(nil, nil); // 没有找到本地文件 URL 或不支持的格式
            }
        }
    }];
}

#pragma mark - GA
/// Description
/// - Parameters:
///   - event: yy_xzmvzty_ljzzan：孕育_选择mv主题页_立即制作按钮   yy_xzmvzty_xzmb：孕育_选择mv主题页_选择模板
///   - info_type: 失败原因，仅失败上报，例如：手动取消、没有找到试纸、最终失败-原因等
///   public_type 额外参数：主题id，服务端下发
///   baby_id 宝宝id
- (void)biReportWithEventName:(NSString *)event public_type:(NSInteger)public_type{
    NSDictionary *params = @{@"event":event,
                             @"action":@(2),
                             @"public_type":@(public_type),
                             @"common_baby_id":@(self.common_baby_id),
                             @"baby_id":@(self.bbjBabyId)
                             };
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

#pragma mark - setter and getter
- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];
        [_bottomView imy_setBackgroundColorForKey:kCK_White_AN];
        [_bottomView addSubview:self.collectionView];
        
        [_bottomView addSubview:self.captionSwitchView];
        self.captionSwitchView.hidden = !self.isSupportAiCaption;
        CGFloat captionSwitchViewH = self.isSupportAiCaption ? 56 : 20;
        [self.captionSwitchView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.right.mas_equalTo(_bottomView);
            make.height.mas_equalTo(captionSwitchViewH);
        }];
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.right.mas_equalTo(_bottomView);
            make.top.equalTo(self.captionSwitchView.mas_bottom);
        }];
    } 
    return _bottomView;
}

- (IMYBabyMakeMVCaptionSwitchView *)captionSwitchView {
    if (!_captionSwitchView) {
        _captionSwitchView = [IMYBabyMakeMVCaptionSwitchView new];
        _captionSwitchView.hidden = YES;
        _captionSwitchView.switchView.on = [IMYBabyMakeWeekMVHelper isAiCaptionDisplayStatus];
        @weakify(self);
        _captionSwitchView.switchView.onDidStateChanged = ^(IMYSwitch *swi, BOOL isON) {
            @strongify(self);
            /// 保存状态
            [IMYBabyMakeWeekMVHelper setAiCaptionDisplayStatus:isON];
            /// 刷新数据
            [self reloadPagData];
            /// 埋点
            NSString *publicInfo = @"1";
            if (!isON) {
                publicInfo = @"2";
            }
            NSDictionary *params = @{@"event": @"yy_mvzzy_znpwkg", @"action": @(2), @"public_type": @(self.changeModel.tModel.tid), @"public_info": publicInfo, @"common_baby_id": @(self.common_baby_id), @"baby_id": @(self.bbjBabyId)};
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        };
    }
    return _captionSwitchView;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.itemSize = CGSizeMake(64, 90);
        layout.minimumLineSpacing = 12;
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumInteritemSpacing = 12;
        CGFloat sectionInsetB = SCREEN_LAND_TABBAR_SAFEBOTTOM_MARGIN + (self.isSupportAiCaption ? 4: 20);
        layout.sectionInset = UIEdgeInsetsMake(0, 12, sectionInsetB, 12);
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        [_collectionView imy_setBackgroundColorForKey:kIMYClearColorKey];
        _collectionView.dragInteractionEnabled = YES;
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.showsHorizontalScrollIndicator = NO;
    }
    return _collectionView;
}

- (IMYBabyMVPAGPlayerView *)playerView {
    if (!_playerView) {
        _playerView = [IMYBabyMVPAGPlayerView new];
        _playerView.userInteractionEnabled = YES;
        _playerView.layer.borderWidth = 1;
        [_playerView imy_addThemeChangedBlock:^(IMYBabyMVPAGPlayerView *weakObject) {
            weakObject.layer.borderColor = [UIColor imy_colorForKey:kCK_White_AN].CGColor;
        }];
        [_playerView imy_drawAllCornerRadius:12];
        _playerView.isReverseEditOrder = self.changeModel.tModel.isReverseEditOrder;
        [_playerView loadPAGAndPlay:self.changeModel.tModel.local_pag_file_path];
    }
    return _playerView;
}

- (void)setMvPositionModels:(NSArray<IMYBabyMVPositionModel *> *)mvPositionModels {
    [self updatePagPositions:mvPositionModels];
    _mvPositionModels = mvPositionModels;
}

- (IMYBabyMakeWeekMVSaver *)mvSaver {
    if (!_mvSaver) {
        _mvSaver = [IMYBabyMakeWeekMVSaver new];
        _mvSaver.fromURI = self.fromURI;
        @weakify(self);
        _mvSaver.nextBlock = ^(NSString * _Nonnull videoPath, NSInteger recordId, BOOL noCheckSaveVideo) {
            @strongify(self);
            /// 发布成功后。需要跳转到播放页面  MV已保存
            IMYBabyMakeMVShareVC *vc = [[IMYBabyMakeMVShareVC alloc] initWithVideoPath:videoPath babyId:self.bbjBabyId recordId:recordId];
            vc.common_baby_id = self.common_baby_id;
            vc.tid = self.changeModel.tModel.tid;
            vc.mv_type = self.mv_type;
            vc.noCheckSaveVideo = noCheckSaveVideo;
            vc.baby_follower = self.changeModel.baby_follower;
            [self imy_present:vc];
        };
    }
    return _mvSaver;
}

@end
