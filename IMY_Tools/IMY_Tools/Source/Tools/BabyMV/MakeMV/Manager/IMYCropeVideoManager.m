//
//  IMYCropeVideoManager.m
//  AFNetworking
//
//  Created by meetyou on 2025/5/27.
//

#import "IMYCropeVideoManager.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYCropeVideoManager

+ (void)cropeVideoWithAsset:(AVURLAsset*) asset
                     clipModel:(BBJVideoExportClipModel *) model
                 exportPath:(NSString*) exportPath
              completeBlock:(void (^)(BOOL success)) completeBlock {
    if (exportPath.length <= 0 || !asset) {
        completeBlock(NO);
        return;
    }
    @autoreleasepool {
        imy_asyncBlock(^{
            CMTime startTime = CMTimeMakeWithSeconds(model.startTime, 600); // 开始时间，例如10秒
            CMTime duration = CMTimeMakeWithSeconds(model.endTime-model.startTime, 600); // 持续时间，例如5秒
            CMTimeRange timeRange = CMTimeRangeMake(startTime, duration);
            AVAssetExportSession *exportSession = [[AVAssetExportSession alloc] initWithAsset:asset presetName:AVAssetExportPresetLowQuality];
            exportSession.timeRange = timeRange;
            exportSession.outputURL = [NSURL fileURLWithPath:exportPath];
            exportSession.outputFileType = AVFileTypeMPEG4;
            [exportSession exportAsynchronouslyWithCompletionHandler:^{
                switch (exportSession.status) {
                    case AVAssetExportSessionStatusCompleted:
    //                    NSLog(@"Export successful");
                        completeBlock(YES);
                        break;
                    case AVAssetExportSessionStatusFailed:
    //                    NSLog(@"Export failed: %@", exportSession.error);
                        completeBlock(NO);
                        break;
                    default:
                        completeBlock(NO);
                        break;
                }
            }];
        });
        
    }
}

+ (void)fileSize:(NSString*) path {
    NSFileManager * filemanager = [[NSFileManager alloc]init];
    BOOL isDirectory;
    if([filemanager fileExistsAtPath:path isDirectory:&isDirectory]){
         NSDictionary * attributes = [filemanager attributesOfItemAtPath:path error:nil];
         NSNumber *theFileSize;
        if (theFileSize = [attributes objectForKey:NSFileSize]) {
            NSLog(@"path: %@, size = %ld",path,[theFileSize intValue]);
        }
    }
}

/// 获取本地路径
/// - Parameters:
///   - asset: 资源
///   - completion: 回调
+ (void)fetchLocalFileURLForAsset:(PHAsset *)asset completion:(void (^)(NSString *path, AVURLAsset * assetUrl))completion {
    if (!asset) {
        !completion?:completion(nil, nil);
        return;
    }
    PHImageManager *manager = [PHImageManager defaultManager];
    // 使用 requestAVAsset 获取 AVAsset
    [manager requestAVAssetForVideo:asset options:nil resultHandler:^(AVAsset * _Nullable asset, AVAudioMix * _Nullable audioMix, NSDictionary * _Nullable info) {
        if ([asset isKindOfClass:[AVURLAsset class]]) {
            AVURLAsset *urlAsset = (AVURLAsset *)asset;
            NSURL *localURL = urlAsset.URL;
            if (completion) {
                completion(localURL.path, urlAsset);
            }
        } else {
            if (completion) {
                completion(nil, nil); // 没有找到本地文件 URL 或不支持的格式
            }
        }
    }];
}

/// 获取对应的本地路径
/// - Parameters:
///   - mvPositionModels: 模型数组
///   - completion: 完成回调
+ (void)updatePagPositions:(NSArray<IMYBabyMVPositionModel *> *) mvPositionModels
                completion:(void (^)(void))completion {
    dispatch_group_t group = dispatch_group_create();
    for (IMYBabyMVPositionModel * model in mvPositionModels) {
        dispatch_group_enter(group);
        if (model.assetModel.isVideo) {
            if (model.assetModel.asset) {
                [self fetchLocalFileURLForAsset:model.assetModel.asset completion:^(NSString *path, AVURLAsset * assetUrl) {
                    model.position.path = path;
                    model.assetUrl = assetUrl;
                    dispatch_group_leave(group);
                }];
            } else if (model.assetModel.videoModel.localVideoPath.length > 0) {
                model.position.path = model.assetModel.videoModel.localVideoPath;
                dispatch_group_leave(group);
            } else {
                dispatch_group_leave(group);
            }
        } else {
            if (model.thumbImage) {
                model.position.image = model.thumbImage;
            } else {
                model.position.image = model.assetModel.thumb;
            }
            dispatch_group_leave(group);
        }
    }
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        !completion?:completion();
    });
}

/// 临时存储MV文件
+ (NSString*)tempMVFilePathWithPath:(NSString *) path
                       tempDirPath:(NSString*) tempDirPath {
    if (path.length <= 0 || tempDirPath.length <= 0) {
        return nil;
    }
    
    NSString * dirPath = tempDirPath;
    if (![[NSFileManager defaultManager] fileExistsAtPath:dirPath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSURL * url = [[NSURL alloc] initFileURLWithPath:path];
    NSString *fileName = [url lastPathComponent];
    NSString * last = [[fileName split:@"."] lastObject];
    NSString * uuid = [[[NSUUID alloc] init] UUIDString];
    NSString *filePath = [NSString stringWithFormat:@"%@/%@.%@",dirPath,uuid,last];
    return filePath;
}


/// 根据资源信息进行视频裁剪，并存储至临时文件夹内
/// - Parameters:
///   - mvPositionModels: MV的位置信息及资源信息
///   - completeBlock: 完成回调
///   - mvTempDirPath: 临时文件夹
+ (void)cropVideoWithMVPositions:(NSArray<IMYBabyMVPositionModel*> *) mvPositionModels
                   mvTempDirPath:(NSString *) mvTempDirPath
                   progressBlock:(void (^)(NSInteger successNum, NSInteger failNum)) progressBlock
                   completeBlock:(void (^)(NSInteger success, NSInteger fail)) completeBlock {
    __block NSInteger successNum = 0;
    __block NSInteger failNum = 0;
    dispatch_group_t group = dispatch_group_create();
    
    for (IMYBabyMVPositionModel * model in mvPositionModels) {
        dispatch_group_enter(group);
        if (model.position.time >= model.assetModel.duration || !model.assetModel.isVideo || model.position == nil) {
            successNum ++;
            imy_asyncMainExecuteBlock(^{
                progressBlock(successNum, failNum);
            });
            dispatch_group_leave(group);
        } else {
            BBJVideoExportClipModel * clipModel = [BBJVideoExportClipModel new];
            clipModel.startTime = 0;
            clipModel.endTime = model.position.time;
            clipModel.duration = model.assetModel.duration;
            NSString * filePath = [self tempMVFilePathWithPath:model.position.path tempDirPath:mvTempDirPath];
            if (filePath.length <= 0) {
                filePath = [self tempMVFilePathWithPath:model.assetModel.videoModel.localVideoPath tempDirPath:mvTempDirPath];
            }
            AVURLAsset *assetUrl = model.assetUrl;
            if (!assetUrl) {
                if ([model.assetModel.avAsset isKindOfClass:[AVURLAsset class]]) {
                    assetUrl = model.assetModel.avAsset;
                }
            }
            [self cropeVideoWithAsset:model.assetUrl clipModel:clipModel exportPath:filePath completeBlock:^(BOOL success) {
                if (success) {
                    successNum ++;
                    model.position.path = filePath;
                } else {
                    failNum ++;
                }
                imy_asyncMainExecuteBlock(^{
                    progressBlock(successNum, failNum);
                });
                dispatch_group_leave(group);
            }];
        }
    }
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (completeBlock) {
            completeBlock(successNum, failNum);
        }
    });
}


/// 获取对应的资源数组
/// 根据MV模版的位置信息裁剪视频，并存储至临时文件内
/// - Parameters:
///   - assetImages: 图片
///   - assetModels: 资源模型
///   - completeBlock: 处理完成回调
///   - positions: MV的位置信息
///   - mvTempDirPath: 临时存储裁剪后的视频文件夹
+ (void)getMVPositionModelWithImages:(NSArray*) assetImages
                         assetModels:(NSArray*) assetModels
                           positions:(NSArray<IMYPAGPositionModel*> *) positions
                       mvTempDirPath:(NSString *) mvTempDirPath
                       progressBlock:(void (^)(NSInteger successNum, NSInteger failNum)) progressBlock
                       completeBlock:(void (^)(NSArray<IMYBabyMVPositionModel*> * mvPositions, NSInteger success, NSInteger fail)) completeBlock {
    NSMutableArray * mvPositionModels = [NSMutableArray new];
    
    /// 以有加载出来的图片数量为准
    for (int i = 0; i < assetImages.count; i ++) {
        IMYBabyMVPositionModel * mvPosition = [IMYBabyMVPositionModel new];
        if (assetImages.count > i) {
            mvPosition.thumbImage = assetImages[i];
        }
        if (assetModels.count > i) {
            mvPosition.assetModel = assetModels[i];
        }
        if (positions.count > i) {
            mvPosition.position = positions[i];
        }
        [mvPositionModels addObject:mvPosition];
    }
    
    // 更新本地资源文件路径
    [self updatePagPositions:mvPositionModels completion:^{
        [self cropVideoWithMVPositions:mvPositionModels mvTempDirPath:mvTempDirPath progressBlock:progressBlock completeBlock:^(NSInteger success, NSInteger fail) {
            if (completeBlock) {
                completeBlock(mvPositionModels, success, fail);
            }
        }];
    }];
    
}

@end
