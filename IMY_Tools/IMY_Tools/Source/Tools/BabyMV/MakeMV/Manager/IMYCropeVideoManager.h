//
//  IMYCropeVideoManager.h
//  AFNetworking
//
//  Created by meetyou on 2025/5/27.
//

#import <Foundation/Foundation.h>
#import <BBJViewKit/BBJVideoExportClipModel.h>
#import <AVFoundation/AVFoundation.h>
#import "IMYPAGPositionModel.h"
#import <Photos/Photos.h>
#import "IMYBabyMVPositionModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYCropeVideoManager : NSObject

/// 视频资源裁剪
/// - Parameters:
///   - asset: 视频资源
///   - model: 裁剪的数据
///   - exportPath: 导出路径
///   - completeBlock: 完成回调
+ (void)cropeVideoWithAsset:(AVURLAsset*) asset
                     clipModel:(BBJVideoExportClipModel *) model
                 exportPath:(NSString*) exportPath
              completeBlock:(void (^)(BOOL success)) completeBlock;

/// 获取对应的资源数组
/// 根据MV模版的位置信息裁剪视频，并存储至临时文件内
/// - Parameters:
///   - assetImages: 图片
///   - assetModels: 资源模型
///   - completeBlock: 处理完成回调
///   - positions: MV的位置信息
///   - mvTempDirPath: 临时存储裁剪后的视频文件夹
+ (void)getMVPositionModelWithImages:(NSArray*) assetImages
                         assetModels:(NSArray*) assetModels
                           positions:(NSArray<IMYPAGPositionModel*> *) positions
                       mvTempDirPath:(NSString *) mvTempDirPath
                       progressBlock:(void (^)(NSInteger successNum, NSInteger failNum)) progressBlock
                       completeBlock:(void (^)(NSArray<IMYBabyMVPositionModel*> * mvPositions, NSInteger success, NSInteger fail)) completeBlock;

@end

NS_ASSUME_NONNULL_END
