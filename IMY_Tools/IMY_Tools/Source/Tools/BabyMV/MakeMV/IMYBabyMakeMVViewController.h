//
//  IMYBabyMakeMVViewController.h
//  IMYTools
//
//  Created by ss on 2023/9/21.
//

#import "IMYPublicBaseViewController.h"
#import "IMYBabyMVPAGPlayerView.h"
#import "IMYBabyMakeMVBubbleView.h"
#import "IMYBabyMVTemplateListModel.h"
#import "IMYBabyMVPositionModel.h"
#import "IMYBabyMakeMVCaptionSwitchView.h"

@import IMYSwift;

NS_ASSUME_NONNULL_BEGIN
/**
 *  @page 制作宝宝MV Pag
 */
@interface IMYBabyMakeMVViewController : IMYPublicBaseViewController

@property (nonatomic, strong) IMYBabyMVPAGPlayerView *playerView;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) IMYBabyMakeMVCaptionSwitchView *captionSwitchView;
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) IMYTipsLabelView *tipsView;
@property (nonatomic, strong) UIImageView *dragImageView;

@property (nonatomic, strong) NSMutableArray<IMYBabyMVPositionModel*> *mvPositionModels;

@property (nonatomic, assign) NSInteger mv_type; ///<成长MV下发-成长MV类型，1-周mv
@property (nonatomic, assign) NSInteger cid; ///< 模板id
@property (nonatomic, strong) IMYBabyMVTemplateForMaterialModel *changeModel; ///< mv模板
@property (nonatomic, assign) BOOL isSupportAiCaption; ///< 是否支持AI字幕

@property (nonatomic, assign) NSInteger bbjBabyId;
@property (nonatomic, assign) NSInteger common_baby_id;
@property (nonatomic, assign) NSInteger babyType;

- (void)reloadPagDataForReplace;
- (void)reloadPagData;

- (void)biReportWithEventName:(NSString *)event public_type:(NSInteger)public_type;

@end

NS_ASSUME_NONNULL_END
