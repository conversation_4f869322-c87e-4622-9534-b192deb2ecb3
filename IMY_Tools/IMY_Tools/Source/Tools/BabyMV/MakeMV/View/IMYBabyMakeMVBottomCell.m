//
//  IMYBabyMakeMVBottomCell.m
//  IMYTools
//
//  Created by ss on 2023/9/22.
//

#import "IMYBabyMakeMVBottomCell.h"
#import "Masonry.h"
#import "IMY_ViewKit.h"

@interface IMYBabyMakeMVBottomCell()

@property (nonatomic, strong) UIView * bottomBgView;

@property (nonatomic, strong) IMYTouchEXButton * editBtn;

@property (nonatomic, strong) UILabel * timeLabel;

@property (nonatomic, strong) UIImageView * imageView;

@property (nonatomic, strong) UILabel * numberLabel;

@property (nonatomic, strong) IMYBabyMVPositionModel * model;

@property (nonatomic, strong) UILongPressGestureRecognizer * pangesture;

@end

@implementation IMYBabyMakeMVBottomCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self initView];
    }
    return self;
}

- (void)prepareForReuse {
    [super prepareForReuse];
    self.imageView.image = nil;
}

- (void)initView {
    [self.contentView addSubview:self.imageView];
    [self.contentView addSubview:self.numberLabel];
    [self.imageView addSubview:self.bottomBgView];
    [self.bottomBgView addSubview:self.timeLabel];
    [self.bottomBgView addSubview:self.editBtn];
    
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.contentView);
        make.height.equalTo(self.contentView.mas_width);
    }];
    
    [self.numberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.contentView);
        make.top.equalTo(self.imageView.mas_bottom);
    }];
    
    [self.bottomBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(self.imageView);
        make.height.mas_equalTo(32);
    }];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(6);
        make.bottom.mas_equalTo(-4);
    }];
    
    [self.editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.timeLabel);
        make.right.mas_equalTo(-6);
        make.size.mas_equalTo(CGSizeMake(12, 12));
    }];
    
    [self.contentView addGestureRecognizer:self.pangesture];
}

- (void)config:(IMYBabyMVPositionModel*) model number:(NSString*) number {
    self.model = model;
    self.numberLabel.text = number;
    self.imageView.image = model.thumbImage;
    NSString * time = @"";
    if (model.position.duration > 0) {
        time = [NSString stringWithFormat:@"%.1fs",(model.position.time)];
    }
    self.timeLabel.text = time;
    BOOL hidden = !model.assetModel.isVideo;
    // 素材时长小于空位转场时长，则不显示编辑按键
    if (model.assetModel.duration <= model.position.time) {
        hidden = YES;
    }
    self.editBtn.hidden = hidden;
}

- (void)pangestureAction:(UILongPressGestureRecognizer *) gesture {
    if (self.pangestureBlock) {
        self.pangestureBlock(gesture, self);
    }
}

#pragma mark - get

- (UILongPressGestureRecognizer *)pangesture {
    if (!_pangesture) {
        _pangesture = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(pangestureAction:)];
        _pangesture.minimumPressDuration = 0.5f;
    }
    return _pangesture;
}

- (UILabel *)numberLabel {
    if (!_numberLabel) {
        _numberLabel = [UILabel new];
        _numberLabel.font = [UIFont imy_regularWith:16];
        [_numberLabel imy_setTextColorForKey:kCK_Black_B];
        _numberLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _numberLabel;
}

- (UIImageView *)imageView {
    if(!_imageView) {
        _imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 64, 64)];
        _imageView.userInteractionEnabled = YES;
        [_imageView imy_drawAllCornerRadius:8];
        _imageView.contentMode = UIViewContentModeScaleAspectFill;
        [_imageView imy_setBackgroundColorForKey:kCK_Black_A];
    }
    return _imageView;
}

- (IMYTouchEXButton *)editBtn {
    if (!_editBtn) {
        _editBtn = [IMYTouchEXButton new];
        [_editBtn imy_setImage:@"make_mv_edit_icon"];
        @weakify(self)
        [[_editBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self)
            !self.editBlock?:self.editBlock(self.model);
        }];
        [_editBtn setExtendTouchAllValue:20];
    }
    return _editBtn;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        _timeLabel.font = [UIFont imy_mediumWith:13];
        _timeLabel.textAlignment = NSTextAlignmentLeft;
        [_timeLabel imy_setTextColorForKey:kCK_White_A];
    }
    return _timeLabel;
}

- (UIView *)bottomBgView {
    if (!_bottomBgView) {
        _bottomBgView = [UIView new];
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = CGRectMake(0, 0, 64, 32);
        
        // 设置颜色数组
        gradientLayer.colors = @[(id)[UIColor colorWithRed:0 green:0 blue:0 alpha:0].CGColor,
                                 (id)[UIColor colorWithRed:0 green:0 blue:0 alpha:0.4].CGColor];
        
        // 设置渐变方向（默认为垂直）
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        gradientLayer.locations = @[@0,@1];
        [_bottomBgView.layer addSublayer:gradientLayer];
    }
    return _bottomBgView;
}

@end
