//
//  IMYBabyMakeMVCaptionSwitchView.m
//  IMYTools
//
//  Created by h<PERSON><PERSON><PERSON><PERSON> on 2025/7/2.
//

#import "IMYBabyMakeMVCaptionSwitchView.h"

@interface IMYBabyMakeMVCaptionSwitchView ()

@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;

@end

@implementation IMYBabyMakeMVCaptionSwitchView

- (instancetype)initWithFrame:(CGRect)frame{
    if (self = [super initWithFrame:frame]) {
        [self prepareUI];
    }
    return self;
}

#pragma mark - private
- (void)prepareUI{
    [self imy_setBackgroundColorForKey:kCK_Clear_A];
    [self addSubview:self.iconImageView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.switchView];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self);
        make.right.mas_equalTo(self.titleLabel.mas_left).offset(-4);
    }];
    CGFloat titleLabelM = 8;
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self);
        make.right.mas_equalTo(self.switchView.mas_left).offset(-titleLabelM);
    }];
    CGFloat switchViewW = 40;
    CGFloat switchViewH = 24;
    CGFloat switchViewM = 12;
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-switchViewM);
        make.centerY.mas_equalTo(self);
        make.size.mas_equalTo(CGSizeMake(switchViewW, switchViewH));
    }];
}

#pragma mark - setter or getter
- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [UIImageView new];
        _iconImageView.image = [UIImage imy_imageForKey:@"mvzz_edit_icon_ai"];
    }
    return _iconImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont imy_regularWith:15];
        [_titleLabel imy_setTextColorForKey:kCK_Black_A];
        _titleLabel.text = IMYString(@"智能配文");
    }
    return _titleLabel;
}

- (IMYSwitch *)switchView {
    if (!_switchView) {
        _switchView = [[IMYSwitch alloc] initWithFrame:CGRectMake(0, 0, 40, 24)];
    }
    return _switchView;
}

@end
