//
//  IMYBabyMakeMVShareVC.h
//  IMYTools
//
//  Created by ss on 2023/9/25.
//

#import <Foundation/Foundation.h>

#import "IMYVKCoolViewController.h"
#import "BBJBabyRecordCellModel.h"
#import "BBJVideoPlayerModel.h"
#import "BBJVideoPlayBIModel.h"
#import "IMYBabyMVTemplateListModel.h"

NS_ASSUME_NONNULL_BEGIN
/**
 *  @page 宝宝MV视频播放
 */
@interface IMYBabyMakeMVShareVC : IMYVKCoolViewController

@property (nonatomic, copy) NSString *babyName;  ///< 宝宝名
@property (nonatomic, assign) BOOL noCheckSaveVideo; ///< 是否不校验保存视频
@property (nonatomic, assign) NSInteger tid; ///< 模板id
@property (nonatomic, assign) NSInteger cid; ///< 主题id
@property (nonatomic, assign) NSInteger mv_type; ///< 成长MV下发-成长MV类型，1-周mv
@property (nonatomic, strong) IMYBabyMVTemplateBabyFollowerModel *baby_follower; ///< 成长MV下发-宝宝信息
@property (nonatomic, assign) NSInteger common_baby_id; /// 美柚宝宝id

- (instancetype)initWithVideoPath:(NSString *)videoPath babyId:(NSInteger)babyId recordId:(NSInteger)record;

@end

NS_ASSUME_NONNULL_END
