//
//  IMYBabyMakeMVViewController+Bottom.m
//  IMYTools
//
//  Created by ss on 2023/9/22.
//

#import "IMYBabyMakeMVViewController+Bottom.h"
#import "IMYBabyMakeMVBottomCell.h"
#import "IMY_ViewKit.h"
#import "IMYTabBarBubbleView.h"
#import <BBJBabyHome/BBJVideoCropViewController.h>
#import <BBJViewKit/BBJViewKit.h>

#import "IMYCropeVideoManager.h"
#import <BBJBabyHome/BBJPhotoPickerViewController.h>

@import IMYSwift;

@interface IMYBabyMakeMVViewController(Bottom) <UICollectionViewDataSource, UICollectionViewDelegate>

@end

@implementation IMYBabyMakeMVViewController(Bottom)

- (NSString *)mvTempDirPath {
    return kMVTempDirPath;
}

/// 删除临时文件夹
- (void)clearTempDir {
    NSURL *url = [NSURL fileURLWithPath:[self mvTempDirPath]];
    [[NSFileManager defaultManager] removeItemAtURL:url error:nil];
}

/// 临时存储MV文件
- (NSString*)temMVFilePathWithPath:(NSString *) path {
    if (path.length <= 0) {
        return nil;
    }
    
    NSString *dirPath = [self mvTempDirPath];
    if (![[NSFileManager defaultManager] fileExistsAtPath:dirPath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSURL *url = [[NSURL alloc] initFileURLWithPath:path];
    NSString *fileName = [url lastPathComponent];
    NSString *last = [[fileName split:@"."] lastObject];
    NSString *uuid = [[[NSUUID alloc] init] UUIDString];
    NSString *filePath = [NSString stringWithFormat:@"%@/%@.%@",dirPath,uuid,last];
    return filePath;
}

#pragma mark - UICollectionViewDataSource, UICollectionViewDelegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.mvPositionModels.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    IMYBabyMakeMVBottomCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"IMYBabyMakeMVBottomCell"
                                                                              forIndexPath:indexPath];
    [cell imy_drawAllCornerRadius:8];
    IMYBabyMVPositionModel *model = [self.mvPositionModels objectAtIndex:indexPath.row];
    
    NSString * number = [NSString stringWithFormat:@"%d", indexPath.row+1];
    NSLog(@"cellForItemAtIndexPath %@",number);
    [cell config:model number:number];
    @weakify(self)
    cell.pangestureBlock = ^(UILongPressGestureRecognizer * _Nonnull recognizer, IMYBabyMakeMVBottomCell * cell) {
        @strongify(self)
        [self handlelongGesture:recognizer cell: cell];
    };
    
    cell.editBlock = ^(IMYBabyMVPositionModel * _Nonnull model) {
        @strongify(self)
        [self biReportWithEventName:@"yy_mvzzy_spcj" public_type:self.changeModel.tModel.tid];
        // TODO: 跳转视频编辑页面
        if (!model.assetModel.videoModel) {
            model.assetModel.videoModel = [BBJVideoModel videoModelWithAsset:model.assetModel.asset babyId:self.bbjBabyId];
        }
        BBJVideoCropViewController *vc = [[BBJVideoCropViewController alloc] initWithAssetModel:@[model.assetModel] babyId:self.bbjBabyId];
        vc.allowDelete = NO;
        vc.fixedTime = model.position.time;
        vc.fixedTimeEnable = YES;
        @weakify(vc,self)
        vc.completionBlk = ^(BOOL isDelete) {
            @strongify(vc,self)
            [vc imy_pop:YES];
            [self biReportWithEventName:@"yy_mvzzy_spcjwc" public_type:self.changeModel.tModel.tid];
            NSString * exportPath = [self temMVFilePathWithPath:model.position.path];
            if (exportPath.length <= 0) {
                return;
            }
            
            [self.playerView pause];
            BBJVideoExportClipModel * clipModel = [BBJVideoExportClipModel new];
            clipModel.startTime = model.assetModel.videoModel.operate.startTime;
            clipModel.endTime = model.assetModel.videoModel.operate.endTime;
            clipModel.duration = model.assetModel.duration;
            
            [UIView imy_showLoadingHUD];
            @weakify(self)
            [IMYCropeVideoManager cropeVideoWithAsset:model.assetUrl clipModel:clipModel exportPath:exportPath completeBlock:^(BOOL success) {
                imy_asyncMainBlock(^{
                    @strongify(self)
                    [UIView imy_hideHUD];
                    if (success) {
                        if (model.assetModel.videoModel.coverImage) {
                            model.thumbImage = model.assetModel.videoModel.coverImage;
                        }
                        model.position.path = exportPath;
                        [self reloadPagDataForReplace];
                    } else {
                        [UIView imy_showTextHUD:@"裁剪失败~"];
                    }
                });
            }];
        };
        [self imy_push:vc];
    };
    return cell;
}

- (void)handlelongGesture:(UILongPressGestureRecognizer *)gesture cell:(IMYBabyMakeMVBottomCell *)cell {
    UIImageView * imageView = cell.imageView;
    CGRect frame = [imageView convertRect:imageView.bounds toView:self.view];
    CGPoint centerPoint = CGPointMake(frame.origin.x + frame.size.width/2, frame.origin.y+frame.size.height/2);
    frame = CGRectMake(frame.origin.x - frame.size.width*0.1/2, frame.origin.y - frame.size.height*0.1/2, frame.size.width*1.1, frame.size.height*1.1);
    switch (gesture.state) {
        case UIGestureRecognizerStateBegan:
        {
            self.dragImageView = [UIImageView new];
            self.dragImageView.contentMode = UIViewContentModeScaleAspectFill;
            self.dragImageView.image = imageView.image;
            self.dragImageView.frame = frame;
            self.dragImageView.layer.cornerRadius = 8;
            self.dragImageView.clipsToBounds = YES;
            [self.view addSubview:self.dragImageView];
            cell.hidden = YES;
            NSIndexPath *indexPath = [self.collectionView indexPathForCell:cell];
            [self.collectionView beginInteractiveMovementForItemAtIndexPath: indexPath];
        }
            break;
        case UIGestureRecognizerStateChanged:
            if (!self.dragImageView) {
                return;
            }
            
            self.dragImageView.center = centerPoint;
            [self.collectionView updateInteractiveMovementTargetPosition:[gesture locationInView:self.collectionView]];
            break;
        case UIGestureRecognizerStateEnded:
            self.dragImageView.hidden = YES;
            cell.hidden = NO;
            [self.dragImageView removeFromSuperview];
            self.dragImageView = nil;
            //移动结束后关闭cell移动
            [self.collectionView endInteractiveMovement];
            break;
        default:
            self.dragImageView.hidden = YES;
            cell.hidden = NO;
            [self.dragImageView removeFromSuperview];
            self.dragImageView = nil;
            [self.collectionView cancelInteractiveMovement];
            break;
    }
}

- (void)collectionView:(UICollectionView *)collectionView moveItemAtIndexPath:(NSIndexPath *)sourceIndexPath toIndexPath:(NSIndexPath*)destinationIndexPath {
    //移除数据插入到新的位置
    [self hiddleBubble];
    [self swapItems:sourceIndexPath toIndexPath:destinationIndexPath];
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSLog(@"%zd", indexPath.row);
    if (collectionView == self.collectionView) {
        [self hiddleBubble];
    }
}

- (void)swapItems:(NSIndexPath*) fromIndexPath toIndexPath:(NSIndexPath*) toIndexPath {
    id obj = [self.mvPositionModels objectAtIndex:fromIndexPath.row];
    [self.mvPositionModels removeObjectAtIndex:fromIndexPath.row];
    [self.mvPositionModels insertObject:obj atIndex:toIndexPath.row];
    
    imy_asyncMainBlock(0.4, ^{
        [self.collectionView reloadData];
    });
    [self reloadPagDataForReplace];
    [self biReportWithEventName:@"yy_mvzzy_tzsx" public_type:self.changeModel.tModel.tid];
}

- (BOOL)collectionView:(UICollectionView *)collectionView canMoveItemAtIndexPath:(NSIndexPath *)indexPath{
    return YES;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView == self.collectionView) {
        [self hiddleBubble];
    }
}

- (void)registerClass {
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    [self.collectionView registerClass:IMYBabyMakeMVBottomCell.class forCellWithReuseIdentifier:@"IMYBabyMakeMVBottomCell"];
}

#pragma mark - 气泡
- (BOOL)exitVideo {
    for (IMYBabyMVPositionModel * model in self.mvPositionModels) {
        if ([model.assetModel isVideo]) {
            return YES;
        }
    }
    return NO;
}

- (void)createBubbleView {
    if (self.mvPositionModels.count <= 1 || [self isShowBubble]) {
        return;
    }
    BOOL exitVideo = [self exitVideo];
    NSString *tips = @"长按拖动调整顺序";
    if (exitVideo) {
        tips = @"长按拖动调整顺序，点击裁剪视频";
    }
    [self saveBubble];
    
    [self.view layoutIfNeeded];
    IMYTipsLabelView *tipsView = [IMYTipsLabelView showOn:self.collectionView
                                                      tips:tips
                                               enableBgBtn:YES
                                             contentHeight:45
                                         leftAndRightSpace:12
                                               onViewSpace:4
                                                      font:[UIFont imy_regularWith:15]
                                                 textColor:[UIColor imy_colorForKey:kCK_White_A]
                                                   bgColor:[[UIColor blackColor] colorWithAlphaComponent:0.6]];
    [tipsView.tipsLabel imy_setTextColorForKey:kCK_White_A];
    [tipsView.contentView imy_addThemeChangedBlock:^(UIView * weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.backgroundColor = [UIColor imy_colorWithHexString:@"#FF4D88"];
        } else {
            weakObject.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        }
    }];
    [tipsView.arrowLayer imy_addThemeChangedBlock:^(CAShapeLayer *weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.fillColor = [UIColor imy_colorWithHexString:@"#FF4D88"].CGColor;
        } else {
            weakObject.fillColor = [[UIColor blackColor] colorWithAlphaComponent:0.6].CGColor;
        }
    }];
    @weakify(self)
    tipsView.dismissBlock = ^{
        @strongify(self)
        self.tipsView = nil;
    };
    self.tipsView = tipsView;
}

- (void)hiddleBubble {
    [self.tipsView dismiss];
    self.tipsView = nil;
}

/// 是否展示气泡
- (BOOL)isShowBubble {
    NSString * isVideo = [self exitVideo] ? @"1" : @"0";
    NSString *key = [NSString stringWithFormat:@"IMYBabyMakeMVViewController_bubble_%@_%@", [IMYPublicAppHelper shareAppHelper].userid, isVideo];
    NSNumber *object = [[IMYUserDefaults standardUserDefaults] objectForKey:key];
    return [object boolValue];
}

- (void)saveBubble {
    NSString * isVideo = [self exitVideo] ? @"1" : @"0";
    NSString *key = [NSString stringWithFormat:@"IMYBabyMakeMVViewController_bubble_%@_%@", [IMYPublicAppHelper shareAppHelper].userid, isVideo];
    [[IMYUserDefaults standardUserDefaults] setObject:@(YES) forKey:key];
    [[IMYUserDefaults standardUserDefaults] synchronize];
}

@end
