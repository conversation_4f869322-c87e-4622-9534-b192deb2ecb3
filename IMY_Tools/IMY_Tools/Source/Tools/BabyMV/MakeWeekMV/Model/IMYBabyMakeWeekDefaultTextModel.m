//
//  IMYBabyMakeWeekDefaultTextModel.m
//  IMYTools
//
//  Created by huang<PERSON><PERSON><PERSON> on 2025/7/1.
//

#import "IMYBabyMakeWeekDefaultTextModel.h"
#import <IMYBaseKit/IMYPublic.h>

@interface IMYBabyMakeWeekDefaultTextModel ()

@property (nonatomic, assign) NSInteger index; ///< 索引
@property (nonatomic, copy) NSString *content; ///< 内容

@end

@implementation IMYBabyMakeWeekDefaultTextModel

+ (LKDBHelper *)getUsingLKDBHelper {
    static LKDBHelper *helper;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        NSString *dbFile = @"ToolsBabyMv.db";
        NSString *dBFileName = @"ToolsBabyMv";
        NSString *dbPath = [LKDBUtils getPathForDocuments:dbFile inDir:@"db"];
        if (![[NSFileManager defaultManager] fileExistsAtPath:dbPath]) {
            NSString *sourcePath = [[NSBundle mainBundle] pathForResource:dBFileName ofType:@"db"];
            [[NSFileManager defaultManager] copyItemAtPath:sourcePath toPath:dbPath error:nil];
        }
        helper = [[LKDBHelper alloc] initWithDBName:dBFileName];
    });

    return helper;
}

+ (NSString *)getPrimaryKey {
    return @"index";
}

+ (NSString *)getTableName {
    return @"ToolsBabyMvText";
}

/// 随机获取一条文案
+ (NSString *)getDefaultRandomText {
    NSInteger maxCount = 212;
    NSInteger index = arc4random() % maxCount + 1;
    NSString *where = [NSString stringWithFormat:@"\"index\" = %d", index];
    IMYBabyMakeWeekDefaultTextModel *model = [IMYBabyMakeWeekDefaultTextModel searchSingleWithWhere:where orderBy:nil];
    return model.content;
}

@end
