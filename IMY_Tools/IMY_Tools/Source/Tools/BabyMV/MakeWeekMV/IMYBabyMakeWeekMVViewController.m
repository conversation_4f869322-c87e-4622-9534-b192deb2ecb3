//
//  IMYBabyMakeWeekMVViewController.m
//  IMYTools
//
//  Created by ss on 2023/10/31.
//

#import "IMYBabyMakeWeekMVViewController.h"
#import "IMY_ViewKit.h"
#import "BBJPhotoPickerConfig.h"
#import "IMYBabyMakeMVShareVC.h"
#import "IMYBabyMVPAGPlayerView.h"
#import "IMYBabyMakeWeekMVViewModel.h"
#import "IMYRecordPregnancyBabyManager.h"
#import "IMYBabyMVTemplateViewModel.h"
#import "IMYBabyMakeMVViewController.h"
#import "BBJBabyCacheManager.h"
#import "IMYBabyMVTemplateContainerVC.h"
#import "IMYBabyMakeWeekMVHelper.h"
#import "IMYBabyMakeWeekDefaultTextModel.h"
#import "IMYBabyMakeWeekMVSaver.h"

@interface IMYBabyMakeWeekMVViewController ()<UITabBarControllerDelegate>

@property (nonatomic, strong) IMYButton *editBtn; ///< 编辑按钮
@property (nonatomic, strong) IMYButton *saveBtn; ///< 保存按钮
@property (nonatomic, strong) IMYButton *aiCaptionButton; ///< AI字幕按钮
@property (nonatomic, strong) IMYButton *backButton; ///< 返回按钮

@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) UIImageView *captionLoadingView;
@property (nonatomic, strong) UIView *shareView;

@property (nonatomic, strong) IMYBabyMakeWeekMVViewModel *viewModel; ///< viewModel
@property (nonatomic, copy) NSArray<NSString *> *pagFileNames; ///<  pag文件名
@property (nonatomic, copy) NSArray *lastSelectedModels; ///< 最后一次选择的模型
@property (nonatomic, copy) NSArray<IMYPAGPositionModel *> *pagPositions; ///< 位置信息
@property (nonatomic, strong) IMYBabyMakeWeekMVSaver *mvSaver; ///< 保存器
 
@end

@implementation IMYBabyMakeWeekMVViewController

- (void)dealloc {
    _playerView = nil;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self prepareData];
    [self initView];
    [self requestData];
}

- (void)initView {
    self.enablePopGestureStatus = NO;
    self.enableFullPopGestureRecognizer = NO;
    [self.view imy_setBackgroundColor:[UIColor blackColor]];
    
    [self.view addSubview:self.playerView];
    [self.playerView addSubview:self.captionLoadingView];
    [self.captionLoadingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    [self createBottomView];
    self.aiCaptionButton.hidden = !self.viewModel.isSupportAiCaption;
    [self.view addSubview:self.aiCaptionButton];
    [self.aiCaptionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.playerView.mas_bottom).mas_offset(-8);
        make.right.mas_equalTo(-8);
        make.height.mas_equalTo(16);
    }];
    [self setupNavigationBar];
}

- (void)prepareData {
    /// 重置开关状态
    [IMYBabyMakeWeekMVHelper resetAiCaptionDisplayStatus];
    if (self.common_babyId <= 0 && self.baby_id <= 0) {
        self.common_babyId = [IMYBabyMVTemplateViewModel getCurrCommonBabyId];
        @weakify(self);
        [[BBJBabyCacheManager shareInstance] convertBBJBabyIdFromCacheDataCommonBabyId:self.common_babyId completion:^(id _Nullable resData, NSError * _Nullable error) {
            @strongify(self);
            NSInteger babyId = [resData integerValue];
            self.baby_id = babyId;
        }];
    } else if (self.common_babyId <= 0) {
        self.common_babyId = [[BBJBabyCacheManager shareInstance] getCommonBabyIdWithBBJBabyId:self.baby_id];
    }
}

- (void)setupNavigationBar {
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;

    UIView *leftMask = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_HEIGHT + 14 + 22 + 12)];
    leftMask.userInteractionEnabled = NO;
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    id color1 = (__bridge id)[[UIColor blackColor] colorWithAlphaComponent:0.4].CGColor;
    id color2 = (__bridge id)[[UIColor blackColor] colorWithAlphaComponent:0].CGColor;
    gradientLayer.colors = @[color1, color2];
    gradientLayer.locations =  @[@0.0, @1.0];
    gradientLayer.startPoint = CGPointMake(0.5, 0.0);
    gradientLayer.endPoint = CGPointMake(0.5, 1);
    gradientLayer.frame = leftMask.bounds;
    [leftMask.layer addSublayer:gradientLayer];
    [self.view addSubview:leftMask];
    [self.view addSubview:self.backButton];

    [leftMask mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.right.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.backButton.mas_bottom).mas_offset(-12);
    }];
    
    [self.view addSubview:self.backButton];
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).mas_offset(16);
        make.width.height.mas_equalTo(22);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(SCREEN_STATUSBAR_HEIGHT + 14);
    }];
}

- (void)createBottomView {
    UIView *shareBgView = [[UIView alloc] init];
    [shareBgView setHidden:YES];
    shareBgView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    [self.view addSubview:shareBgView];
    [shareBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.mas_equalTo(self.view);
        make.height.mas_equalTo(64 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
    }];
    [shareBgView addSubview:self.editBtn];
    [shareBgView addSubview:self.saveBtn];
    self.shareView = shareBgView;
    
    [self.editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(shareBgView).mas_offset(12);
        make.right.mas_equalTo(self.saveBtn.mas_left).mas_offset(-12);
        make.height.mas_equalTo(40);
        make.width.mas_equalTo((SCREEN_WIDTH - 36)/2.0);
    }];
    [self.saveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(shareBgView).mas_offset(12);
        make.left.mas_equalTo(self.editBtn.mas_right).mas_offset(12);
        make.right.mas_equalTo(shareBgView).mas_offset(-12);
        make.height.mas_equalTo(40);
        make.width.mas_equalTo((SCREEN_WIDTH - 36)/2.0);
    }];
}

- (void)reloadPagData {
    // 替换图片
    @weakify(self);
    [self.playerView loadPAGAndPlay:self.pagName completeBlock:^(BOOL bSuccess) {
        @strongify(self);
        [UIView animateWithDuration:0.1 animations:^{
            self.captionLoadingView.alpha = 0;
        }];
    }];
    [self.playerView replaceText:self.replaceTexts];
    [self.playerView replace:self.pagPositions];
    if (self.viewModel.changeModel.baby_follower.can_record || self.viewModel.changeModel.baby_follower.can_share) {
        [self.shareView setHidden:NO];
        //都有权限
        if (self.viewModel.changeModel.baby_follower.can_record && self.viewModel.changeModel.baby_follower.can_share) {
            
        } else if (!self.viewModel.changeModel.baby_follower.can_record) {//没有编辑权限
            [self.editBtn setHidden:YES];

            [self.saveBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.shareView).mas_offset(12);
                make.left.mas_equalTo(self.shareView.mas_left).mas_offset(12);
                make.right.mas_equalTo(self.shareView.mas_right).mas_offset(-12);
                make.height.mas_equalTo(40);
            }];

        } else if (!self.viewModel.changeModel.baby_follower.can_share) {//没有保存权限
            [self.shareView setHidden:YES];
        }
    }
}

#pragma mark - request

- (void)requestData {
    @weakify(self);
    [self.captionLoadingView setHidden:NO];
    [self.captionView setHidden:YES];
    self.captionLoadingView.alpha = 1;
    [self.viewModel requestDataWithBabyId:self.baby_id 
                                  mv_type:self.mv_type
                                    mv_id:self.mv_id
                               period_uni:self.period_uni
                 completeWithRequestBlock:^(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel * _Nonnull model) {
        @strongify(self);
        [self.captionLoadingView imy_setImageURL:model.tModel.first_cover];
    }
        completeWithRequestResourcesBlock:^(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel * _Nonnull model) {
        @strongify(self);
        if (bSuccess) {
            self.pagName = model.tModel.local_pag_file_path;
            self.playerView.isReverseEditOrder = model.tModel.isReverseEditOrder;
            self.replaceTexts = [model displayTextList:self.viewModel.isSupportAiCaption];
            [self updatePositionModelsWithModel:model];
            [self reloadPagData];
            [self biReportWithEventName:@"yy_bbjmvbfy_bjmvan" action:1];
            [self biReportWithEventName:@"yy_bbjmvbfy_bcmvan" action:1];
            self.lastSelectedModels = [self.viewModel getMaterialAssets];
        } else {
            [self.captionView setHidden:NO];
            [self.captionView setTitle: IMYNetState.networkEnable ? @"加载失败，请点击重新加载试试" : @"网络不见了，请检查网络"
                              forState:IMYCaptionViewStateRetry];
            self.captionView.state = IMYCaptionViewStateRetry ;
            self.captionLoadingView.alpha = 0;
        }
    }];
}

- (void)updatePositionModelsWithModel:(IMYBabyMVTemplateForMaterialModel*) model {
    NSString * pagFilePath = model.tModel.local_pag_file_path;
    NSMutableArray * positions = [[NSMutableArray alloc] initWithArray:[IMYPAGPositionModel getModelsWithFilePath:pagFilePath]];
    NSInteger index = 0;
    for (IMYBabyMVMixMaterialAssetModel * asset in model.materialsMix.medias) {
        if (index >= positions.count) {
            break;
        }
        IMYPAGPositionModel * positionModel = positions[index];
        if (asset.type == 1) {
            positionModel.image = asset.image;
            if (!positionModel.image) {
                positionModel.image = asset.coverImage;
            }
        } else {
            positionModel.duration = asset.duration * 1000000;
            positionModel.path = asset.localFilePath;
        }
        index ++;
    }
    self.pagPositions = positions;
}

#pragma mark - ExportVideo

/*
 点击保存按钮
 */
- (void)saveAction {
    /// 无网络，不响应点击事件
    if (!IMYNetState.networkEnable || self.pagPositions.count <= 0) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    /// 埋点
    [self biReportWithEventName:@"yy_bbjmvbfy_bcmvan" action:2];
    /// 暂停播放
    [self.playerView pause];
    /// 开始导出数据
    PAGComposition *exportComposition = [self.playerView createComposition:self.pagPositions replaceImages:@[] andReplaceTexts:self.replaceTexts];
    self.mvSaver.baby_id = self.baby_id;
    self.mvSaver.babyType = self.babyType;
    self.mvSaver.changeModel = self.viewModel.changeModel;
    [self.mvSaver exportDataWithComposition:exportComposition pagName:self.pagName];
}

#pragma mark - editAction
- (void)editAction {
    // 无网络，不响应点击事件
    if (!IMYNetState.networkEnable || self.pagPositions.count <= 0) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    if (![self handleTurnToLogin]) {
        return;
    }
    [self biReportWithEventName:@"yy_bbjmvbfy_bjmvan" action:2];
    [UIWindow imy_showLoadingHUD];
    [self makePagMVWithDownloadPag_step1];
}

- (void)makePagMVWithDownloadPag_step1 {
    [self makePagMVWithGetBabyId_step2];
}

/// 获取相关的 babyid
- (void)makePagMVWithGetBabyId_step2 {
    //怀孕宝宝
    if ([IMYRecordPregnancyBabyManager sharedInstance].bbj_baby_id == self.baby_id) {
        [self gotoMakeMVVCWithBBJId_step3:self.baby_id andBabyType:2];
        [UIWindow imy_hideHUD];
        return;
    }
    if (self.baby_id > 0) {
        [self gotoMakeMVVCWithBBJId_step3:self.baby_id andBabyType:3];
    } else if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
           //数据异常 。怀孕身份胎宝宝
        [self gotoMakeMVVCWithBBJId_step3:[IMYRecordPregnancyBabyManager sharedInstance].bbj_baby_id andBabyType:2];
    } else {
        [UIWindow imy_showTextHUD:@"数据异常，请重试"];
    }
}
/// 获取相关的 babyid
///< babyType 宝宝类型，1=备孕，2=怀孕，3=出生
- (void)gotoMakeMVVCWithBBJId_step3:(NSInteger)BBJId andBabyType:(NSInteger)babyType {
    @weakify(self);
    [IMYBabyMVTemplateViewModel requestBabyPicCountDataWithBabyId:BBJId completeBlock:^(BOOL bSuccess, NSInteger picCount, NSString * _Nonnull header, NSString * _Nonnull nickname, NSString *birthday) {
        @strongify(self);
        [UIWindow imy_hideHUD];
        NSInteger picType = 0;
        if (picCount < self.viewModel.changeModel.materialsMix.medias.count) {
            picType = 1;
        }
        NSMutableDictionary *dict = [NSMutableDictionary new];
        [dict imy_setNonNilObject:self.viewModel.changeModel.tModel.file_url forKey:@"fileUrl"];
        [dict imy_setNonNilObject:self.viewModel.changeModel.tModel.local_pag_file_path forKey:@"localFilePath"];
        
        BOOL mvEnableSelectVideo = self.viewModel.changeModel.tModel.support_video == 1;
        if (mvEnableSelectVideo) {
            [dict imy_setNonNilObject:@(BBJPhotoSelectedTypeAll) forKey:@"selectedType"];
            [dict imy_setNonNilObject:@(BBJPhotoShowTypeAll) forKey:@"showType"];
            [dict imy_setNonNilObject:@(mvEnableSelectVideo) forKey:@"mvEnableSelectVideo"];
        }
        
        [dict imy_setNonNilObject:@(self.viewModel.changeModel.tModel.material_num) forKey:@"picNumMin"];
        [dict imy_setNonNilObject:@(self.viewModel.changeModel.tModel.material_num) forKey:@"picNumMax"];
        [dict imy_setNonNilObject:@(picType) forKey:@"picType"];
        [dict imy_setNonNilObject:@(BBJId) forKey:@"babyId"];
        [dict imy_setNonNilObject:header forKey:@"header"];
        [dict imy_setNonNilObject:nickname forKey:@"nickname"];
        [dict imy_setNonNilObject:@(self.viewModel.changeModel.tModel.tid) forKey:@"tid"];
        [dict imy_setNonNilObject:self.lastSelectedModels forKey:@"lastSelectedModels"];
        IMYURI *uri = [IMYURI uriWithPath:@"tools/selectPhotoForMV" params:dict info:nil];
        
        IMYURIActionBlockObject *actionObj = [IMYURIActionBlockObject actionBlockWithURI:uri];
        @weakify(self);
        actionObj.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
           @strongify(self);
           imy_asyncMainBlock(^{
               [UIWindow imy_hideHUD];
               if ([result isKindOfClass:[NSArray class]]) {
                   NSMutableArray *imageArr = [[NSMutableArray alloc] initWithArray:result];
                   IMYBabyMakeMVViewController *vc = [IMYBabyMakeMVViewController new];
                   vc.fromURI = self.fromURI;
                   vc.mv_type = self.mv_type;
                   vc.common_baby_id = self.common_babyId;
                   vc.bbjBabyId = BBJId;
                   vc.babyType = babyType;
                   if (self.viewModel.isSupportAiCaption) {
                       for (IMYBabyMVPositionModel *model in imageArr) {
                           if (imy_isBlankString(model.assetModel.aiCaptionContent)) {
                               for (IMYBabyMVMixMaterialAssetModel *mediaModel in self.viewModel.changeModel.materialsMix.medias) {
                                   NSString *url = model.assetModel.picturePath;
                                   if (model.assetModel.videoModel) {
                                       url = model.assetModel.videoModel.url;
                                   }
                                   if ([mediaModel.url isEqualToString:url]) {
                                       model.assetModel.aiCaptionContent = mediaModel.gen_text;
                                   }
                               }
                               if (imy_isBlankString(model.assetModel.aiCaptionContent)) {
                                   model.assetModel.aiCaptionContent = [IMYBabyMakeWeekDefaultTextModel getDefaultRandomText];
                               }
                           }
                           if (imy_isBlankString(model.assetModel.aiCaptionTitle)) {
                               if (model.assetModel.creationDate) {
                                   model.assetModel.aiCaptionTitle = [model.assetModel.creationDate bbj_mvAgeTextWithBirthday:[birthday imy_getOnlyDate]];
                               }
                           }
                       }
                   }
                   vc.mvPositionModels = imageArr;
                   vc.changeModel = self.viewModel.changeModel;
                   vc.isSupportAiCaption = self.viewModel.isSupportAiCaption;
                   [[self imy_currentShowViewController] imy_push:vc];
                   NSLog(@"-收到图片数据");
               }else if ([result isKindOfClass:[NSDictionary class]]){
                   NSDictionary *dict = (NSDictionary *)result;
                   BOOL isClose = [dict objectForKey:@"isClose"];
                   if (isClose) {
                       NSArray *lastSelectedModels = [dict objectForKey:@"lastSelectedModels"];
                       self.lastSelectedModels = lastSelectedModels;
                   }
               }
           });
       };
       [[IMYURIManager shareURIManager] runActionWithActionObject:actionObj completed:nil];
    }];
}

- (BOOL)handleTurnToLogin {
    if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
        return YES;
    }
    @weakify(self);
    NSString *userid = [IMYPublicAppHelper shareAppHelper].userid;
    void(^finishBlock)(void) = ^{
        @strongify(self);
        // 注册用户相同
        if ([userid isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
            [[UIViewController imy_currentViewControlloer] dismissViewControllerAnimated:YES completion:^{
                @strongify(self);
                [self makePagMVWithDownloadPag_step1];
            }];
        } else {
            [self dismissViewControllerAnimated:NO completion:^{
                [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                [[IMYURIManager shareURIManager] runActionWithString:@"home"];
            }];
            
            UIViewController *currentVc = [UIViewController imy_currentViewControlloer];
            Class cls = NSClassFromString(@"IMYBabyMVTemplateContainerVC");
            UIViewController *bottomVC = [self findDismissVcWithClass:cls fromVc:currentVc];
            if (bottomVC) {
                [bottomVC dismissViewControllerAnimated:YES completion:^{
                    [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                    [[IMYURIManager shareURIManager] runActionWithString:@"home"];
                }];
                
                return ;
            }
        }
    };
    IMYURI *uri = [IMYURI uriWithPath:@"login" params:@{@"finishedBlock": finishBlock} info:nil];
    [[IMYURIManager shareURIManager] runActionWithURI:uri];
    return NO;
}

- (UIViewController *)findDismissVcWithClass:(Class)cls fromVc:(UIViewController *)vc {
    UIViewController *parentVC = vc;
    UIViewController *bottomVC = nil;
    while (parentVC) {
        BOOL hasPhotoSelectVc = NO;
        if ([parentVC isKindOfClass:[UINavigationController class]]) {
            UINavigationController *nav = (UINavigationController *)parentVC;
            hasPhotoSelectVc = [nav.viewControllers bk_match:^BOOL(UIViewController *obj) {
                return [obj isKindOfClass:cls];
            }];
        }else{
            hasPhotoSelectVc = [parentVC isKindOfClass:cls];
        }
        
        if (hasPhotoSelectVc) {
            bottomVC = parentVC.presentingViewController;
            break;
        }
        parentVC = parentVC.presentingViewController;
    }
    return bottomVC;
}

#pragma mark - private
- (void)closeAction {
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - GA
/// Description
/// - Parameters:
///   - event: yy_xzmvzty_ljzzan：孕育_选择mv主题页_立即制作按钮   yy_xzmvzty_xzmb：孕育_选择mv主题页_选择模板
///   - info_type: 失败原因，仅失败上报，例如：手动取消、没有找到试纸、最终失败-原因等
///   public_type 额外参数：主题id，服务端下发
///   baby_id 宝宝id
- (void)biReportWithEventName:(NSString *)event  action:(NSInteger)action {
    NSDictionary *params = @{@"event":event,
                             @"action":@(action),
                             @"public_type":@(self.viewModel.changeModel.tModel.tid),
                             @"baby_id":@(self.baby_id)
                             };
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

#pragma mark - setter and getter
- (IMYBabyMVPAGPlayerView *)playerView {
    if (!_playerView) {
        CGFloat scale = 16/9.0;
        CGFloat width = SCREEN_WIDTH;
        CGFloat height = width * scale;
        CGFloat offsetY = SCREEN_STATUSBAR_HEIGHT;
        if (height >= SCREEN_HEIGHT) {
            offsetY = 0;
        }
        _playerView = [[IMYBabyMVPAGPlayerView alloc] initWithFrame:CGRectMake(0, offsetY, width, height)];
        _playerView.userInteractionEnabled = YES;
    }
    return _playerView;
}

- (IMYButton *)editBtn {
   if (!_editBtn) {
       IMYButton *btn = [[IMYButton alloc] init];
       [btn imy_setImage:@"icon_baby_mv_public" highl:@"icon_baby_mv_public"];
       [btn imy_setTitle:@"编辑MV"];
       btn.imageAtDirection = IMYDirectionLeft;
       btn.titleLabel.font = [UIFont imy_mediumWith:16];
       btn.offset = 4;
       btn.iconOffset = 4;
       [btn imy_setBackgroundColorForKey:@"#B266FF"];
       [btn imy_drawAllCornerRadius:20];
       @weakify(self);
       [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
           @strongify(self);
           [self editAction];
       }];
       _editBtn = btn;
   }
   return _editBtn;
}

- (IMYButton *)saveBtn {
   if (!_saveBtn) {
       IMYButton *btn = [[IMYButton alloc] init];
       [btn imy_setImage:@"icon_baby_mv_save" highl:@"icon_baby_mv_save"];
       [btn imy_setTitle:@"保存MV"];
       btn.imageAtDirection = IMYDirectionLeft;
       btn.titleLabel.font = [UIFont imy_mediumWith:16];
       btn.offset = 4;
       btn.iconOffset = 14;
       [btn imy_setBackgroundColorForKey:kCK_Red_B];
       [btn imy_drawAllCornerRadius:20];
       @weakify(self);
       [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
           @strongify(self);
           [self saveAction];
       }];
       _saveBtn = btn;
   }
   return _saveBtn;
}

- (IMYButton *)aiCaptionButton {
   if (!_aiCaptionButton) {
       IMYButton *btn = [[IMYButton alloc] init];
       btn.userInteractionEnabled = NO;
       [btn imy_setImage:@"icon_baby_mv_mark" highl:@"icon_baby_mv_mark"];
       [btn imy_setTitle:@"配文由AI生成(内测中)"];
       [btn imy_setTitleColor:kCK_Black_B];
       btn.imageAtDirection = IMYDirectionLeft;
       btn.titleAtDirection = IMYDirectionRight;
       btn.titleLabel.font = [UIFont imy_regularWith:11];
       btn.offset = 2;
       [btn imy_setBackgroundColorForKey:kIMYClearColorKey];
       _aiCaptionButton = btn;
   }
   return _aiCaptionButton;
}

- (IMYTouchEXButton *)backButton {
    if (!_backButton) {
        IMYTouchEXButton *button = [[IMYTouchEXButton alloc] init];
        [button imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
        [button setExtendTouchInsets:UIEdgeInsetsMake(10, 16, 10, 4)];
        [button imy_setImage:@"bbj_all_navibar_icon_close_white"];
        [button setExtendTouchAllValue:20];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            UIViewController *controller = self;
            while(controller.presentingViewController != nil){
               controller = controller.presentingViewController;
            }
            [controller dismissViewControllerAnimated:NO completion:^{
                
            }];
        }];
        _backButton = button;
    }
    return _backButton;
}

- (IMYCaptionView *)captionView {
    if (!_captionView) {
        _captionView = [IMYCaptionView addToView:self.playerView show:YES];
        _captionView.state = IMYCaptionViewStateLoading;
        [_captionView imy_setBackgroundColorForKey:kCK_Black_F];
        [_captionView.imageView setHidden:YES];
        @weakify(self);
        _captionView.retryBlock = ^() {
            @strongify(self);
            [self dismissViewControllerAnimated:NO completion:^{
                [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                [[IMYURIManager shareURIManager] runActionWithString:@"tools/baby/makeMV"];
            }];
        };
    }
    return _captionView;
}
- (UIImageView *)captionLoadingView {
    if (!_captionLoadingView) {
        _captionLoadingView = [[UIImageView alloc] initWithFrame:self.playerView.bounds];
        [_captionLoadingView imy_setPlaceholderImage:[UIImage imageWithColor:[UIColor imy_colorForKey:kCK_Black_FN] ]];
        [_captionLoadingView imy_setBackgroundColorForKey:kCK_Black_F];
        UIActivityIndicatorView *indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        [_captionLoadingView addSubview:indicatorView];
        [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_equalTo(self.captionLoadingView);
            make.width.height.mas_equalTo(26);
        }];
        [indicatorView startAnimating];
        [_captionLoadingView setHidden:YES];
    }
    return _captionLoadingView;
}

- (IMYBabyMakeWeekMVViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [IMYBabyMakeWeekMVViewModel new];
        _viewModel.isSupportAiCaption = ((self.mv_type == 1) && [IMYBabyMakeWeekMVHelper mvAiCaptionSwitch]);
    }
    return _viewModel;
}

- (IMYBabyMakeWeekMVSaver *)mvSaver {
    if (!_mvSaver) {
        _mvSaver = [IMYBabyMakeWeekMVSaver new];
        _mvSaver.fromURI = self.fromURI;
        _mvSaver.isPublishPostNotification = YES;
        @weakify(self);
        _mvSaver.nextBlock = ^(NSString * _Nonnull videoPath, NSInteger recordId, BOOL noCheckSaveVideo) {
            @strongify(self);
            if (noCheckSaveVideo) {
                [self imy_pop:YES];
            } else {
                /// 发布成功后。需要跳转到播放页面  MV已保存
                IMYBabyMakeMVShareVC *vc = [[IMYBabyMakeMVShareVC alloc] initWithVideoPath:videoPath babyId:self.baby_id recordId:recordId];
                vc.common_baby_id = self.common_babyId;
                vc.tid = self.tid;
                vc.mv_type = self.mv_type;
                vc.baby_follower = self.viewModel.changeModel.baby_follower;
                [self imy_present:vc];
            }
        };
    }
    return _mvSaver;
}

@end
