//
//  IMYBabyMakeWeekMV.m
//  IMYTools
//
//  Created by ss on 2023/11/2.
//

#import "IMYBabyMakeWeekMVViewModel.h"
#import <IMYBaseKit/IMYPublic.h>
#import "IMYPAGView.h"
#import "BBJDownloadManager.h"


@implementation IMYBabyMakeWeekMVViewModel

IMY_KYLIN_FUNC_IDLE_ASYNC {
    //服务端要求的接口请求
    imy_asyncBlock(5, ^{
        if (![ [IMYBabyMakeWeekMVViewModel getCurrentAppVersion] isEqualToString:APPVersion]) {
            [[IMYServerRequest getPath:@"app/open" host:api_bbj_meiyou_com params:nil headers:nil] subscribeNext:^(id<IMYHTTPResponse> x) {
                [IMYBabyMakeWeekMVViewModel setCurrentAppVersion:APPVersion];
            } error:^(NSError *error) {
            }];
        }
        //切换用户需要再次请求下 app/open
        [[[IMYPublicAppHelper shareAppHelper].useridChangedSignal skip:1] subscribeNext:^(id x) { //skip:1] 打开 app 就会赋值一次，所有需要 跳过第一次
            [[IMYServerRequest getPath:@"app/open" host:api_bbj_meiyou_com params:nil headers:nil] subscribeNext:^(id<IMYHTTPResponse> x) {
                [IMYBabyMakeWeekMVViewModel setCurrentAppVersion:APPVersion];
            } error:^(NSError *error) {
            }];
        }];
    });
}

/// 获取图片素材的url
- (NSArray<BBJAssetModel*> *)getMaterialAssets {
    NSMutableArray * array = [NSMutableArray new];
    for (NSInteger index = 0; index < self.changeModel.materialsMix.medias.count; index ++) {
        IMYBabyMVMixMaterialAssetModel *model = [self.changeModel.materialsMix.medias imy_objectAtIndex:index];
        BBJAssetModel *assetModel = [BBJAssetModel new];
        assetModel.aiCaptionTitle = model.title;
        assetModel.aiCaptionContent = model.gen_text;
        if (model.type == 1) {
            assetModel.picturePath = model.url;
        } else {
            BBJVideoModel *videoModel = [BBJVideoModel new];
            videoModel.url = model.url;
            videoModel.coverUrl = model.cover;
            videoModel.duration = model.duration;
            assetModel.duration = model.duration;
            assetModel.videoModel = videoModel;
            assetModel.picturePath = model.cover;
        }
        assetModel.isSelected = YES;
        [array addObject:assetModel];
    }
    return array;
}

#pragma mark - 请求数据

/// 请求模板数据

- (void)requestDataWithBabyId:(NSInteger) babyId
                         mvId:(NSInteger) mvId
                 commonBabyId:(NSInteger) commonBabyId
                completeWithRequestBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeWithRequestBlock
                completeWithRequestResourcesBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeWithRequestResourcesBlock {
    @weakify(self);
    NSString *path = [NSString stringWithFormat:@"v3/mv_make/template_material?baby_id=%ld&mv_id=%ld&common_baby_id=%ld",babyId,mvId,commonBabyId];
    
    [[IMYServerRequest getPath:path host:api_bbj_meiyou_com params:nil headers:nil] subscribeNext:^(id<IMYHTTPResponse> x) {
        @strongify(self);

        imy_asyncMainBlock(^{
            NSDictionary *dic = x.responseObject;
            IMYBabyMVTemplateForMaterialModel *changeModel = [dic toModel:[IMYBabyMVTemplateForMaterialModel class]];
            !completeWithRequestBlock?:completeWithRequestBlock(YES, changeModel);
            [self prepareForAsset:changeModel completeBlock:completeWithRequestResourcesBlock];
        });
    } error:^(NSError *error) {
        imy_asyncMainBlock(^{
            !completeWithRequestResourcesBlock?:completeWithRequestResourcesBlock(NO, self.changeModel);
        });

    }];
}


/// 请求模板数据
/// - Parameter completeBlock: <#completeBlock description#>

- (void)requestDataWithBabyId:(NSInteger)babyId
                      mv_type:(NSInteger)mv_type
                        mv_id:(NSInteger)mv_id
                   period_uni:(NSString *)period_uni
                completeWithRequestBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeWithRequestBlock
                completeWithRequestResourcesBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeWithRequestResourcesBlock {
    @weakify(self);
    NSString *path = [NSString stringWithFormat:@"v3/mv_make/material?period_uni=%@&mv_type=%ld&baby_id=%ld&mv_id=%ld",period_uni,mv_type,babyId,mv_id];
    [[IMYServerRequest getPath:path host:api_bbj_meiyou_com params:nil headers:nil] subscribeNext:^(id<IMYHTTPResponse> x) {
        @strongify(self);

        imy_asyncMainBlock(^{
            NSDictionary *dic = x.responseObject;
            IMYBabyMVTemplateForMaterialModel *changeModel = [dic toModel:[IMYBabyMVTemplateForMaterialModel class]];
            !completeWithRequestBlock?:completeWithRequestBlock(YES, changeModel);
            [self prepareForAsset:changeModel completeBlock:completeWithRequestResourcesBlock];
        });
    } error:^(NSError *error) {
        imy_asyncMainBlock(^{
            !completeWithRequestResourcesBlock?:completeWithRequestResourcesBlock(NO, self.changeModel);
        });

    }];
}

- (void)preparePagFileWithUrl:(NSString *) url completeBlock:(void (^)(BOOL bSuccess, NSString *filePath))completeBlock {
    if (url.length <= 0) {
        !completeBlock?:completeBlock(NO,nil);
        return;
    }
    
    NSURL *pageUrl = [NSURL URLWithString:url];
    if (!pageUrl) {
        !completeBlock?:completeBlock(NO,nil);
        return;
    }
    @weakify(self);
    [IMYPAGView downloadPAGFileWithURL:pageUrl completionHandler: completeBlock];
}

- (void)downloadAssetWith:(IMYBabyMVTemplateForMaterialModel*) model
            completeBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeBlock {
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    __block NSInteger successNum = 0;
    __block NSInteger failNum = 0;
    NSMutableArray * imageArray = [NSMutableArray new];
    
    for (IMYBabyMVMixMaterialAssetModel *asset in model.materialsMix.medias) {
        dispatch_group_enter(group);
        NSString * imageUrl = asset.url;
        // 图片资源
        if (asset.type == 1) {
            [self downloadImageWithUrl:asset.url completeBlock:^(BOOL success, UIImage *image) {
                if (success && image) {
                    successNum ++;
                    asset.image = image;
                } else {
                    failNum ++;
                }
                dispatch_group_leave(group);
            }];
        } else if (asset.type == 2) {
            // 视频资源，先下封面，再下资源
            [self downloadImageWithUrl:asset.cover completeBlock:^(BOOL success, UIImage *image) {
                // 封面下载失败，直接失败
                if (!image || !success) {
                    failNum ++;
                    dispatch_group_leave(group);
                    return;
                }
                asset.coverImage = image;
                /// 下载视频
                [self downLoadVideoWithUrl:asset.url completeBlock:^(BOOL isSuccess, NSString *filePath) {
                    if (!success) {
                        failNum ++;
                        dispatch_group_leave(group);
                        return;
                    }
                    successNum ++;
                    asset.localFilePath = filePath;
                    dispatch_group_leave(group);
                }];
            }];
        } else {
            failNum ++;
            dispatch_group_leave(group);
        }
    }
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (successNum == model.materialsMix.medias.count) {
            completeBlock(YES,model);
        } else {
            completeBlock(NO,model);
        }
    });
}

- (void)prepareForAsset:(IMYBabyMVTemplateForMaterialModel*) model
          completeBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeBlock {
    [self preparePagFileWithUrl:model.tModel.file_url completeBlock:^(BOOL bSuccess, NSString *filePath) {
        if (bSuccess) {
            model.tModel.local_pag_file_path = filePath;
            @weakify(self)
            [self downloadAssetWith:model completeBlock:^(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model) {
                @strongify(self)
                self.changeModel = model;
                !completeBlock?:completeBlock(bSuccess,model);
            }];
        } else {
            !completeBlock?:completeBlock(NO,model);
        }
    }];
}

- (void)downloadImageWithUrl:(NSString *) imageUrl completeBlock:(void (^)(BOOL success, UIImage *image))completeBlock {
    if (imageUrl.length <= 0) {
        !completeBlock?:completeBlock(NO,nil);
        return;
    }
    
    UIImage *image = [[[SDWebImageManager sharedManager] imageCache] imageFromMemoryCacheForKey:imageUrl];
    if (!image) {
        image = [[[SDWebImageManager sharedManager] imageCache] imageFromDiskCacheForKey:imageUrl];
    }
    if (image) {
        !completeBlock?:completeBlock(YES,image);
        return;
    }
    
    NSURL *url = [NSURL URLWithString:imageUrl];
    [SDWebImageDownloader sharedDownloader].downloadTimeout = 30;
    @weakify(self);
    [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:url
                                                          options:SDWebImageDownloaderHighPriority | SDWebImageDownloaderContinueInBackground
                                                         progress:nil
                                                        completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
                                                            @strongify(self);
                                                            if (image && finished) {
                                                                [[SDImageCache sharedImageCache] storeImage:image recalculateFromImage:NO imageData:data forKey:imageUrl toDisk:YES];
                                                                !completeBlock?:completeBlock(YES,image);
                                                            } else {
                                                                !completeBlock?:completeBlock(NO,nil);
                                                            }
                                                        }];
}

- (void)downLoadVideoWithUrl:(NSString *) videoUrl
                        completeBlock:(void (^)(BOOL isSuccess, NSString * filePath))completeBlock {
    NSString * path = [BBJDownloadManager convertPathWithUrl: videoUrl];
    
    if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
        !completeBlock?:completeBlock(YES,path);
        return;
    }
    
    @weakify(self);
    [[BBJDownloadManager manager] downloadTaskWithUrlString:videoUrl targetPath:path progress:nil completionHandler:^(NSURLResponse * _Nonnull response, id  _Nonnull responseObject, NSError * _Nonnull error) {
        @strongify(self)
        if (error) {
            !completeBlock?:completeBlock(NO, nil);
        } else {
            !completeBlock?:completeBlock(YES,path);
        }
    }];
}

- (void)downloadPagURLString:(NSString *)pagURLString
                  retryCount:(NSInteger)retryCount
                       model:(IMYBabyMVTemplateForMaterialModel *)model
                       group:(dispatch_group_t)group
                       queue:(dispatch_queue_t)queue {
    ///最大重试两次
    if (retryCount >= 2) {
        dispatch_group_leave(group);
        return;
    }
    NSURL *url = [NSURL URLWithString:pagURLString];
    if (url) {
        @weakify(self);
        [IMYPAGView downloadPAGFileWithURL:url completionHandler:^(BOOL success, NSString * _Nonnull filePath) {
            @strongify(self);
            if (!success) {
                dispatch_async(queue, ^{
                    [self downloadPagURLString:pagURLString retryCount:retryCount + 1 model:model group:group queue:queue];
                });
            } else {
                model.tModel.local_pag_file_path = filePath;
                dispatch_group_leave(group);
            }
        }];
    }
}

+ (NSString *)getCurrentAppVersion {
    return [[IMYUserDefaults standardUserDefaults] objectForKey:[[self class] currentAppKey]];
}
+ (void)setCurrentAppVersion:(NSString *)appVersion {
    [[IMYUserDefaults standardUserDefaults] setObject:appVersion forKey:[[self class] currentAppKey]];
    [[IMYUserDefaults standardUserDefaults] synchronize];
}
+ (NSString *)currentAppKey {
    return [NSString stringWithFormat:@"IMYBabyMakeWeekMVViewModel_appVersion_%@",[IMYPublicAppHelper shareAppHelper].userid];
}
@end
