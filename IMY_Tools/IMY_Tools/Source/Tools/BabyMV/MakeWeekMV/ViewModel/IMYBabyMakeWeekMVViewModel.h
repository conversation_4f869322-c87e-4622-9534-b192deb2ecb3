//
//  IMYBabyMakeWeekMV.h
//  IMYTools
//
//  Created by ss on 2023/11/2.
//

#import <Foundation/Foundation.h>
#import "IMYBabyMVTemplateListModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYBabyMakeWeekMVViewModel : NSObject
@property (nonatomic, assign) NSInteger mv_type; ///<成长MV下发-成长MV类型，1-周mv
@property (nonatomic, assign) NSInteger period_uni; ///< 周期唯一标识
@property (nonatomic, assign) BOOL isSupportAiCaption; ///< 是否支持AI字幕
/// 获取mv模板
@property (nonatomic, strong) IMYBabyMVTemplateForMaterialModel *changeModel;

- (void)requestDataWithBabyId:(NSInteger) babyId
                        mvId:(NSInteger) mvId
               commonBabyId:(NSInteger) commonBabyId
                completeWithRequestBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model)) completeWithRequestBlock
        completeWithRequestResourcesBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model)) completeWithRequestResourcesBlock;


- (void)requestDataWithBabyId:(NSInteger)babyId
                      mv_type:(NSInteger)mv_type
                        mv_id:(NSInteger)mv_id
                   period_uni:(NSString *)period_uni
                completeWithRequestBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeBlock
                completeWithRequestResourcesBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateForMaterialModel *model))completeBlock ;


/// 获取图片素材的 BBJAssetModel 对象
- (NSArray<BBJAssetModel*> *)getMaterialAssets;


@end

NS_ASSUME_NONNULL_END
