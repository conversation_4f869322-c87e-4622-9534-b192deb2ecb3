//
//  IMYBabyMakeWeekMVViewController.h
//  IMYTools
//
//  Created by ss on 2023/11/1.
//

#import "IMYPublicBaseViewController.h"
#import "IMYBabyMVPAGPlayerView.h"
#import "IMYBabyMakeMVBubbleView.h"
#import "IMYBabyMVPositionModel.h"
NS_ASSUME_NONNULL_BEGIN
/**
 *  @page 制作宝宝周年MV Pag
 */
@interface IMYBabyMakeWeekMVViewController : IMYPublicBaseViewController

@property (nonatomic, copy) NSString *pagName;
@property (nonatomic, strong) IMYBabyMVPAGPlayerView *playerView;
@property (nonatomic, strong) NSMutableArray<NSString *> *replaceTexts;
@property (nonatomic, assign) NSInteger tid;//模板id
@property (nonatomic, assign) NSInteger cid;//主题id

@property (nonatomic, assign) NSInteger common_babyId;//美柚id
@property (nonatomic, assign) NSInteger baby_id;//宝宝记id
@property (nonatomic, assign) NSInteger babyType;
@property (nonatomic, assign) NSInteger mv_type; ///<成长MV下发-成长MV类型，1-周mv
@property (nonatomic, assign) NSInteger mv_id;//
@property (nonatomic, copy) NSString *period_uni;//

- (void)biReportWithEventName:(NSString *)event public_type:(NSInteger)public_type;

@end

NS_ASSUME_NONNULL_END
