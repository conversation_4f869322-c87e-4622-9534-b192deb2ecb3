//
//  IMYBabyMakeWeekMVSaver.m
//  IMYTools
//
//  Created by huang<PERSON><PERSON><PERSON> on 2025/7/4.
//

#import "IMYBabyMakeWeekMVSaver.h"
#import "BBJProgressAlertView.h"
#import "BBJPublishModel.h"
#import "BBJUploader.h"

@interface IMYBabyMakeWeekMVSaver () <PAGExportCallback>

@property (nonatomic, strong) PAGMovieExporter *exportSession;
@property (nonatomic, strong) PAGExportConfig *exportConfig;
@property (nonatomic, strong) BBJProgressAlertView *progressAlertView;
@property (nonatomic, assign) BOOL isCancenl;
@property (nonatomic, strong) BBJPublishModel *publishModel;
@property (nonatomic, copy) NSString *saveMVFilePath;

@end

@implementation IMYBabyMakeWeekMVSaver

- (void)dealloc {
    [self.exportSession cancel];
    self.exportSession = nil;
    self.exportConfig = nil;
}

#pragma mark - public
- (void)exportDataWithComposition:(PAGComposition *)composition pagName:(NSString *)pagName {
    if (self.exportSession != nil) {
        [self.exportSession cancel];
        self.exportSession = nil;
    }
    
    if (composition == nil) {
        return;
    }
    self.isCancenl = NO;
    /// 创建默认导出配置
    PAGExportConfig *config = [PAGExportConfig new];
    NSString *path = [pagName lastPathComponent];
    /// 设置路径
    NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES).firstObject;
    config.outputPath = [NSString stringWithFormat:@"%@/%@_%@_%@.mp4", self.saveMVFilePath, path ,[IMYPublicAppHelper shareAppHelper].userid ,@((uint64_t)[[NSDate date] timeIntervalSince1970]).stringValue];
    if ([[NSFileManager defaultManager] fileExistsAtPath:config.outputPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:config.outputPath error:nil];
    }
    
    config.width = composition.width;
    config.height = composition.height;
    config.frameRate = composition.frameRate;
    self.exportSession = [PAGMovieExporter Make:composition config:config callback:self];

    self.exportConfig = config;
    if (self.exportSession != nil) {
        [self.exportSession start];
    }
}

#pragma mark - PAGExportCallback
/// PAGMovieExporter的状态回调，回调在主线程
- (void)onStatusChange:(PAGExportStatus)status msgs:(NSArray<NSString*>*)msgs {
    if (status == PAGExportStatusComplete ||
        status == PAGExportStatusFailed ||
        status == PAGExportStatusCanceled) {
        self.exportSession = nil;
    }
    /// pag 1、导出视频成功 2、压缩视频 3、上传到OSS 4、上传到宝宝记相册
    if (status == PAGExportStatusComplete) {
        [self saveVideo:self.exportConfig.outputPath];
    } else if (status == PAGExportStatusFailed ) {
        [self.progressAlertView updateViewWithStatusIsFailure:YES proregss:0];
    }
}

/// PAGMovieExporter的进度回调，回调在主线程
- (void)onProgress:(CGFloat)progress {
    /// loding  1/2
    if (self.isCancenl) {
        return;
    }
    if (![self.progressAlertView superview]) {
        [[UIApplication sharedApplication].keyWindow addSubview:self.progressAlertView];
    }
    NSInteger progressInt = (progress*100)*0.5;
    self.progressAlertView.progressingText = @"正在制作...";
    [self.progressAlertView updateViewWithStatusIsFailure:NO proregss:progressInt];
}

/// videoPath为视频下载到本地之后的本地路径
- (void)saveVideo:(NSString *)videoPath {
    /// 发布成功后。需要跳转到播放页面  MV已保存
    if (self.isCancenl) {
        return;
    }
    if (!self.changeModel.baby_follower.can_record) {
        if (imy_isNotEmptyString(videoPath)) {
            [self saveVideoToAlbum:videoPath];
        }
        return;
    }

    /// 隐藏 MV
    if ([self.fromURI.params.allKeys containsObject:@"module_flag"] && self.isPublishPostNotification) {
        NSString *module_flag = [self.fromURI.params objectForKey:@"module_flag"];
        NSInteger module_id = [[self.fromURI.params objectForKey:@"module_id"] integerValue];
        NSMutableDictionary *dict = [NSMutableDictionary new];
        [dict imy_setNonNilObject:module_flag forKey:@"module_flag"];
        [dict imy_setNonNilObject:@(module_id) forKey:@"module_id"];
        [dict imy_setNonNilObject:@(self.baby_id) forKey:@"baby_id"];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"k_had_publish_mv" object:dict];
    }
    self.isCancenl = NO;
    BBJPublishModel *model = [BBJPublishModel createPublishModelForMVWithBabyId:self.baby_id babyType:self.babyType videoPath:videoPath];
    if ([self.fromURI.params objectForKey:@"position"]) {
        NSInteger postition = [[self.fromURI.params objectForKey:@"position"] integerValue];
        NSInteger secondPosition = 0;
        if ([self.fromURI.params objectForKey:@"secondPosition"]) {
            secondPosition = [[self.fromURI.params objectForKey:@"secondPosition"] integerValue];
        } else if ([self.fromURI.params objectForKey:@"second_position"]){
            secondPosition = [[self.fromURI.params objectForKey:@"second_position"] integerValue];
        }
        BBJPublishConfig *publishConfig = [BBJPublishConfig new];
        publishConfig.position = postition;
        publishConfig.secondPosition = secondPosition;
        model.publishConfig = publishConfig;
    }

    self.publishModel = model;
    @weakify(self);
    [[BBJUploader sharedInstance] sendUploadPublishModel:model progress:^(double value) {
        @strongify(self);
        imy_asyncMainExecuteBlock(^{
            if (!self.isCancenl) {
                NSInteger progressInt = (value*100)*0.5 + 50;
                self.progressAlertView.progressingText = @"正在保存...";
                [self.progressAlertView updateViewWithStatusIsFailure:NO proregss:progressInt];
            } else {
                [self removeProgressAlertView];
            }
        });
    } completion:^(BOOL isSuccess, NSError * _Nullable error) {
        @strongify(self);
        imy_asyncMainExecuteBlock(^{
            if (!self.isCancenl) {
                if (isSuccess) {
                    self.progressAlertView.progressingText = @"正在保存...";
                    [self.progressAlertView updateViewWithStatusIsFailure:NO proregss:100];
                    if (self.nextBlock) {
                        self.nextBlock(videoPath, self.publishModel.recordId, NO);
                    }
                } else {
                    [self.progressAlertView updateViewWithStatusIsFailure:YES proregss:0];
                    [[BBJUploader sharedInstance] removePublishModel:self.publishModel];
                    [[BBJUploader sharedInstance] giveUpPublishModel:self.publishModel failedType:BBJUploadRecordFailedTypeManual];
                }
            } else {
                [self removeProgressAlertView];
            }
        });
    }];
}

/// 保存视频到相册
- (void)saveVideoToAlbum:(NSString *)videoPath {
    if (UIVideoAtPathIsCompatibleWithSavedPhotosAlbum(videoPath)) {
        NSError *error = nil;
        __block PHObjectPlaceholder *placeholder;
        [[PHPhotoLibrary sharedPhotoLibrary] performChangesAndWait:^{
            PHAssetChangeRequest *request = [PHAssetChangeRequest creationRequestForAssetFromVideoAtFileURL:[NSURL URLWithString:videoPath]];
            placeholder = [request placeholderForCreatedAsset];
        } error:&error];
        if (error) {
            [UIWindow imy_showTextHUD:@"保存至手机失败"];
            [self.progressAlertView updateViewWithStatusIsFailure:YES proregss:100];
        } else {
            [UIWindow imy_showTextHUDWithDelay:2 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"已保存至手机")];
            [self.progressAlertView updateViewWithStatusIsFailure:NO proregss:100];
            imy_asyncMainBlock(^{
                if (self.nextBlock) {
                    self.nextBlock(videoPath, self.publishModel.recordId, YES);
                }
            });
        }
    } else {
        [UIWindow imy_showTextHUD:@"视频保存失败,请设置软件读取相册权限"];
        [self removeProgressAlertView];
    }
}

/// 移除 progressAlertView
- (void)removeProgressAlertView {
    if (_progressAlertView) {
        [self.progressAlertView removeFromSuperview];
        self.progressAlertView = nil;
    }
}

#pragma mark - setter or getter
- (BBJProgressAlertView *)progressAlertView {
    if (!_progressAlertView) {
        _progressAlertView = [[BBJProgressAlertView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        _progressAlertView.progressingText = @"正在制作…";
        _progressAlertView.isAnimBg = YES;
        [[UIApplication sharedApplication].keyWindow addSubview:_progressAlertView];
        @weakify(self);
        _progressAlertView.touchCancel = ^{
            @strongify(self);
            [self.exportSession cancel];
            NSInteger index = [[BBJUploader sharedInstance].publishModels indexOfObject:self.publishModel];
            [[BBJUploader sharedInstance] removePublishModelAtIndex:index];
            NSInteger uploadingCount = [BBJUploader sharedInstance].uploadingModels.count;
            NSInteger successCount = [BBJUploader sharedInstance].uploadSuccessModels.count;
            if (uploadingCount == 0 && successCount == 0) {
                [BBJUploader sharedInstance].hideUploadView = YES;
                [[NSNotificationCenter defaultCenter] postNotificationName:@"BBJ_Record_All_Cancel_Not" object:nil];
            }
            self.isCancenl = YES;
            [self removeProgressAlertView];
        };
    }
    return _progressAlertView;
}

- (NSString *)saveMVFilePath {
    if (!_saveMVFilePath) {
        NSString *cache = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES)lastObject];
        _saveMVFilePath = [cache stringByAppendingPathComponent:@"BabyMVVideoCache"];
        if (![[NSFileManager defaultManager] fileExistsAtPath:_saveMVFilePath]) {
            [[NSFileManager defaultManager] createDirectoryAtPath:_saveMVFilePath withIntermediateDirectories:YES attributes:nil error:nil];
        }
    }
    return _saveMVFilePath;
}

@end
