//
//  IMYBabyMakeWeekMVSaver.h
//  IMYTools
//
//  Created by huang<PERSON><PERSON><PERSON> on 2025/7/4.
//

#import <Foundation/Foundation.h>
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYBabyMVTemplateListModel.h"
#import <libpag/PAGMovieExporter.h>

NS_ASSUME_NONNULL_BEGIN

/// mv保存器
@interface IMYBabyMakeWeekMVSaver : NSObject

@property (nonatomic, strong) IMYURI *fromURI; ///< fromURI
@property (nonatomic, strong) IMYBabyMVTemplateForMaterialModel *changeModel; ///< 素材模型
@property (nonatomic, assign) NSInteger baby_id; ///< 宝宝记id
@property (nonatomic, assign) NSInteger babyType; ///< 宝宝类型
@property (nonatomic, assign) BOOL isPublishPostNotification; ///< 是否发布通知
/// 操作回调
@property (nonatomic, copy) void (^nextBlock)(NSString *videoPath, NSInteger recordId, BOOL noCheckSaveVideo);

/// 导出数据
- (void)exportDataWithComposition:(PAGComposition *)composition pagName:(NSString *)pagName;

@end

NS_ASSUME_NONNULL_END
