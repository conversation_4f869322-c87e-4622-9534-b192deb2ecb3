//
//  IMYBabyMakeWeekMVHelper.m
//  IMYTools
//
//  Created by huang<PERSON><PERSON><PERSON> on 2025/7/1.
//

#import "IMYBabyMakeWeekMVHelper.h"
#import "IMYBabyMakeWeekDefaultTextModel.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYBabyMakeWeekMVHelper

/*
实验名称：用户可对宝宝成长MV中的照片智能配文
正式环境：https://admin-ab.meiyou.com/ab/experiment/detail?id=1780
测试环境：https://test-admin-ab.meiyou.com/ab/experiment/detail?id=1996
*/
+ (BOOL)mvAiCaptionSwitch {
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"mv_ai_caption"];
    if (exp && (exp.status == IMYABTestExpStatusGloballyIsolated || exp.status == IMYABTestExpStatusModuleIsolated)) {//全局隔离域  业务隔离域
        return NO;
    }
    if ([exp.vars integerForKey:@"ai_caption"] != 1) {
        return NO;
    }
    return YES;
}

/// 是否展示ai配文
+ (BOOL)isAiCaptionDisplayStatus {
    BOOL isSwitch = [[IMYUserDefaults standardUserDefaults] boolForKey:[self aiCaptionSwitchKey]];
    return isSwitch;
}

/// 设置ai配文展示状态
+ (void)setAiCaptionDisplayStatus:(BOOL)isSwitch {
    [[IMYUserDefaults standardUserDefaults] setBool:isSwitch forKey:[self aiCaptionSwitchKey]];
    [[IMYUserDefaults standardUserDefaults] synchronize];
}

/// 重置ai配文展示状态
+ (void)resetAiCaptionDisplayStatus {
    [self setAiCaptionDisplayStatus:YES];
}

+ (NSString *)aiCaptionSwitchKey {
    return @"IMYBabyMakeWeekMVAiCaptionSwitchKey";
}

@end
