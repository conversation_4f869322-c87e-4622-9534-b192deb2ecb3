//
//  IMYBabyMVPAGView.m
//  IMYRecord
//
//  Created by ss on 2023/10/8.
//

#import "IMYBabyMVPAGView.h"
#import "IMYBaseKit.h"
@interface IMYBabyMVPAGView ()<IMYPAGViewDelegate>

@property (nonatomic, strong) UIActivityIndicatorView *indicatorView;//pag指示器

@end

@implementation IMYBabyMVPAGView

- (instancetype)initWithBigPAG:(BOOL)bigPAG {
    self = [super initWithBigPAG:bigPAG];
    if (self) {
        [self initUI];
        
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.captionView];
    [self addSubview:self.indicatorView];
    [self.indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.height.mas_equalTo(26);
    }];
    [self.captionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

- (void)loadWithURL:(NSURL * const)url
        placeholder:(UIImage * const)placeholder
          completed:(void (^ const)(BOOL))completedBlock {
    @weakify(self)
    if ([self.currentURL isEqual:url]) {
        !completedBlock? : completedBlock(self.loaded);
        if (!self.loaded && !IMYNetState.networkEnable) {
            imy_asyncMainBlock(0.5, ^{
                [self.captionView setHidden:NO];
            });
        }
        return;
    }
    [self stop];
    self.userInteractionEnabled = NO;
    [self.captionView setHidden:YES];
    self.indicatorView.hidden = NO;
    [self.indicatorView startAnimating];
    [super loadWithURL: url placeholder:placeholder completed:^(BOOL loaded) {
        @strongify(self);
        self.indicatorView.hidden = YES;
        [self.indicatorView stopAnimating];
        
        !completedBlock? : completedBlock(loaded);
        if(!loaded) {
            [self stop];
            self.userInteractionEnabled = YES;
            [self.captionView setHidden:NO];
            
        }
    }];
}


- (UIActivityIndicatorView *)indicatorView {
    if (!_indicatorView) {
        _indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
    }
    return _indicatorView;
}

- (UIView *)captionView {
    if (!_captionView) {
        _captionView = [[UIView alloc] initWithFrame:self.bounds];
        [_captionView imy_setBackgroundColorForKey:kCK_White_AN];
        [_captionView imy_drawAllCornerRadius:12];
        
        UIView *bgView = [[UIView alloc] init];
        [_captionView addSubview:bgView];
        bgView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
        [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];
        
        UIView *containView = [[UIView alloc] init];
        [bgView addSubview:containView];
        [containView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.mas_equalTo(self.captionView);
            make.left.right.mas_equalTo(self.captionView);
            make.height.mas_equalTo(72);
        }];
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
        label.font = [UIFont imy_LightFontWith:14];
        [label imy_setTextColorForKey:kCK_White_AN];
        label.text = IMYString(@"网络不见了，请检查网络");
        label.textAlignment = NSTextAlignmentCenter;
        [containView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.mas_equalTo(containView);
            make.height.mas_equalTo(20);
        }];
        
        IMYCapsuleButton *retryButton = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, 72, 32)];
        [retryButton setTitle:@"重新加载" forState:UIControlStateNormal];
        retryButton.titleLabel.font = [UIFont imy_LightFontWith:12];
        retryButton.capType = IMYButtonCapTypeNormal;
        retryButton.borderColor = [IMYColor colorWithNormal:kCK_Red_A];
        retryButton.titleColor = [IMYColor colorWithNormal:kCK_Red_A];
        retryButton.makeBorder = YES;
        retryButton.cornerRadius = 16;
        retryButton.contentColor = [IMYColor colorWithNormal:kCK_White_A high:kCK_White_A];
        [retryButton imy_drawAllCornerRadius:16];
        [containView addSubview:retryButton];
        [retryButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.bottom.mas_equalTo(containView);
            make.height.mas_equalTo(32);
            make.width.mas_equalTo(72);
        }];
        @weakify(self);
        [[retryButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self);
            [self.captionView setHidden:YES];
            !self.retryBlock ? : self.retryBlock();

        }];
        [containView addSubview:retryButton];
    }
    return _captionView;
}

@end
