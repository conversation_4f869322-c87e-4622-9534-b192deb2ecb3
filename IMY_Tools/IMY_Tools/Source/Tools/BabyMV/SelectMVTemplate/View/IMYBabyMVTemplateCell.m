//
//  IMYBabyMVTemplateCell.m
//  IMYTools
//
//  Created by SS on 20/8/10.
//  Copyright © 2020年 MeetYou. All rights reserved.
//

#import "IMYBabyMVTemplateCell.h"
#import <IMYBaseKit/IMYBaseKit.h>
@interface IMYBabyMVTemplateCell()

@property (strong, nonatomic) UIImageView *iconImageView;
@property (strong, nonatomic) UIImageView *selectBgImage;
@property (strong, nonatomic) UIImageView *selectBgWhiteImage;

@property (strong, nonatomic) UILabel *textLabel;

@property (strong, nonatomic) UILabel *countLabel;
@property (strong, nonatomic) IMYBabyMVTemplateModel *model;

@end

@implementation IMYBabyMVTemplateCell


- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.selectBgImage = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, self.imy_width)];
        self.selectBgImage.layer.borderColor = [UIColor imy_colorForKey:kCK_Red_A].CGColor;
        self.selectBgImage.layer.borderWidth = 4;
        [self.selectBgImage setHidden:YES];
        
        self.selectBgWhiteImage = [[UIImageView alloc] initWithFrame:CGRectMake(4, 4, self.imy_width - 8, self.imy_width - 8)];
        self.selectBgWhiteImage.layer.borderColor = [UIColor imy_colorForKey:kCK_White_AN].CGColor;
        self.selectBgWhiteImage.layer.borderWidth = 2;
        [self.selectBgWhiteImage setHidden:YES];
        
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(6, 6, self.imy_width - 12, self.imy_width - 12)];
        [self.contentView addSubview:imageView];
        self.iconImageView = imageView;
        self.iconImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.iconImageView.imy_showViewSize = self.iconImageView.imy_size;
        [self.iconImageView imy_setPlaceholderImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Black_HN) andSize:CGSizeMake(self.imy_width, self.imy_width)]];
        [self.iconImageView imy_setBackgroundColorForKey:kCK_Black_HN];
        self.iconImageView.userInteractionEnabled = NO;
        
        UILabel *textLabel = [[UILabel alloc] init];
        textLabel.textAlignment = NSTextAlignmentCenter;
        textLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:16];
        [textLabel imy_setTextColorForKey:kCK_Black_A];
        [self.contentView addSubview:textLabel];
        self.textLabel = textLabel;

        [textLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.mas_equalTo(self.contentView);
            make.bottom.mas_equalTo(self.contentView.mas_bottom);
            make.height.mas_offset(20);
        }];
        
        [self.contentView addSubview:self.selectBgImage];
        [self.contentView addSubview:self.selectBgWhiteImage];
        
        [self.selectBgWhiteImage imy_drawAllCornerRadius:10];
        [self.selectBgImage imy_drawAllCornerRadius:12];
        [self.iconImageView imy_drawAllCornerRadius:8];
        
    }
    return self;
}

- (void)setTemplateModel:(IMYBabyMVTemplateDetailModel *)model {
    [self setContentViewHide:NO];
    self.model = model;
    [self.iconImageView imy_setImageURL:model.grid_cover];
    self.textLabel.text = model.title;
    if (model.isSelect) {
        [self.selectBgImage setHidden:NO];
        [self.selectBgWhiteImage setHidden:NO];
    } else {
        [self.selectBgImage  setHidden:YES];
        [self.selectBgWhiteImage setHidden:YES];
    }
}


- (void)setBlankCell {
    [self setContentViewHide:YES];
}

- (void)setContentViewHide:(BOOL)isHidden {
    [self.iconImageView setHidden:isHidden];
    [self.textLabel setHidden:isHidden];
    [self.countLabel setHidden:isHidden];
    [self.selectBgImage  setHidden:YES];
    [self.selectBgWhiteImage setHidden:YES];
}

#pragma mark 预加载Pag 预览图图片
- (void)preLoadPagFile:(NSString *)url {
    /// 都没有则预下载图片
    [IMYPAGView downloadPAGFileWithURL:[NSURL URLWithString:url] completionHandler:^(BOOL success, NSString * _Nonnull filePath) {
            
    }];
}
+ (NSString *)cellIdentifier {
    return @"IMYBabyMVTemplateCellCollectionViewCellIdentifier";;
}

@end
