//
//  IMYToolsPostpartumVC.m
//  IMY_Tools
//
//  Created by meiyou on 2021/4/8.
//  Copyright © 2021 linggan. All rights reserved.
//

#import "IMYBabyMVTemplateContainerVC.h"
#import "IMYBabyMVTemplateViewModel.h"
#import "IMYBabyMVTemplatePageVC.h"
#import <IMYBaseKit/UIFont+IMYViewKit.h>
#import <IMYBaseKit/IMYTabButtonPageViewController.h>
#if __has_include(<IMYCommonKit/IMYCommonPushAlertView.h>)
#import <IMYCommonKit/IMYCommonPushAlertView.h>
#endif
#if __has_include(<IMYYunyuChange/UIScrollView+PageBarMask.h>)
#import <IMYYunyuChange/UIScrollView+PageBarMask.h>
#endif
#import <libpag/PAGVideoDecoder.h>
#import <libpag/PAGLicenseManager.h>
#import <libpag/PAGFile.h>
#import <libpag/PAGMovie.h>
#import <libpag/PAGMovieExporter.h>
#import <CoreServices/CoreServices.h>
#import "IMYBabyMVPAGView.h"
#import "IMYBabyMakeMVViewController.h"
#import "BBJBabyCacheManager.h"
#import "IMYRecordPregnancyBabyManager.h"
#import <BBJBabyHome/BBJPhotoPickerConfig.h>

@import IMYSwift;

@interface IMYBabyMVTemplateContainerVC () <IMYPageViewControllerDelegate, IMYPageViewControllerDataSource, UIScrollViewDelegate,IMYPAGViewDelegate>

@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) IMYTabButtonPageViewController *pageVC;
@property (nonatomic, strong) IMYBabyMVTemplateViewModel *viewModel;
@property (nonatomic, assign) NSUInteger selectIndex;
@property (nonatomic, strong) IMYCapsuleButton *makeMVBtn;//立即制作
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) IMYButton *backButton;
@property (nonatomic, strong) IMYBabyMVPAGView *playerView;
@property (nonatomic, strong) IMYBabyMVTemplateDetailModel *selectTemplateModel;//当前选中Pag模板
@property (nonatomic, strong) IMYCaptionView *captionPagPlayerView;//page 状态
@property (nonatomic, strong) UIView *shadowView;
@property (nonatomic, copy) NSArray *lastSelectedModels;
@property (nonatomic, copy) NSString *lastPagUrl;

@end

@implementation IMYBabyMVTemplateContainerVC
- (void)dealloc {
    [_playerView stop];
    [_playerView removeFromSuperview];
    _playerView = nil;
    _pageVC = nil;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    [self prepareData];
    [self initUI];
    [self requestData];
    [self initMaskView];
    [self addNotification];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear: animated];
    [self showTips];
}

- (UIView *)addShowTipsBgView {
    UIView * bgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 140)];
    // 渐变效果
    UIColor * white = [UIColor imy_colorForKey:kCK_White_A];
    CAGradientLayer *gradient = [CAGradientLayer layer];
    gradient.frame = CGRectMake(0, 0, SCREEN_WIDTH, 140);
    gradient.colors = @[(id)[white colorWithAlphaComponent:0].CGColor,
                        (id)[white colorWithAlphaComponent:0.6].CGColor];
    
    [bgView.layer addSublayer:gradient];
    [self.view addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-72 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
        make.height.mas_equalTo(140);
        make.left.right.mas_equalTo(self.view);
    }];
    return bgView;
}

- (void)showTips {
    
    NSString * tipsKey = @"mvtipskey";
    
    BOOL showed = [[IMYUserDefaults standardUserDefaults] boolForKey:tipsKey];
    if (showed) {
        return;
    }
    
    UIView * bgView = [self addShowTipsBgView];
    
    [[IMYUserDefaults standardUserDefaults] setObject:@(YES) forKey:tipsKey];
    [[IMYUserDefaults standardUserDefaults] synchronize];
    
    IMYTipsLabelView * tipsView = [IMYTipsLabelView showOn:self.makeMVBtn
                                                      tips:@"MV支持添加视频素材啦，快来试试吧~"
                                               enableBgBtn:YES
                                             contentHeight:45
                                         leftAndRightSpace:12
                                               onViewSpace:16
                                                      font:[UIFont imy_regularWith:15]
                                                 textColor:[UIColor imy_colorForKey:kCK_White_A]
                                                   bgColor:[[UIColor blackColor] colorWithAlphaComponent:0.6]];
    
    [tipsView.tipsLabel imy_setTextColorForKey:kCK_White_A];
    
    [tipsView.contentView imy_addThemeChangedBlock:^(UIView * weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.backgroundColor = [UIColor imy_colorWithHexString:@"#FF4D88"];
        } else {
            weakObject.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        }
    }];
    
    [tipsView.arrowLayer imy_addThemeChangedBlock:^(CAShapeLayer * weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.fillColor = [UIColor imy_colorWithHexString:@"#FF4D88"].CGColor;
        } else {
            weakObject.fillColor = [[UIColor blackColor] colorWithAlphaComponent:0.6].CGColor;
        }
    }];
    
    tipsView.dismissBlock = ^{
        [bgView removeFromSuperview];
    };
}

#pragma mark - Method
- (void)prepareData {
    if(self.common_babyId == 0){
        self.common_babyId = [IMYBabyMVTemplateViewModel getCurrCommonBabyId];
        @weakify(self);
        [[BBJBabyCacheManager shareInstance] convertBBJBabyIdFromCacheDataCommonBabyId:self.common_babyId
                                                                            completion:^(id  _Nullable resData, NSError * _Nullable error) {
            @strongify(self);
            NSInteger babyId = [resData integerValue];
            self.bbj_babyId = babyId;
        }];
    }
}

- (void)initUI {
    [self setupNavigationBar];
    [self initPageUI];
    [self initBottom];
    [self initPagPlayerUI];
    [self initShadowView];
}

- (void)setupNavigationBar {
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    
    [self.view addSubview:self.backButton];
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).mas_offset(16);
        make.width.height.mas_equalTo(24);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(SCREEN_STATUSBAR_HEIGHT + 10);
    }];
    
    [self.view addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(150);
        make.height.mas_offset(44);
        make.centerX.mas_equalTo(self.view);
        make.centerY.mas_equalTo(self.backButton);
    }];
}

- (void)initPagPlayerUI {
    [self.view addSubview:self.playerView];
    
}

- (void)initPageUI {
    [self.view addSubview:self.pageVC.view];
    [self addChildViewController:self.pageVC];
    [self.pageVC didMoveToParentViewController:self];
    //底部pageview 高度
    CGFloat height = 44 + IMYBabyMVTemplatePageVCCellSpace + (SCREEN_WIDTH - IMYBabyMVTemplatePageVCCellSpace)/3.0 + 30 + IMYBabyMVTemplatePageVCCellSpace + 24;
    self.pageVC.view.frame = CGRectMake(0,
                                        SCREEN_HEIGHT - height - 72 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN,
                                        SCREEN_WIDTH,
                                        height);
//    self.pageVC.scrollView.scrollEnabled = NO;
}

- (void)initBottom {
    UIView *bgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 72 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    [bgView imy_setBackgroundColorForKey:kCK_White_AN];
    [bgView imy_showLineForDirection:IMYDirectionUp];
    IMYCapsuleButton *makeMVBtn = [IMYCapsuleButton new];
    self.makeMVBtn = makeMVBtn;
    makeMVBtn.titleLabel.font = [UIFont systemFontOfSize:18];
    makeMVBtn.type = IMYButtonTypeFillRed;
    makeMVBtn.frame = CGRectMake(12, 12, SCREEN_WIDTH - 24, 48);
    [makeMVBtn setTitle:@"立即制作" forState:UIControlStateNormal];
    [bgView addSubview:makeMVBtn];
    @weakify(self);
    [[makeMVBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        [self makePagMVAction];
        
    }];
    [self.view addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view.mas_bottom);
        make.height.mas_equalTo(72 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
        make.left.right.mas_equalTo(self.view);
    }];
}
/// pag 左右滚动蒙层
- (void)initMaskView {
    UIView *superView = self.pageVC.pageBarView;
    UIScrollView *scrollView = self.pageVC.collectionViewBar;
    [scrollView imy_initMaskOnView:superView colorKey:kCK_White_AN];

    @weakify(self, scrollView);
    [[RACObserve(scrollView, contentOffset) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self, scrollView);
        [scrollView imy_updateMaskState];
    }];
}
//MARK: 吸顶投影
- (void)initShadowView {
    if (!self.shadowView) {
        UIView *shadow = [UIView new];
        [shadow imy_setBackgroundColorForKey:kCK_Black_F];
        shadow.layer.shadowColor = [[UIColor imy_colorForKey:kCK_Black_F] colorWithAlphaComponent:0.1].CGColor;
        shadow.layer.shadowRadius = 5;
        shadow.layer.shadowOffset = CGSizeMake(0, 3);
        shadow.layer.shadowOpacity = 0.6;
        self.shadowView = shadow;
    }
    
    UIView *superView = self.pageVC.pageBarView.superview;
    [superView insertSubview:self.shadowView belowSubview:self.pageVC.pageBarView];
    [self.shadowView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.pageVC.pageBarView);
    }];
}

#pragma mark - notify
- (void)addNotification {
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYBabyMVTemplatePageVCMoreThanScreen" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        BOOL isHiddle = [note.object boolValue];
        [self.pageVC.separatorLine setHidden:!isHiddle];
    }];
}
#pragma mark - 网络相关
- (void)requestData {
    self.selectIndex = MIN(self.pageIndex, [self.viewModel categoryCount] -1);
    self.captionPagPlayerView.state = IMYCaptionViewStateHidden;
    self.captionView.state = IMYCaptionViewStateLoading;
    if (self.bbj_babyId <= 0 && self.pregnancy_bbj_babyId <= 0) {//没宝宝 无数据兜底
        self.captionView.state = IMYCaptionViewStateNoResult;
        return;
    }
    @weakify(self);
    [self.viewModel requestDataWithBabyId:self.common_babyId
                              first_MV_Id:self.first_MV_Id
                            completeBlock:^(BOOL bSuccess, IMYBabyMVTemplateListModel * _Nonnull model) {
        @strongify(self);
        if (bSuccess && model && model.list.count > 0) {
            [self.pageVC reloadData];
            [self.pageVC setViewControllerAtIndex:self.selectIndex animated:NO];
            self.captionView.state = IMYCaptionViewStateHidden;
            [self defaultFirstPagTemplate:model];
         
        } else if ( bSuccess && (!model || model.list.count <= 0)) {
            self.captionView.state = IMYCaptionViewStateNoResult;
        } else {
            self.captionView.state =  IMYCaptionViewStateRetry;
        }
    }];
}

#pragma mark - IMYPageViewControllerDataSource and Delegate
- (UIViewController *)pageViewController:(IMYPageViewController *)pageViewController controllerAtIndex:(NSUInteger)index {
    index = index < self.viewModel.categoryCount ? index : self.viewModel.categoryCount - 1;
    return [self pageVCWithIndex:index tag:pageViewController.view.tag];
}

- (NSUInteger)numberOfControllersInPageViewController:(IMYPageViewController *)pageViewController {
    return self.viewModel.categoryCount;
}

- (NSString *)pageViewController:(IMYPageViewController *)pageViewController titleAtIndex:(NSUInteger)index {
    return [self.viewModel categoryTitleWithIndex:index];
}

- (IMYBabyMVTemplatePageVC *)pageVCWithIndex:(NSInteger)index tag:(NSInteger)tag{
    NSAssert(index >= 0 && index < self.viewModel.categoryCount, @"index error");
    IMYBabyMVTemplateModel *model = [self.viewModel categoryModelWithIndex:index];
    IMYBabyMVTemplatePageVC *vc = [self dequeueVCAtIndex:index];
    vc.view.backgroundColor = [UIColor clearColor];
    vc.templateModel = model;
    return vc;
}

#pragma mark - private

- (void)defaultFirstPagTemplate:(IMYBabyMVTemplateListModel *)model {
    self.selectTemplateModel = model.list.firstObject.items.firstObject;
    //默认选中第一个
    [model selectTemplateWithModel:self.selectTemplateModel andListId:model.list.firstObject.cid];
    if (self.selectTemplateModel) {
        [self updatePagPlayer:self.selectTemplateModel];
    }
}
- (IMYBabyMVTemplatePageVC *)dequeueVCAtIndex:(NSInteger)index {
    IMYBabyMVTemplateModel *model = [self.viewModel categoryModelWithIndex:index];
    IMYBabyMVTemplatePageVC *vc = [[IMYBabyMVTemplatePageVC alloc] init];
    vc.templateModel = model;
    @weakify(self,model);
    vc.selectBlock = ^(IMYBabyMVTemplateDetailModel * _Nonnull templateModel) {
        @strongify(self,model);
        self.selectTemplateModel = templateModel;
        [self.viewModel.changeModel selectTemplateWithModel:self.selectTemplateModel andListId:model.cid];
        [self reloadUI];
        [self updatePagPlayer:templateModel];
        [self biReportWithEventName:@"yy_xzmvzty_xzmb" public_type:self.selectTemplateModel.tid public_info:model.cid] ;
        
    };
    return vc;
}

- (void)reloadUI {
    for (IMYBabyMVTemplatePageVC *pageVC in self.pageVC.visibleViewControllers) {
        [pageVC reloadUI];
    }
}
#pragma mark - IMYPAG 相关

- (void)updatePagPlayer:(IMYBabyMVTemplateDetailModel *)model {
   
    NSURL *imageURL = [NSURL URLWithString:model.first_cover];
    UIImage *placeholderImage = [[SDImageCache sharedImageCache] imageFromMemoryCacheForKey:model.first_cover];
    //已下载就不设置占位图
    if (placeholderImage == nil && ![IMYPAGView pagCachePathWithURL:[NSURL URLWithString:model.file_url]]) {
        placeholderImage = [[SDImageCache sharedImageCache] imageFromDiskCacheForKey:model.first_cover];
    }
    @weakify(self);
    [self.playerView loadWithURL:[NSURL URLWithString:model.file_url]
                     placeholder:placeholderImage completed:^(BOOL loaded) {
        @strongify(self);
        if (loaded) {
            self.selectTemplateModel.local_pag_file_path = [IMYPAGView pagCachePathWithURL:[NSURL URLWithString:model.file_url]];
            [self.playerView.captionView setHidden:YES];
            [self.playerView play];
        }
    }];
}
#pragma mark - 立即制作Action

/// 立即制作Action
- (void)makePagMVAction {
    // 无网络，不响应点击事件
    if (!IMYNetState.networkEnable) {
        [UIWindow imy_showTextHUD:MT_Request_NoNetToast];
        return;
    }
    if (imy_isEmptyString(self.selectTemplateModel.local_pag_file_path)) {
        [UIWindow imy_showTextHUD:@"MV模板加载中，请稍后再试"];
        return;
    }
    if (![self handleTurnToLogin]) {
        return;
    }
    [UIWindow imy_showLoadingHUD];
    [self makePagMVWithDownloadPag_step1];
    [self biReportWithEventName:@"yy_xzmvzty_ljzzan" public_type:self.selectTemplateModel.tid public_info:[IMYBabyMVTemplateListModel getSelectCid:self.viewModel.changeModel]];
}
- (void)makePagMVWithDownloadPag_step1 {
    NSString *localPagPath = [IMYPAGView pagCachePathWithURL:self.playerView.currentURL];
    self.selectTemplateModel.local_pag_file_path = localPagPath;
    if (imy_isEmptyString(localPagPath)) {
        @weakify(self);
        [UIWindow imy_showLoadingHUD];
        [IMYPAGView downloadPAGFileWithURL:[NSURL URLWithString:self.selectTemplateModel.file_url]
                         completionHandler:^(BOOL success, NSString * _Nonnull filePath) {
            @strongify(self);
            [UIWindow imy_hideHUD];
            if (success && imy_isNotEmptyString(filePath)) {
                self.selectTemplateModel.local_pag_file_path = localPagPath;
                [self makePagMVWithGetBabyId_step2];
            } else {
                [UIWindow imy_showTextHUD:@"网络异常，请检查网络"];
            }
        }];
    } else {
        [self makePagMVWithGetBabyId_step2];
    }
}

/// 获取相关的 babyid
- (void)makePagMVWithGetBabyId_step2 {
    if (self.pregnancy_bbj_babyId > 0) {
        [self gotoMakeMVVCWithBBJId_step3:self.pregnancy_bbj_babyId andBabyType:2];
        [UIWindow imy_hideHUD];
        return;
    }
    if (self.bbj_babyId > 0) {
        NSInteger type = [[BBJBabyCacheManager shareInstance] searchBabyCacheFromDBWithBabyId:self.bbj_babyId].baby_type;
        [self gotoMakeMVVCWithBBJId_step3:self.bbj_babyId andBabyType: type];
    } else if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
           //数据异常 。怀孕身份胎宝宝
        [self gotoMakeMVVCWithBBJId_step3:[IMYRecordPregnancyBabyManager sharedInstance].bbj_baby_id andBabyType:2];
    } else {
        [UIWindow imy_showTextHUD:@"数据异常，请重试"];
    }
}
/// 获取相关的 babyid
///< babyType 宝宝类型，1=备孕，2=怀孕，3=出生
- (void)gotoMakeMVVCWithBBJId_step3:(NSInteger)BBJId andBabyType:(NSInteger)babyType {
    @weakify(self);
    [IMYBabyMVTemplateViewModel requestBabyPicCountDataWithBabyId:BBJId completeBlock:^(BOOL bSuccess, NSInteger picCount, NSString * _Nonnull header, NSString * _Nonnull nickname, NSString *birthday) {
        @strongify(self);
        [UIWindow imy_hideHUD];
        NSInteger picType = 0;
        if (picCount < self.selectTemplateModel.material_num) {
            picType = 1;
        }
        NSMutableDictionary *dict = [NSMutableDictionary new];
        [dict imy_setNonNilObject:self.selectTemplateModel.file_url forKey:@"fileUrl"];
        [dict imy_setNonNilObject:self.selectTemplateModel.local_pag_file_path forKey:@"localFilePath"];
        
        BOOL mvEnableSelectVideo = self.selectTemplateModel.support_video == 1;
        if (mvEnableSelectVideo) {
            [dict imy_setNonNilObject:@(BBJPhotoSelectedTypeAll) forKey:@"selectedType"];
            [dict imy_setNonNilObject:@(BBJPhotoShowTypeAll) forKey:@"showType"];
            [dict imy_setNonNilObject:@(mvEnableSelectVideo) forKey:@"mvEnableSelectVideo"];
        }
        
        [dict imy_setNonNilObject:@(self.selectTemplateModel.material_num) forKey:@"picNumMin"];
        [dict imy_setNonNilObject:@(self.selectTemplateModel.material_num) forKey:@"picNumMax"];
        [dict imy_setNonNilObject:@(picType) forKey:@"picType"];
        [dict imy_setNonNilObject:@(BBJId) forKey:@"babyId"];
        [dict imy_setNonNilObject:header forKey:@"header"];
        [dict imy_setNonNilObject:nickname forKey:@"nickname"];
        [dict imy_setNonNilObject:@(self.selectTemplateModel.tid) forKey:@"tid"];
        [dict imy_setNonNilObject:@([IMYBabyMVTemplateListModel getSelectCid:self.viewModel.changeModel]) forKey:@"cid"];
        /// 同一个MV模版，不清空素材
        if (self.lastPagUrl.length > 0 && [self.lastPagUrl isEqualToString:self.selectTemplateModel.file_url]) {
            [dict imy_setNonNilObject:self.lastSelectedModels forKey:@"lastSelectedModels"];
        }
        IMYURI *uri = [IMYURI uriWithPath:@"tools/selectPhotoForMV" params:dict info:nil];
        IMYURIActionBlockObject *actionObj = [IMYURIActionBlockObject actionBlockWithURI:uri];
        @weakify(self);
        actionObj.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
           @strongify(self);
           imy_asyncMainBlock(^{
               [UIWindow imy_hideHUD];
               if ([result isKindOfClass:[NSArray class]]) {
                   NSArray *imageArr = [[NSMutableArray alloc] initWithArray:result];
                   IMYBabyMakeMVViewController *vc = [IMYBabyMakeMVViewController new];
                   vc.fromURI = self.fromURI;
                   vc.common_baby_id = self.common_babyId;
                   vc.bbjBabyId = BBJId;
                   vc.babyType = babyType;
                   vc.mvPositionModels = imageArr;
                   vc.cid = [IMYBabyMVTemplateListModel getSelectCid:self.viewModel.changeModel];
                   IMYBabyMVTemplateForMaterialModel *changeModel = [IMYBabyMVTemplateForMaterialModel new];
                   changeModel.baby_follower = self.viewModel.changeModel.baby_follower;
                   changeModel.tModel = self.selectTemplateModel;
                   vc.changeModel = changeModel;
                   [[self imy_currentShowViewController]  imy_push:vc];
                   NSLog(@"-收到图片数据");
               }else if ([result isKindOfClass:[NSDictionary class]]){
                   NSDictionary *dict = (NSDictionary *)result;
                   BOOL isClose = [dict objectForKey:@"isClose"];
                   if (isClose) {
                       NSArray *lastSelectedModels = [dict objectForKey:@"lastSelectedModels"];
                       self.lastSelectedModels = lastSelectedModels;
                       NSString *pagUrl = [dict objectForKey:@"pagUrl"];
                       self.lastPagUrl = pagUrl;
                   }
               }
           });
       };
       [[IMYURIManager shareURIManager] runActionWithActionObject:actionObj completed:nil];
    }];
   
}

- (BOOL)handleTurnToLogin{
    if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
        return YES;
    }
    @weakify(self);
    NSString *userid = [IMYPublicAppHelper shareAppHelper].userid;
    void(^finishBlock)(void) = ^{
        @strongify(self);
        // 注册用户相同
        if ([userid isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
            [[UIViewController imy_currentViewControlloer] dismissViewControllerAnimated:YES completion:^{
                @strongify(self);
                [self makePagMVWithDownloadPag_step1];
            }];
        } else {
            [self dismissViewControllerAnimated:NO completion:^{
                [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                [[IMYURIManager shareURIManager] runActionWithString:@"home"];
            }];
            
            UIViewController *currentVc = [UIViewController imy_currentViewControlloer];
            Class cls = NSClassFromString(@"IMYBabyMVTemplateContainerVC");
            UIViewController *bottomVC = [self findDismissVcWithClass:cls fromVc:currentVc];
            if (bottomVC) {
                [bottomVC dismissViewControllerAnimated:YES completion:^{
                    [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
                    [[IMYURIManager shareURIManager] runActionWithString:@"home"];
                }];
                
                return ;
            }
        }
    };
    IMYURI *uri = [IMYURI uriWithPath:@"login" params:@{@"finishedBlock": finishBlock} info:nil];
    [[IMYURIManager shareURIManager] runActionWithURI:uri];
    return NO;
}
- (UIViewController *)findDismissVcWithClass:(Class)cls fromVc:(UIViewController *)vc{
    UIViewController *parentVC = vc;
    UIViewController *bottomVC = nil;
    while (parentVC) {
        BOOL hasPhotoSelectVc = NO;
        if ([parentVC isKindOfClass:[UINavigationController class]]) {
            UINavigationController *nav = (UINavigationController *)parentVC;
            hasPhotoSelectVc = [nav.viewControllers bk_match:^BOOL(UIViewController *obj) {
                return [obj isKindOfClass:cls];
            }];
        }else{
            hasPhotoSelectVc = [parentVC isKindOfClass:cls];
        }
        
        if (hasPhotoSelectVc) {
            bottomVC = parentVC.presentingViewController;
            break;
        }
        parentVC = parentVC.presentingViewController;
    }
    return bottomVC;
}
//MARK: - getter

-(IMYTabButtonPageViewController *)pageVC {
    if (!_pageVC) {
        _pageVC = [[IMYTabButtonPageViewController alloc] init];
        _pageVC.view.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [_pageVC.view imy_setBackgroundColorForKey:kCK_Black_F];
        [_pageVC.scrollView imy_setBackgroundColorForKey:kCK_White_AN];
        [_pageVC.pageBarView imy_setBackgroundColorForKey:kCK_White_AN];
        [_pageVC.pageBarView imy_drawTopCornerRadius:12];
        _pageVC.delegate = self;
        _pageVC.dataSource = self;
        _pageVC.scrollViewForwardDelegate = self;
//        _pageVC.isDisableScrollAnimated = NO;
        _pageVC.isProgressScrollAnimated = YES;
        
        _pageVC.barStyle = IMYPageBarStyleProgressView;
        _pageVC.normalTextColor = [UIColor imy_colorForKey:kCK_Black_M];
        _pageVC.selectedTextColor = [UIColor imy_colorForKey:kCK_Red_A];
        _pageVC.normalTextFont = [UIFont imy_regularWith:16];
        _pageVC.selectedTextFont = [UIFont imy_mediumWith:16];
        _pageVC.progressColor = [UIColor imy_colorForKey:kCK_Red_A];
        _pageVC.progressWidth = 20;
        _pageVC.cellSpacing = 24;
        _pageVC.cellEdging = 3;
        [_pageVC.separatorLine imy_setBackgroundColorForKey:kCK_Black_J];
        
        @weakify(self);
        _pageVC.didSelectIndexPathHandler = ^(NSUInteger index) {
            @strongify(self);
            if (self.selectIndex != index) {
                self.selectIndex = index;
            }
        };
    }
    
    return _pageVC;
}

- (IMYBabyMVTemplateViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [IMYBabyMVTemplateViewModel new];
    }
    return _viewModel;
}

- (IMYCaptionView *)captionView {
    if (!_captionView) {
        _captionView = [IMYCaptionView addToView:self.view show:YES];
        _captionView.state = IMYCaptionViewStateLoading;
        [_captionView imy_setBackgroundColorForKey:kCK_Black_F];
        _captionView.imy_top = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
        _captionView.imy_height = _captionView.imy_height - _captionView.imy_top;
        @weakify(self);
        _captionView.retryBlock = ^() {
            @strongify(self);
            [self requestData];
        };
    }
    return _captionView;
}


- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont imy_mediumWith:17];
        _titleLabel.text = IMYString(@"选择MV主题");
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        [_titleLabel imy_setTextColorForKey:kCK_Black_A];
    }
    return _titleLabel;
}
- (IMYTouchEXButton *)backButton {
    if (!_backButton) {
        IMYTouchEXButton *button = [[IMYTouchEXButton alloc] init];
        [button imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
        [button setExtendTouchInsets:UIEdgeInsetsMake(10, 16, 10, 4)];
        
        NSString *backImageName = @"account_top_close_button";
        [button imy_addThemeActionBlock:^(UIButton *weakObject) {
            UIImage *image = [UIImage imy_imageForKey:backImageName];
            [weakObject setImage:image.imy_getNightStyleTopBarImage
                        forState:UIControlStateNormal];
        } forKey:@"setImage"];
        
        [button setExtendTouchAllValue:20];

        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            [self imy_pop:YES];
            //TODO 定位到宝宝首页记录位置
        }];
        _backButton = button;
    }
    return _backButton;
}

- (IMYBabyMVPAGView *)playerView {
    if (!_playerView) {
        CGFloat height = SCREEN_HEIGHT - self.pageVC.view.imy_height - 12 - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - 12 - 72 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        CGFloat width = height * (210.0 / 374.0);
        CGRect frame = CGRectMake((SCREEN_WIDTH - width)/2.0,
                                       SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT + 12,
                                       width,
                                       height);
        
        _playerView = [[IMYBabyMVPAGView alloc] initWithBigPAG:YES];
        _playerView.frame = frame;
        _playerView.userInteractionEnabled = NO;
        [_playerView imy_drawAllCornerRadius:12];
        _playerView.layer.borderColor = [UIColor imy_colorForKey:kCK_Black_G].CGColor;
        _playerView.layer.borderWidth = 1;
//        _playerView.delegate = self;
        @weakify(self);
        
        _playerView.retryBlock = ^{
            @strongify(self);
            [self updatePagPlayer:self.selectTemplateModel];
        };
    }
    return _playerView;
}

//MARK: - GA

/// Description
/// - Parameters:
///   - event: yy_xzmvzty_ljzzan：孕育_选择mv主题页_立即制作按钮   yy_xzmvzty_xzmb：孕育_选择mv主题页_选择模板
///   - info_type: 失败原因，仅失败上报，例如：手动取消、没有找到试纸、最终失败-原因等
///   public_type 额外参数：主题id，服务端下发
///   baby_id 宝宝id
- (void)biReportWithEventName:(NSString *)event public_type:(NSInteger)public_type public_info:(NSInteger)public_info{
    NSDictionary *params = @{@"event":event,
                             @"action":@(2),
                             @"public_type":@(public_type),
                             @"public_info":@(public_info),
                             @"baby_id":@(self.bbj_babyId ? self.bbj_babyId : self.pregnancy_bbj_babyId)
                             };
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}
@end
