//
//  IMYToolsPostpartumPageVC.m
//  IMY_Tools
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/4/15.
//  Copyright © 2021 linggan. All rights reserved.
//

#import "IMYBabyMVTemplatePageVC.h"
#import <IMYBaseKit/IMY_ViewKit.h>
#import "IMYBabyMVTemplateViewModel.h"
#import "IMYBabyMVTemplateCell.h"
#if __has_include(<BBJViewKit/BBJNavBarBottomLine.h>)
#import <BBJViewKit/BBJNavBarBottomLine.h>
#endif

@interface IMYBabyMVTemplatePageVC () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) NSMutableArray *dataList;


@end

@implementation IMYBabyMVTemplatePageVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initData];
    [self initAll];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self reloadUI];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"IMYBabyMVTemplatePageVCMoreThanScreen" object:@(self.collectionView.contentOffset.y > 10) ];
}

- (void)initData {
    self.dataList = self.templateModel.items;
//    [self.templateModel selectTemplateWithModel:self.dataList.firstObject];
}
- (void)initAll {
    [self.view addSubview:self.collectionView];
}
#pragma mark - Method
- (void)reloadUI {
    [self.collectionView reloadData];
}

#pragma mark - delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    if (!scrollView) {
        return;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:@"IMYBabyMVTemplatePageVCMoreThanScreen" object:@(scrollView.contentOffset.y > 10) ];
//    // 去掉下拉阻尼效果
//    if (scrollView.contentOffset.y < 0) {
//        scrollView.contentOffset = CGPointZero;
//    }
}

#pragma mark - UICollectionViewDataSource, UICollectionViewDelegate

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSInteger count = self.dataList.count;
    NSInteger maxRowCount = 3;
    return (count + 2) / maxRowCount * maxRowCount;

}

- (void)collectionView:(UICollectionView *)collectionView willDisplaySupplementaryView:(UICollectionReusableView *)view forElementKind:(NSString *)elementKind atIndexPath:(NSIndexPath *)indexPath {
    if (@available(iOS 11.0, *)) {
        if ([elementKind isEqualToString:UICollectionElementKindSectionHeader]) {
            view.layer.zPosition = 0;
        }
    }
}
- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    if ([kind isEqualToString:UICollectionElementKindSectionFooter]) {
        UICollectionReusableView *footView = [_collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:@"footer" forIndexPath:indexPath];
        return footView;
    }
        
    return nil;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section {
    if (section == self.dataList.count -1 && iPhoneX) {
        return CGSizeMake(0, SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
    }
    return CGSizeZero;
}


- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    IMYBabyMVTemplateCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[IMYBabyMVTemplateCell cellIdentifier] forIndexPath:indexPath];
    
    IMYBabyMVTemplateDetailModel *model;
    if (indexPath.row < self.dataList.count) {
        model = [self.dataList imy_objectAtIndex:indexPath.row];
    }
    if (model) {
        [cell setTemplateModel:model];
        //预加载图片
        [[SDWebImageManager sharedManager] prefetchImageWithURL:[NSURL URLWithString:model.first_cover]];
        return cell;
    } else {
        //返回空白
        [cell setBlankCell];
        return cell;
    }
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    IMYBabyMVTemplateDetailModel *model;
    if (indexPath.row < self.dataList.count) {
        model = [self.dataList imy_objectAtIndex:indexPath.row];
    }
    [self.collectionView reloadData];
    if (!model.isSelect) {
        !self.selectBlock?:self.selectBlock(model);
    }
}

#pragma mark - getter and setter

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        // 以最小间距为10计算间距
        // 每行可放多少 cell
        CGFloat fSpacing = IMYBabyMVTemplatePageVCCellSpace;
        NSInteger nCountCell = 3;
        CGFloat cellWidth = (SCREEN_WIDTH - fSpacing * 4) / 3;
        layout.itemSize = CGSizeMake(cellWidth, cellWidth + 30);//30 标题+间距
        // 平均后的间距
        layout.minimumInteritemSpacing = fSpacing;
        layout.minimumLineSpacing = fSpacing * 2;
        layout.sectionInset = UIEdgeInsetsMake(0, fSpacing, fSpacing/2, fSpacing);

        UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:CGRectMake(0,
                                                                                              0,
                                                                                              SCREEN_WIDTH,
                                                                                              self.view.imy_height)
                                                              collectionViewLayout:layout];
        collectionView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        collectionView.delegate = self;
        collectionView.dataSource = self;
        collectionView.alwaysBounceVertical = YES;
        [collectionView imy_setBackgroundColorForKey:kCK_White_AN];
        collectionView.contentInset = UIEdgeInsetsMake(fSpacing, 0, fSpacing, 0);
        //注册容器视图中显示的方块视图
        [collectionView registerClass:[IMYBabyMVTemplateCell class]
           forCellWithReuseIdentifier:[IMYBabyMVTemplateCell cellIdentifier]];

        //注册容器视图中显示的顶部视图
        [collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:@"footer"];
        
        _collectionView = collectionView;
    }
    return _collectionView;
}

@end
