//
//  IMYToolsPostpartumPageVC.h
//  IMY_Tools
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/4/15.
//  Copyright © 2021 linggan. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <IMYBaseKit/IMYPublic.h>
#import "IMYBabyMVTemplateListModel.h"
#import "IMYPublicBaseViewController.h"
NS_ASSUME_NONNULL_BEGIN

/**
 *  @page 小工具-宝宝MV-page页
 */
static CGFloat const IMYBabyMVTemplatePageVCCellSpace = 8;

@interface IMYBabyMVTemplatePageVC : IMYPublicBaseViewController

@property (nonatomic, weak) IMYBabyMVTemplateModel *templateModel;
@property (nonatomic, copy) void (^selectBlock)(IMYBabyMVTemplateDetailModel *templateModel);//选择模板点击事件
- (void)reloadUI;

@end

NS_ASSUME_NONNULL_END
