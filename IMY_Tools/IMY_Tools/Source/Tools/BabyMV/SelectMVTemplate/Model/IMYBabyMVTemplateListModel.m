//
//  IMYToolsDaduHomeModel.m
//  IMYTools
//
//  Created by ss on 2020/8/11.
//

#import "IMYBabyMVTemplateListModel.h"
#import "NSObject+IMY_YYJSON.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYBabyMVTemplateListModel

+ (NSMutableDictionary<NSString *, id> *)imy_yyjsonPropertyGenericClass {
    return [NSMutableDictionary dictionaryWithDictionary:@{
        @"list": [IMYBabyMVTemplateModel class]
    }];
}

- (NSArray<NSString *> *)titleArray {
    if (!_titleArray) {
        NSMutableArray *titles = [[NSMutableArray alloc] initWithCapacity:self.list.count];
        for (IMYBabyMVTemplateModel *model in self.list) {
            [titles addObject:model.title];
        }
        _titleArray = titles;
    }
    return _titleArray;
}

/// 若first_mv对应id的MV存在首个tab的MV列表中，则将该MV放置在第一位，并定位它
/// - Parameter firt_MV_id: firt_MV_id description
- (void)firstMVSelectId:(NSInteger)firt_MV_id {
    IMYBabyMVTemplateDetailModel *findModel = nil;
    NSMutableArray *firstArray = [[NSMutableArray alloc] initWithCapacity:self.list.firstObject.items.count];

    for (NSInteger index = 0; index < self.list.firstObject.items.count; index++) {
        IMYBabyMVTemplateDetailModel *model = self.list.firstObject.items[index];
        if (model.tid == firt_MV_id) {
            findModel = model;
            
        } else {
            [firstArray addObject:model];
        }
    }
  
    if (findModel) {
        [firstArray insertObject:findModel atIndex:0];
        self.list.firstObject.items = firstArray;
        [self unSelectAllTemplate];
        [self selectTemplateWithModel:findModel andListId:0];
    }

}
/// 设置选中
/// - Parameter detailModel: 模板model
- (void)selectTemplateWithModel:(IMYBabyMVTemplateDetailModel *)detailModel andListId:(NSInteger)cid {
    for (IMYBabyMVTemplateModel *listModel in self.list) {
        for (IMYBabyMVTemplateDetailModel *model in listModel.items) {
            if (model.tid == detailModel.tid) {
                model.isSelect = YES;
            } else {
                model.isSelect = NO;
            }
        }
    }
}

/// 反选
- (void)unSelectAllTemplate {
    for (IMYBabyMVTemplateModel *listModel in self.list) {
        for (IMYBabyMVTemplateDetailModel *model in listModel.items) {
            model.isSelect = NO;
        }
    }
}

/// 获取当前选中的cid
/// - Parameter model: model description
+ (NSInteger)getSelectCid:(IMYBabyMVTemplateListModel *)model {
    NSInteger cid = 0;
    for (IMYBabyMVTemplateModel *listModel in model.list) {
        for (IMYBabyMVTemplateDetailModel *model in listModel.items) {
            if (model.isSelect) {
                cid = listModel.cid;
                return cid;
            }
        }
    }
    return cid;
}

@end

@implementation IMYBabyMVTemplateModel

+(void)initialize {
    [self bindYYJSONKey:@"id" toProperty:@"cid"];
}

+ (NSMutableDictionary<NSString *, id> *)imy_yyjsonPropertyGenericClass {
    return [NSMutableDictionary dictionaryWithDictionary:@{
        @"items" : [IMYBabyMVTemplateDetailModel class]
    }];
}

@end

@implementation IMYBabyMVMixMaterialModel

+ (NSMutableDictionary<NSString *, id> *)imy_yyjsonPropertyGenericClass {
    return [NSMutableDictionary dictionaryWithDictionary:@{
        @"medias" : [IMYBabyMVMixMaterialAssetModel class]
    }];
}

@end

@implementation IMYBabyMVMixMaterialAssetModel

@end

@implementation IMYBabyMVTemplateBabyFollowerModel

@end

@implementation IMYBabyMVTemplateDetailModel

+(void)initialize {
    [self bindYYJSONKey:@"id" toProperty:@"tid"];
}

- (BOOL)isReverseEditOrder {
    if (self.photo_sort_type == 2) {
        return YES;
    } else {
        return NO;
    }
}
@end

@implementation IMYBabyMVTemplateForMaterialModel

+(void)initialize {
    [self bindYYJSONKey:@"template" toProperty:@"tModel"];
    [self bindYYJSONKey:@"materials_mix" toProperty:@"materialsMix"];
    [self bindYYJSONKey:@"template_text_type" toProperty:@"templateTextType"];
}

+ (NSMutableDictionary<NSString *, id> *)imy_yyjsonPropertyGenericClass {
    return [NSMutableDictionary dictionaryWithDictionary:@{
        @"tModel" : [IMYBabyMVTemplateDetailModel class],
        @"materialsMix": [IMYBabyMVMixMaterialModel class]
    }];
}

/// 展示文本列表
- (NSArray<NSString *> *)displayTextList:(BOOL)isAiCaption {
    if (!isAiCaption) {
        return [self.materialsMix.texts copy];
    }
    return [self displayTextsWithMaterials:self.materialsMix.medias];
}

- (NSArray<NSString *> *)displayTextsWithMaterials:(NSArray <IMYBabyMVMixMaterialAssetModel *>*)materials {
    if (!materials || materials.count == 0) {
        return @[];
    }
    NSInteger materialNum = materials.count;
    NSMutableArray *texts = [NSMutableArray array];
    if (self.templateTextType == 1) {
        /// 宝宝天数倒序
        texts = [NSMutableArray arrayWithCapacity:materialNum];
        for (NSInteger i = 1; i <= materialNum; i++) {
            NSString *title = materials[materialNum - i].title;
            if (imy_isBlankString(title)) {
                title = @"";
            }
            [texts addObject:title];
        }
    } else if (self.templateTextType == 2 || self.templateTextType == 3) {
        /// 2、宝宝天数和文案倒序＋日期
        /// 3、日期＋宝宝天数和文案倒序
        texts = [NSMutableArray arrayWithCapacity:2 * materialNum + 1];
        for (NSInteger i = 1; i <= materialNum; i++) {
            NSString *title = materials[materialNum - i].title;
            if (imy_isBlankString(title)) {
                title = @"";
            }
            [texts addObject:title];
            NSString *content = materials[materialNum - i].gen_text;
            if (imy_isBlankString(content)) {
                content = @"";
            }
            [texts addObject:content];
        }
        /// 日期范围
        if (self.templateTextType == 2) {
            [texts addObject:self.materialsMix.period_text];
        } else {
            [texts insertObject:self.materialsMix.period_text atIndex:0];
        }
    }
    texts = [texts reverseObjectEnumerator].allObjects;
    return [texts copy];
}

@end
