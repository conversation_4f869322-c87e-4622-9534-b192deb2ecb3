// IMYToolsDaduHomeModel.h

#import <Foundation/Foundation.h>
#import "BBJAssetModel.h"

@class IMYBabyMVTemplateModel;
@class IMYBabyMVTemplateDetailModel;
@class IMYBabyMVTemplateBabyFollowerModel;
@class IMYBabyMVMixMaterialModel;
@class IMYBabyMVMixMaterialAssetModel;

NS_ASSUME_NONNULL_BEGIN
/// 接口地址：https://apidoc.seeyouyima.com/doc/65151642089728211a1beb7a
#pragma mark - Object interfaces

@interface IMYBabyMVTemplateListModel : NSObject

@property (nonatomic, copy) NSArray<IMYBabyMVTemplateModel *> *list; ///< 模板集合
@property (nonatomic, copy) NSArray<NSString *> *titleArray; ///< 类别总数标题集合
@property (nonatomic, strong) IMYBabyMVTemplateBabyFollowerModel *baby_follower; ///< 宝宝权限

/// 设置选中
/// - Parameter detailModel: 模板model
- (void)selectTemplateWithModel:(IMYBabyMVTemplateDetailModel *)detailModel andListId:(NSInteger)cid;

/// 反选
- (void)unSelectAllTemplate;

/// 若first_mv对应id的MV存在首个tab的MV列表中，则将该MV放置在第一位，并定位它
/// - Parameter firt_MV_id: firt_MV_id description
- (void)firstMVSelectId:(NSInteger)firt_MV_id;
/// 获取当前选中的cid
/// - Parameter model: model description
+ (NSInteger)getSelectCid:(IMYBabyMVTemplateListModel *)model;

@end

@interface IMYBabyMVTemplateModel : NSObject

@property (nonatomic, assign) NSInteger cid; ///< 分类id
@property (nonatomic, copy) NSString *title; ///< 模板名称
@property (nonatomic, copy) NSArray<IMYBabyMVTemplateDetailModel *> *items; ///< 模板数组

@end

@interface IMYBabyMVTemplateDetailModel : NSObject

@property (nonatomic, assign) NSInteger tid; ///< 模板id
@property (nonatomic, copy) NSString *title; ///< 模板名称
@property (nonatomic, copy) NSString *first_cover; ///< 首帧封面图
@property (nonatomic, copy) NSString *grid_cover; ///< 小格封面图
@property (nonatomic, assign) NSInteger material_num; ///< 素材张数
@property (nonatomic, copy) NSString *file_url; ///< 配置文件
@property (nonatomic, copy) NSString *local_pag_file_path; ///< 本地pag文件
@property (nonatomic, assign) NSInteger photo_sort_type; ///< 1 反序， 2 正序
@property (nonatomic, assign) NSInteger support_video; ///< 是否支持视频 1 支持
@property (nonatomic, assign) BOOL isSelect; ///< 是否选中
@property (nonatomic, assign) BOOL isReverseEditOrder; ///< 是否反序

@end

@interface IMYBabyMVTemplateForMaterialModel : NSObject

@property (nonatomic, strong) IMYBabyMVTemplateDetailModel *tModel; ///< 模板
@property (nonatomic, strong) IMYBabyMVTemplateBabyFollowerModel *baby_follower; ///< 混合素材
@property (nonatomic, strong) IMYBabyMVMixMaterialModel *materialsMix; ///< 混合素材
@property (nonatomic, assign) NSInteger templateTextType; ///< 文本类型, 0无文本，1宝宝天数倒序，2宝宝天数和文案倒序+日期，3日期+宝宝天数和文案倒序

/// 展示文本列表
- (NSArray<NSString *> *)displayTextList:(BOOL)isAiCaption;
- (NSArray<NSString *> *)displayTextsWithMaterials:(NSArray <IMYBabyMVMixMaterialAssetModel *>*)materials;

@end

@interface IMYBabyMVMixMaterialModel : NSObject

@property (nonatomic, copy) NSArray<IMYBabyMVMixMaterialAssetModel *> *medias; ///< 素材数组
@property (nonatomic, copy) NSArray<NSString *> *texts; ///< 文本数组
@property (nonatomic, copy) NSString *period_text; ///< 周期

@end

@interface IMYBabyMVMixMaterialAssetModel : NSObject

@property (nonatomic, assign) NSInteger type; ///< 图片=1、视频=2
@property (nonatomic, copy) NSString *url; ///< 链接
@property (nonatomic, copy) NSString *cover; ///< 封面链接
@property (nonatomic, copy) NSString *title; ///< 日期标题
@property (nonatomic, copy) NSString *content; ///< 记录文案
@property (nonatomic, copy) NSString *gen_text; ///< AI生成文案
@property (nonatomic, assign) CGFloat duration; ///< 单位 秒
/// 本地存储使用
@property (nonatomic, strong) UIImage *image; ///< 图片
@property (nonatomic, strong) UIImage *coverImage; ///< 封面图片
@property (nonatomic, copy) NSString *localFilePath; ///< 本地视频文件路径

@end

@interface IMYBabyMVTemplateBabyFollowerModel : NSObject

@property (nonatomic, assign) BOOL can_record; ///< 是否能够记录
@property (nonatomic, assign) BOOL can_share; ///< 是否能分享和下载
@property (nonatomic, assign) BOOL can_invite; ///< 是否能邀请
@property (nonatomic, assign) BOOL can_view_before; ///< 是否能查看出生前记录 true=是
@property (nonatomic, assign) BOOL can_poke; ///< 戳一下

@end

NS_ASSUME_NONNULL_END
