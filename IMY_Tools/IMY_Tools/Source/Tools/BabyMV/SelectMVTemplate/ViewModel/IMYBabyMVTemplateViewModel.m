//
//  IMYToolsPostpartumViewModel.m
//  IMY_Tools
//
//  Created by meiyou on 2021/4/8.
//  Copyright © 2021 linggan. All rights reserved.
//

#import "IMYBabyMVTemplateViewModel.h"
#import <IMYBaseKit/IMYPublic.h>
#import "IMYBabyMVTemplateListModel.h"
#import "IMYRecordBabyManager.h"
#import "IMYRecordPregnancyBabyManager.h"
@interface IMYBabyMVTemplateViewModel ()

@property (nonatomic, strong) NSArray *titles;
@property (nonatomic, assign) NSInteger defaultIndexDay;

@end

@implementation IMYBabyMVTemplateViewModel

#pragma mark - Method


#pragma mark - 请求数据

/// 请求模板数据
/// - Parameter completeBlock: <#completeBlock description#>
- (void)requestDataWithBabyId:(NSInteger)babyId first_MV_Id:(NSInteger)first_MV_Id completeBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateListModel *model))completeBlock {
    @weakify(self);
    [[IMYServerRequest getPath:[NSString stringWithFormat:@"v3/mv_make/template2?common_baby_id=%ld&first_mv_id=%ld",babyId,first_MV_Id] host:api_bbj_meiyou_com params:nil headers:nil] subscribeNext:^(id<IMYHTTPResponse> x) {
        @strongify(self);
        NSDictionary *dic = x.responseObject;
        IMYBabyMVTemplateListModel *model = [self parseToObjectWithResponseAllData:dic];
        imy_asyncMainBlock(^{
            !completeBlock?:completeBlock(YES, self.changeModel);
        });
    } error:^(NSError *error) {
        imy_asyncMainBlock(^{
            !completeBlock?:completeBlock(NO, self.changeModel);
        });

    }];
}

     
- (IMYBabyMVTemplateListModel*)parseToObjectWithResponseAllData:(id)responseData {
    ///1.产后变化
    IMYBabyMVTemplateListModel *changeModel = [responseData toModel:[IMYBabyMVTemplateListModel class]];
    self.changeModel = changeModel;
    return changeModel;
}


/// 类别总数
- (NSInteger)categoryCount {
    return [self.changeModel.titleArray count];
}
//获取列表标题名称
- (NSString *)categoryTitleWithIndex:(NSInteger)index {
    return [self.changeModel.titleArray imy_objectAtIndex:index];
}
/// 类别model
- (IMYBabyMVTemplateModel *)categoryModelWithIndex:(NSInteger)index {
    return [self.changeModel.list imy_objectAtIndex:index];
}


#pragma mark - 请求宝宝记 图片数量+视频数量
+ (void)requestBabyPicCountDataWithBabyId:(NSInteger)babyId completeBlock:(void (^)(BOOL bSuccess, NSInteger picCount, NSString *header, NSString *nickname, NSString *birthday))completeBlock {
    @weakify(self);
    [[IMYServerRequest getPath:@"v3/baby/record/amount" host:api_bbj_meiyou_com params:@{@"baby_id":@(babyId)} headers:nil] subscribeNext:^(id<IMYHTTPResponse> x) {
        @strongify(self);
        NSDictionary *dic = x.responseObject;
        NSInteger picCount = [[dic objectForKey:@"pic_count"] integerValue];
        NSInteger videoCount = [[dic objectForKey:@"video_count"] integerValue];
        NSDictionary *babyInfo = [dic objectForKey:@"baby_info"];
        NSString *header;
        NSString *nickname;
        NSString *birthday;
        if (babyInfo && [babyInfo isKindOfClass:[NSDictionary class]]) {
            header = [babyInfo objectForKey:@"header"];
            nickname = [babyInfo objectForKey:@"nickname"];
            birthday = [babyInfo objectForKey:@"birthday"];
        }
        imy_asyncMainBlock(^{
            !completeBlock?:completeBlock(YES, picCount+videoCount, header, nickname, birthday);
        });
    } error:^(NSError *error) {
        imy_asyncMainBlock(^{
            !completeBlock?:completeBlock(NO, 0, nil, nil, nil);
        });
    }];
}

///获取当前美柚 宝宝 id
+ (NSInteger)getCurrCommonBabyId{
    NSInteger commonBabyId = 0;
    commonBabyId = [IMYPublicAppHelper shareAppHelper].currentBabyID;
    if (commonBabyId == 0) {
#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
        commonBabyId = [[IMYRecordBabyManager sharedInstance] currentBaby].baby_id;
#endif
    }
    if (commonBabyId == 0) {  //数据异常 。兜底
        NSNumber *commonBabayId = [[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"check/yunyuHome/babyVC_V2_babyId" params:nil];
        commonBabyId = [commonBabayId integerValue];
    }
    if (commonBabyId == 0) {
#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
        commonBabyId = [[IMYRecordBabyManager sharedInstance] lastBirthdayBaby].baby_id;
#endif
    }
    return commonBabyId;
}
@end


