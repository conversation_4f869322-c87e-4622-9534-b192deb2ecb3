//
//  IMYToolsPostpartumViewModel.h
//  IMY_Tools
//
//  Created by meiyo<PERSON> on 2021/4/8.
//  Copyright © 2021 linggan. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "IMYBabyMVTemplateListModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYBabyMVTemplateViewModel : NSObject

/// 获取mv模板
@property (nonatomic, strong) IMYBabyMVTemplateListModel *changeModel;

- (void)requestDataWithBabyId:(NSInteger)babyId first_MV_Id:(NSInteger)first_MV_Id completeBlock:(void (^)(BOOL bSuccess, IMYBabyMVTemplateListModel *model))completeBlock ;

/// 请求宝宝图片记录总数 & 宝宝头像 昵称
+ (void)requestBabyPicCountDataWithBabyId:(NSInteger)babyId completeBlock:(void (^)(BOOL bSuccess, NSInteger picCount, NSString *header, NSString *nickname, NSString *birthday))completeBlock;
/// 类别总数
- (NSInteger)categoryCount;

//获取列表标题名称
- (NSString *)categoryTitleWithIndex:(NSInteger)index;

/// 类别model
- (IMYBabyMVTemplateModel *)categoryModelWithIndex:(NSInteger)index;

///获取当前美柚 宝宝 id
+ (NSInteger)getCurrCommonBabyId;
@end

NS_ASSUME_NONNULL_END
