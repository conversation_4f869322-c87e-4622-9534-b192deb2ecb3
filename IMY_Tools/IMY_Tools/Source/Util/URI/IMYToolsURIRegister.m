//
//  IMYToolsURIRegister.m
//  IMY_Tools
//
//  Created by TR on 3/29/16.
//  Copyright © 2016 linggan. All rights reserved.
//

#import "IMYToolsURIRegister.h"
#import "IMYToolsUserService.h"
#import <IMYYQBasicServices/IMYToolsURIDefine.h>
#import "IMYDueDateCaculateVC.h"
#import "IMYToolModel.h"
#import "IMYToolsBUltrasonicExplainItemVC.h"
#import "IMYToolsBUltrasonicExplainVC.h"
#import "IMYToolsCategoryVC.h"
#import "IMYToolsChildbirthBagContainerVC.h"
#import "IMYToolsChildbirthBagContainerVC_v2.h"
#import "IMYToolsChildbirthBagListVC.h"
#import "IMYToolsContractionsCountingVC.h"
#import "IMYToolsDailyStandVC.h"
#import "IMYToolsEatDoActInfoVC.h"
#import "IMYToolsEatDoBanActModel.h"
#import "IMYToolsEatDoBanFoodModel.h"
#import "IMYToolsEatDoCanDoVC.h"
#import "IMYToolsEatDoFoodInfoVC.h"
#import "IMYToolsEatDoFoodInfoVC_v3.h"
#import "IMYToolsEatDoSearchActVC.h"
#import "IMYToolsEatDoSearchFoodVC.h"
#import "IMYToolsEncyclopodiaListVC.h"
#import "IMYToolsMainFMCountingVC.h"
#import "IMYToolsMyMensesManager.h"
#import "IMYToolsMyMensesRecordVC.h"
#import "IMYToolsOvulateVC.h"
#import "IMYToolsSegmentWebViewController.h"
#import "IMYToolsVaccinumVC.h"
#import <IMYBaseKit/IMYABTestManager.h>
#import <IMYYQBasicServices/IMYToolsBUltrasonicIndex+Helper.h>
#import <IMYYQBasicServices/IMYToolsEventHelper.h>
#import <IMYYQBasicServices/IMYToolsStrategyManager.h>
#import "IMYToolsDaDuHomeVC.h"
#import "IMYToolsDaduAllPhotosViewController.h"
#import "IMYToolsDaduPhotoPickerViewController.h"
//#import "IMYToolsVideoViewController.h"
#import "IMYToolsABTestManager.h"
#import "IMYToolsCanEatViewController.h"
#import "IMYToolsPostpartumVC.h"
#import "IMYToolsWikiDetailVC.h"
#import "IMYOvulateTestPaperSelectVC.h"
#import "IMYBabyMVTemplateContainerVC.h"
#import "IMYRecordPregnancyBabyManager.h"
#import "IMYBabyMakeWeekMVViewController.h"
#import "IMYRecordSummaryActionManager.h"
#import "IMYRecordBabyManager.h"
#import "BBJBabyCacheManager.h"
#import "BBJBabyMVPriviewController.h"
#import "IMYWikiDetailDialogView.h"
#import "IMYToolsOvulateManager.h"

#define IMYToolsURLOvulateHelp [NSString stringWithFormat:@"%@/help/test_paper_new.html", view_seeyouyima_com]
#define IMYToolsURLOvulateExample [NSString stringWithFormat:@"%@/help/testPaper_example_new.html", view_seeyouyima_com]
#define IMYToolsURLOvulateTech [NSString stringWithFormat:@"%@/help/testPaper_skills_new.html", view_seeyouyima_com]

@implementation IMYToolsURIRegister

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    [IMYToolsURIRegister registTools];
}

IMY_KYLIN_FUNC_METHOD_SWIZZLE {  //注册 host
    [IMYURLEnvironmentManager registHost:@"link.meiyou.com"];
}


+ (void)registTools {
    
    [[IMYURIManager shareURIManager] addForPath:@"tools/wikiDetail/dialog" level:99 withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger entryId = [[actionObject.uri.params objectForKey:@"entryId"] integerValue];
        NSInteger articleId = [[actionObject.uri.params objectForKey:@"articleId"] integerValue];
        id actionBlock = [actionObject.uri.params objectForKey:@"bottomAction"];
        UIView * onView = [actionObject.uri.params objectForKey:@"onView"];
        [IMYWikiDetailDialogView showOn:onView entryId:entryId articleId:articleId bottomActionBlock:actionBlock animation:true];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"holiday/mv/preview/push" level:99 withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger mvId = [[actionObject.uri.params objectForKey:@"mv_id"] integerValue];
        NSString* btnUri = [actionObject.uri.params objectForKey:@"button_uri"];
        NSString* btnTitle = [actionObject.uri.params objectForKey:@"button"];
        
        /// 不存在，则不进行跳转
        if (!mvId) {
            return;
        }
        
        IMYRecordBabyModel *babyModel = [IMYRecordBabyManager sharedInstance].currentBaby;
        BBJBabyMVPriviewController * vc = [[BBJBabyMVPriviewController alloc] init];
        vc.mvId = mvId;
        vc.btnUri = btnUri;
        vc.btnTitle = btnTitle;
        vc.babyId = babyModel.bbj_baby_id;
        vc.commonBabyId = babyModel.baby_id;
        vc.fromURI = actionObject.uri;
        UIViewController *currentVc = [UIViewController imy_currentViewControlloer];
        [currentVc imy_present:vc];
    }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIVaccinum （tools/vaccinum）
    /// @brief  小工具-宝宝MV
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/baby/makeMV"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYBabyMVTemplateContainerVC *vc = [IMYBabyMVTemplateContainerVC new];
                                    if (actionObject.uri.params[@"first_mv"]) {
                                        vc.first_MV_Id = [actionObject.uri.params[@"first_mv"] integerValue];
                                    }
                                    if (actionObject.uri.params[@"first_mv_id"]) {
                                        vc.first_MV_Id = [actionObject.uri.params[@"first_mv_id"] integerValue];
                                    }
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_present:vc];
                                }];
    
    /// @uri_start
    ///
    /// @name   IMYToolsURIVaccinum （tools/vaccinum）
    /// @brief  小工具-宝宝MV 怀孕胎宝宝
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/pregnancy/baby/makeMV"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYBabyMVTemplateContainerVC *vc = [IMYBabyMVTemplateContainerVC new];
                                    vc.bbj_babyId = [IMYRecordPregnancyBabyManager sharedInstance].bbj_baby_id;
                                    vc.common_babyId = [IMYRecordPregnancyBabyManager sharedInstance].common_baby_id;
                                    if (actionObject.uri.params[@"first_mv_id"]) {
                                        vc.first_MV_Id = [actionObject.uri.params[@"first_mv_id"] integerValue];
                                    }
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_present:vc];
                                }];
    
    /// @uri_start
    ///
    /// @name   IMYToolsURIVaccinum （tools/vaccinum）
    /// @brief  节假日mv编辑界面协议
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"holiday/mv/edit"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYBabyMakeWeekMVViewController *vc = [IMYBabyMakeWeekMVViewController new];
                                    [vc imy_setPropertyWithDictionary:actionObject.uri.params];
                                    vc.fromURI = actionObject.uri;
                                    if ([actionObject.uri.params[@"isPregnancy"] boolValue]) {//怀孕宝宝id
                                        vc.baby_id = [IMYRecordPregnancyBabyManager sharedInstance].bbj_baby_id;
                                    }
        
                                    [[actionObject getUsingViewController] imy_present:vc];
                                }];
    /// @uri_start
    ///
    /// @name   IMYToolsURIVaccinum （tools/vaccinum）
    /// @brief  节假日mv编辑界面协议  用于推送
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"holiday/mv/edit/push"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    BOOL gotoMakeMV = NO;
                                    IMYRecordBabyModel *babyModel = [IMYRecordBabyManager sharedInstance].currentBaby;
                                    if (!babyModel) {
                                        gotoMakeMV = YES;
                                    }
                                    if (gotoMakeMV) {
                                        [[IMYURIManager shareURIManager] runActionWithString:@"tools/pregnancy/baby/makeMV"];
                                        return;
                                    }
                                
                                    IMYBabyMakeWeekMVViewController *vc = [IMYBabyMakeWeekMVViewController new];
                                    vc.fromURI = actionObject.uri;
                                    vc.baby_id = babyModel.bbj_baby_id;
                                    vc.mv_id = [actionObject.uri.params[@"mv_id"] integerValue];
                                    [[actionObject getUsingViewController] imy_present:vc];
                                }];


    /// @uri_start
    ///
    /// @name   IMYToolsURIVaccinum （tools/vaccinum）
    /// @brief  小工具-宝宝疫苗主界面
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIVaccinum
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsVaccinumVC *vc = [IMYToolsVaccinumVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIOvulate(@"tools/ovulate")
    /// @brief  排卵试纸页
    ///
    /// 参数
    /// @param scrollToDate  NSDate#对象# 页面初始显示的日期，如不在经期区间，则显示最后一页
    /// @param showCloseButton  bool#yes# 显示关闭按钮
    /// @param addOvulate  block#block# 回调，添加了今天的试纸
    /// @param deleteOvulate  block#block# 回调，删除的试纸时间(NSDate数组)
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIOvulate
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsOvulateVC *vc = [IMYToolsOvulateVC new];
                                    vc.addOvulate = actionObject.uri.params[@"addOvulate"];
                                    vc.deleteOvulate = actionObject.uri.params[@"deleteOvulate"];
                                    vc.moveOvulateDate = actionObject.uri.params[@"moveOvulateDate"];
                                    vc.changeOvulateState = actionObject.uri.params[@"changeOvulateState"];
                                    vc.scrollToDate = actionObject.uri.params[@"scrollToDate"];
                                    vc.showCloseButton = [actionObject.uri.params[@"showCloseButton"] boolValue];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
        
        
        if (!actionObject.uri.params[@"addOvulate"]) {
            //【【ios】【备孕模式】预发环境--添加排卵试纸，分析页和会员tab未展示"已更新"标签】
            //https://www.tapd.meiyou.com/21039721/bugtrace/bugs/view/1121039721001280266
            @weakify(self);
            void (^addOvulate)(NSDate *) = ^(NSDate *date) {
                @strongify(self);
                // 通知授权弹窗：记录-排卵试纸
                [[IMYURIManager shareURIManager] runActionWithPath:@"push/checkPushSettings" params:@{
                    @"position" : @(IMYNABoxShowPositionRecord_pailuanshizhi)
                } info:nil];
                
                //记录首页，排卵试纸的弹泡
                UIViewController *topVC = [UIViewController imy_currentTopViewController];
                NSArray<UIViewController *> *viewControllers = topVC.navigationController.viewControllers;
                [viewControllers enumerateObjectsUsingBlock:^(UIViewController *vc, NSUInteger idx, BOOL * _Nonnull stop) {
                    if ([vc isKindOfClass:NSClassFromString(@"IMYToolsOvulateVC")]) {
                        void (^viewDidDisappearBlock)(void) = ^(void) {
                            NSMutableDictionary *info = [[NSMutableDictionary alloc] init];
                            info[@"type"] = @"Add";
                            if ([IMYToolsOvulateManager sharedManager].modifyList.count > 0) {
                                IMYToolsOvulateModel *lastModel = [IMYToolsOvulateManager sharedManager].modifyList.lastObject;
                                info = @{@"value": @([lastModel recordBannerSubType])};
                            }
                            [[IMYRecordSummaryActionManager sharedInstance] addActionWithType:IMYRecordSummaryActionTypeOvulationTest date:date info:info];
                            [[IMYToolsOvulateManager sharedManager].modifyList removeAllObjects];
                        };
                        [vc setValue:viewDidDisappearBlock forKey:@"viewDidDisappearBlock"];
                        *stop = YES;
                    }
                }];
            };
            vc.addOvulate = addOvulate;
            return;
        }
        
                                }];
    
    /// @uri_start
    ///
    /// @name   tools/ovulateTextPaperSelect
    /// @brief  排卵试纸 选择页 （智能识别、手动拍摄 、相册）
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/ovulateTestPaperSelect"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYOvulateTestPaperSelectVC *vc = [IMYOvulateTestPaperSelectVC new];
        vc.hidesBottomBarWhenPushed = YES;
        vc.fromURI = actionObject.uri;
        IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:vc];
        [nav.navigationBar setHidden:YES];
        UIViewController *currentVc = [UIViewController imy_currentTopViewController];
        [currentVc presentViewController:nav animated:YES completion:nil];
    }];
    
    

    /// @uri_start
    ///
    /// @name   IMYToolsURIOvulateHelp(@"tools/ovulate/help")
    /// @brief  排卵试纸-帮助说明
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIOvulateHelp
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYEventHelper event:@"plsz-bzsm"]; // 排卵试纸-帮助说明
        [IMYEventHelper event:@"plsz_bz"];
        NSArray *titles = @[IMYString(@"帮助"), IMYString(@"示例"), IMYString(@"技巧")];
        NSArray *urls = @[IMYToolsURLOvulateHelp, IMYToolsURLOvulateExample, IMYToolsURLOvulateTech];
        NSInteger curentIndex = [actionObject.uri.params[@"curentIndex"] integerValue];
        IMYToolsSegmentWebViewController *vc = [[IMYToolsSegmentWebViewController alloc] initWithTitles:titles urls:urls curentIndex:curentIndex];
        vc.fromURI = actionObject.uri;
        BOOL isPresent = [actionObject.uri.params[@"isPresent"] boolValue];
        if (isPresent) {
            [vc setToolsSegNavTopLeftButton];
            [[actionObject getUsingViewController] imy_present:vc];
        } else {
            [[actionObject getUsingViewController] imy_push:vc];
        }
    }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIFMCount（@"tools/fmCount"）
    /// @brief  数胎动
    ///
    /// 参数
    /// @param unReq_bi  bool#yes#是否调用IMYToolsEventHelper reqToolsEventTrack:方法
    /// @param isBIRequested  bool#yes#是否为上报埋点请求； YES，最总会上报埋点
    /// @param toolsID_BI  NSString#‘1’# 1-首页；2-工具页面；3-其它页面；4-工具tab；5-美柚工具tab(tools_id为工具名)；9-记录孕期工具
    /// @param sourceType  int#yes# 工具id @see IMYToolsEventTrackSource
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIFMCount
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    if (![actionObject.uri.params[@"unReq_bi"] boolValue]) {
                                        [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    }
                                    IMYToolsMainFMCountingVC *vc = [IMYToolsMainFMCountingVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [vc imy_setPropertyWithDictionary:actionObject.uri.params];
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];
    
    /// @uri_start
    ///
    /// @name   tools/taixin
    /// @brief  小工具-测胎心页
    ///
    /// 参数
    /// @param unReq_bi  bool#yes#是否调用IMYToolsEventHelper reqToolsEventTrack:方法
    /// @param isBIRequested  bool#yes#是否为上报埋点请求； YES，最总会上报埋点
    /// @param toolsID_BI  NSString#‘1’# 1-首页；2-工具页面；3-其它页面；4-工具tab；5-美柚工具tab(tools_id为工具名)；9-记录孕期工具
    /// @param sourceType  int#yes# 工具id @see IMYToolsEventTrackSource
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/taixin"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    if (![actionObject.uri.params[@"unReq_bi"] boolValue]) {
                                        [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    }
                                    IMYToolsMainFMCountingVC *vc = [IMYToolsMainFMCountingVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    vc.isFromTaixin = YES;
                                    [vc imy_setPropertyWithDictionary:actionObject.uri.params];
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];
    /// @uri_start
    ///
    /// @name   widget/tools/ucCount
    /// @brief  小组件 点击 数宫缩
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"widget/tools/ucCount"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    UIViewController *curVC = [UIViewController imy_currentViewControlloer];
                                    if ([curVC isKindOfClass:[IMYToolsContractionsCountingVC class]]) {
                                        return;//当前就在  数宫缩 页面。
                                    }
                                    if (IMYPublicAppHelper.shareAppHelper.userMode != IMYVKUserModePregnancy) {
                                        return;//非怀孕身份。不跳转
                                     }
                                    NSInteger sub_type = [IMYRightsSDK sharedInstance].currentSubscribeType;
                                    NSDictionary *param = @{@"event": @"dy_zmxzjsy", @"action": @(2), @"public_type": @"gongsuo_01",@"subscribe_type":@(sub_type),@"public_info":@"数宫缩"};
                                    [IMYGAEventHelper postWithPath:@"event" params:param headers:nil completed:nil];
        
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsContractionsCountingVC *vc = [IMYToolsContractionsCountingVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];
    /// @uri_start
    ///
    /// @name   IMYToolsURIUCCount(@"tools/ucCount")
    /// @brief  数宫缩
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIUCCount
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsContractionsCountingVC *vc = [IMYToolsContractionsCountingVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];
    /// @uri_start
    ///
    /// @name   IMYToolsURICanEat (@"tools/canEat")
    /// @brief  能不能吃
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURICanEat
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYToolsEventHelper reqToolsEventTrack:actionObject];
        IMYToolsCanEatViewController *vc = [IMYToolsCanEatViewController new];
        vc.hidesBottomBarWhenPushed = YES;
        vc.fromURI = actionObject.uri;
        [[actionObject getUsingViewController] imy_push:vc];
    }];
    /// @uri_start
    ///
    /// @name   IMYToolsURICanEatDetail（tools/canEatDetail）
    /// @brief  能不能吃详情
    ///
    /// 参数
    /// @param foodId  NSInteger#12# id
    /// @param title  NSString#山药# 标题
    ///
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURICanEatDetail
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    NSInteger foodID = [actionObject.uri.params[@"foodId"] integerValue];
                                    NSString *title = actionObject.uri.params[@"title"];

                                    IMYToolsEatDoFoodInfoVC_v3 *vc =  [[IMYToolsEatDoFoodInfoVC_v3 alloc] initWithFoodId:foodID AndTitle:title];

                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURICanDoDetail(@"tools/canDoDetail")
    /// @brief  能不能做详情
    ///
    /// 参数
    /// @param actId  NSInteger#12# id
    /// @param title  NSString#烫头发# 名称
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURICanDoDetail
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    NSInteger actId = [actionObject.uri.params[@"actId"] integerValue];
                                    NSString *title = actionObject.uri.params[@"title"];
                                    IMYToolsEatDoActInfoVC *vc = [[IMYToolsEatDoActInfoVC alloc] initWithActId:actId AndTitle:title];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURICanDo(@"tools/canDo")
    /// @brief  能不能做
    ///
    /// 参数
    /// @param act  IMYToolsEatDoBanActModel#对象# @seeIMYToolsEatDoBanActModel
    /// @param isBIRequested  bool#yes#是否为上报埋点请求； YES，最总会上报埋点
    /// @param toolsID_BI  NSString#‘1’# 1-首页；2-工具页面；3-其它页面；4-工具tab；5-美柚工具tab(tools_id为工具名)；9-记录孕期工具
    /// @param sourceType  int#yes# 工具id @see IMYToolsEventTrackSource
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURICanDo
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsEatDoBanActModel *act = actionObject.uri.params[@"act"];
                                    IMYToolsEatDoCanDoVC *vc = [[IMYToolsEatDoCanDoVC alloc] init];
                                    vc.insertAct = act;
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURICanEatOrDoSearch(@"tools/canEatOrDoSearch")
    /// @brief  能不能吃/做 搜索
    ///
    /// 参数
    /// @param type  int#类型# 0：能不能吃搜索界面；1：能不能做搜索界面；
    /// @param keyword  参数类型#NSString# 关键词
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURICanEatOrDoSearch
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    NSInteger type = [actionObject.uri.params[@"type"] integerValue];
                                    NSString *keyword = actionObject.uri.params[@"keyword"];
                                    IMYToolsEatDoForbidListVC *vc = !type ? [[IMYToolsEatDoSearchFoodVC alloc] init] : [[IMYToolsEatDoSearchActVC alloc] init];
                                    vc.requestWord = keyword;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIBUExplain(@"tools/buExplain")
    /// @brief  B 超单解读
    ///
    /// 参数
    /// @param pregnantStartDate  NSDate#对象#怀孕开始日，预产期无效时，才会用到
    /// @param currentDate  NSDate#对象#外部传入的日期，自动跳转至该日期的周数
    ///
    /// @param isBIRequested  bool#yes#是否为上报埋点请求； YES，最总会上报埋点
    /// @param toolsID_BI  NSString#‘1’# 1-首页；2-工具页面；3-其它页面；4-工具tab；5-美柚工具tab(tools_id为工具名)；9-记录孕期工具
    /// @param sourceType  int#yes# 工具id @see IMYToolsEventTrackSource
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIBUExplain
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsBUltrasonicExplainVC *vc = [[IMYToolsBUltrasonicExplainVC alloc] init];
                                    vc.pregnantStartDate = actionObject.uri.params[@"pregnantStartDate"];
                                    vc.currentDate = actionObject.uri.params[@"currentDate"];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIBUWeekExplain(@"tools/buWeekExplain")
    /// @brief  孕周 B 超单解读
    ///
    /// 参数
    /// @param week  int#6# 孕周，取值范围6~40
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIBUWeekExplain
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    NSArray *keyArray = [actionObject.uri.params allKeys];
                                    IMYToolsBUltrasonicExplainVC *vc;
                                    if ([keyArray containsObject:@"week"]) {
                                        NSInteger week = [actionObject.uri.params[@"week"] integerValue];
                                        vc = [[IMYToolsBUltrasonicExplainVC alloc] initWithWeek:week];
                                    } else {
                                        vc = [[IMYToolsBUltrasonicExplainVC alloc] init];
                                    }
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIBUItemExplain (tools/buItem)
    /// @brief   B 超单项目
    ///
    /// 参数
    /// @param week  int#6# 周数，6~40
    /// @param itemKey  int#1# 项目 key
    /// @param canEdit  bool#YES# YES/NO，是否可编辑
    /// @param id  int#1# 项目id
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIBUItemExplain
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    NSInteger week = [actionObject.uri.params[@"week"] integerValue];
                                    NSString *key = actionObject.uri.params[@"itemKey"];
                                    BOOL canEdit = [actionObject.uri.params[@"canEdit"] boolValue];
                                    NSNumber *itemId = actionObject.uri.params[@"id"];

                                    canEdit = canEdit && [IMYToolsUserService isYunQi];

                                    IMYToolsBUltrasonicIndex *item;
                                    if (week == 0) {
                                        item = [IMYToolsBUltrasonicIndex getRecordByKey:key];
                                    } else {
                                        item = [IMYToolsBUltrasonicIndex getRecordByWeek:week andKey:key];
                                    }
                                    if (item != nil) {
                                        IMYToolsBUltrasonicExplainItemVC *vc = [[IMYToolsBUltrasonicExplainItemVC alloc] initWithItem:item];
                                        [vc setIsCanInput:canEdit];
                                        vc.fromURI = actionObject.uri;
                                        [[actionObject getUsingViewController] imy_push:vc];
                                    } else if (itemId != nil) {
                                        IMYToolsBUltrasonicExplainItemVC *vc = [[IMYToolsBUltrasonicExplainItemVC alloc] initWithItemId:itemId];
                                        [vc setIsCanInput:canEdit];
                                        vc.fromURI = actionObject.uri;
                                        [[actionObject getUsingViewController] imy_push:vc];
                                    } else {
                                        [UIWindow imy_showTextHUD:IMYString(@"当前孕周无该项目 B 超单解读数据")];
                                    }
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIDueDateCalculate (tools/dueDateCalculate)
    /// @brief  计算预产期页
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIDueDateCalculate
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYDueDateCaculateVC *vc = [IMYDueDateCaculateVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIBoyOrGirlTools (tools/boyOrGirlTools)
    /// @brief  生男生女，工具合集
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIBoyOrGirlTools
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsCategoryVC *vc = [[IMYToolsCategoryVC alloc] initWithType:IMYToolsCategoryTypeBoyOrGirl];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIWebTool (tools/webTools)
    /// @brief  web 小工具
    ///
    /// 参数
    /// @param model  IMYToolModel#对象# @see IMYToolModel
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIWebTool
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolModel *model = actionObject.uri.params[@"model"];

                                    NSString *infoString = @"";
                                    if ([IMYToolsUserService userMode] == IMYVKUserModePregnancy) {
                                        ///怀孕天数
                                        NSInteger dayIndex = [IMYToolsUserService pregnantDays];
                                        infoString = [NSString stringWithFormat:@"info=%ld", (long)dayIndex];
                                        [self toolEventWithWebId:model.toolId];
                                    } else if ([IMYToolsUserService userMode] == IMYVKUserModeForPregnant) {
                                        TLPeriodType type; //当前时期
                                        NSInteger day;     //当前时期来了多少天
                                        [IMYToolsUserService currentPeriodType:&type days:&day];
                                        infoString = [NSString stringWithFormat:@"info=%zd,%ld", type, (long)day];
                                    } else if ([IMYToolsUserService userMode] == IMYVKUserModeLama) {
                                        //宝宝辅食需要添加age字段，固此处统一添加，后端已经上线，固两个参数一起传
                                        NSInteger dayIndex = [IMYToolsUserService lamaDays];
                                        infoString = [NSString stringWithFormat:@"info=%ld", (long)dayIndex];
                                    }

                                    NSString *paramsString = [NSString stringWithFormat:@"mode=%zd&%@&imy_share=%zd", [IMYToolsUserService userMode], infoString, model.canShare];
                                    NSString *urlString = [NSString stringWithFormat:@"%@?%@", model.webToolURL, paramsString];

                                    IMYVKWebViewController *vc = [IMYVKWebViewController webWithURLString:urlString];
                                    if ([model.title containsString:IMYString(@"胎动看男女")]) {
                                        [vc.imy_topRightButton imy_setImage:@"all_topmore"];
                                        vc.imy_topRightButton.rac_command = [[RACCommand alloc] initWithSignalBlock:^RACSignal *(id input) {
                                            [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"tools/share" params:@{@"model": model, @"shareUrl": urlString} info:nil]];
                                            return [RACSignal empty];
                                        }];
                                    } else {
                                        vc.showCloseButton = YES;
                                    }
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURINewWebTool (tools/webNewTools)
    /// @brief  web 小工具:分享本地不做设置，直接走协议
    ///
    /// 参数
    /// @param webToolURL  NSString#http链接#网页地址
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURINewWebTool
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    NSString *webUrl = actionObject.uri.params[@"webToolURL"];

                                    if (imy_isEmptyString(webUrl)) {
                                        return;
                                    }

                                    NSString *infoString = @"";
                                    if ([IMYToolsUserService userMode] == IMYVKUserModePregnancy) {
                                        ///怀孕天数
                                        NSInteger dayIndex = [IMYToolsUserService pregnantDays];
                                        infoString = [NSString stringWithFormat:@"info=%ld", (long)dayIndex];
                                    } else if ([IMYToolsUserService userMode] == IMYVKUserModeForPregnant) {
                                        TLPeriodType type; //当前时期
                                        NSInteger day;     //当前时期来了多少天
                                        [IMYToolsUserService currentPeriodType:&type days:&day];
                                        infoString = [NSString stringWithFormat:@"info=%zd,%ld", type, (long)day];
                                    } else if ([IMYToolsUserService userMode] == IMYVKUserModeLama) {
                                        NSInteger dayIndex = [IMYToolsUserService lamaDays];
                                        NSInteger motherIndex = [IMYToolsUserService getBabyMother];
                                        infoString = [NSString stringWithFormat:@"info=%ld&age=%ld", (long)dayIndex, (long)motherIndex];
                                    }

                                    NSString *paramsString = [NSString stringWithFormat:@"mode=%zd&%@", [IMYToolsUserService userMode], infoString];
                                    NSString *urlString = [NSString stringWithFormat:@"%@?%@", webUrl, paramsString];

                                    IMYVKWebViewController *vc = [IMYVKWebViewController webWithURLString:urlString];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIWebToolForPregnant (tools/webToolsForPregnant)
    /// @brief  web 小工具，备孕
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIWebToolForPregnant
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsCategoryVC *vc = [[IMYToolsCategoryVC alloc] initWithType:IMYToolsCategoryTypeForPregnant];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIWebToolPregnancy (tools/webToolsPregnancy)
    /// @brief  web 小工具，怀孕
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIWebToolPregnancy
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsCategoryVC *vc = [[IMYToolsCategoryVC alloc] initWithType:IMYToolsCategoryTypePregnancy];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIWebToolLama (tools/webToolsLama)
    /// @brief  web 小工具，辣妈
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIWebToolLama
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsCategoryVC *vc = [[IMYToolsCategoryVC alloc] initWithType:IMYToolsCategoryTypeLama];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIDailyVote (tools/dailyVote)
    /// @brief  每日站站队(每日测一测)
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIDailyVote
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsDailyStandVC *vc = [IMYToolsDailyStandVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIChildbirthBag (tools/childbirthBag)
    /// @brief  待产包
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIChildbirthBag
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];

                                    UIViewController *chlidbirthBagVC = nil;
                                    if ([IMYToolsStrategyManager isNewChildBag]) {
                                        IMYToolsChildbirthBagContainerVC_v2 *vc = [[IMYToolsChildbirthBagContainerVC_v2 alloc] init];
                                        vc.fromURI = actionObject.uri;
                                        chlidbirthBagVC = vc;
                                    } else {
                                        IMYToolsChildbirthBagContainerVC *vc = [[IMYToolsChildbirthBagContainerVC alloc] init];
                                        vc.fromURI = actionObject.uri;
                                        chlidbirthBagVC = vc;
                                    }
                                    [[actionObject getUsingViewController] imy_push:chlidbirthBagVC];
                                    // ABTest点击上报
                                    [IMYToolsStrategyManager childbirthBagClick];
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIChildbirthBagList (tools/childbirthBagList)
    /// @brief  待产包选择列表
    ///
    /// 参数
    /// @param selectCallback  block#回调# 选择的待产包列表
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIChildbirthBagList
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsChildbirthBagListVC *vc = [[IMYToolsChildbirthBagListVC alloc] init];
                                    vc.selectCallback = actionObject.uri.params[@"selectCallback"];
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   tools/menses/update
    /// @brief  同步记经期
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/menses/update"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [[IMYToolsMyMensesManager shareManager] updateAndDownloadData];
                                }];

    /// @uri_start
    ///
    /// @name   recordmenstrual
    /// @brief  同步记经期
    ///
    /// 参数
    /// @param callback  block#回调# 时期变化回调
    /// @param isFromOvulate  bool#YES# 是否是从排卵试纸push进
    /// @param currentVC  vc#控制器# 可选，当前控制器
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"recordmenstrual"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsMyMensesRecordVC *vc = [[IMYToolsMyMensesRecordVC alloc] init];
                                    id callback = actionObject.uri.params[@"callback"];
                                    id isFromOvulate = actionObject.uri.params[@"isFromOvulate"];
                                    if (callback) {
                                        vc.periodChangeForOvulateBlock = callback;
                                    }
                                    if (isFromOvulate) {
                                        [vc setValue:isFromOvulate forKey:@"isFromOvulate"];
                                    }
                                    vc.fromURI = actionObject.uri;
                                    UIViewController *currentVC = actionObject.uri.params[@"currentVC"];
                                    if (!currentVC) {
                                        currentVC = [actionObject getUsingViewController];
                                        [currentVC imy_push:vc];
                                    } else {
                                        [currentVC imy_push:vc];
                                    }
                                }];

    /// @uri_start
    ///
    /// @name   IMYToolsURIEncyclopedia (tools/encyclopedia)
    /// @brief  百科小工具
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIEncyclopedia
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsEncyclopodiaListVC *vc = [IMYToolsEncyclopodiaListVC new];
                                    vc.hidesBottomBarWhenPushed = YES;
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];

    /// @uri_start
    ///
    /// @name   tools/encyclopedia/detail
    /// @brief  百科详情
    ///
    /// 参数
    /// @param entry_id  int#1# 入口id
    /// @param article_id  int#1# 文章id
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/encyclopedia/detail"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsWikiDetailVC *vc = [IMYToolsWikiDetailVC new];
                                    vc.fromURI = actionObject.uri;
                                    vc.entry_id = [actionObject.uri.params[@"entry_id"] integerValue];
                                    vc.article_id = [actionObject.uri.params[@"article_id"] integerValue];
                                    [[actionObject getUsingViewController] imy_push:vc];
                                }];
    
    /*
     Flutter 接好孕
     https://www.tapd.cn/21039721/prong/stories/view/1121039721001053043?url_cache_key=1b3a312ae86288a482458549a60d7d88&action_entry_type=story_tree_list
     */
    /// @uri_start
    ///
    /// @name   tools/goodPregnancy
    /// @brief  Flutter 接好孕，内部只是上报埋点
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:@"tools/goodPregnancy"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    actionObject.hasStop = NO;
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
    }];
    
    /// @uri_start
    ///
    /// @name   IMYToolsURIDaduHome (record/newdadu/home)
    /// @brief  大肚照
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIDaduHome
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    IMYToolsDaDuHomeVC *vc = [IMYToolsDaDuHomeVC new];
                                    [[actionObject getUsingViewController] imy_push:vc];
    }];
    
    /// @uri_start
    ///
    /// @name   IMYToolsURIDaduPhotoPicker (tools/DaduPhotoPicker)
    /// @brief  大肚照-784照片选择页，
    ///
    /// 参数
    /// @param maxCount  int#9# 最大可选择照片数
    /// @param position  string#3#  3 妈妈变化，4 大肚照记录项
    /// @param addPhotoCallBack  blcok#回调# 选择照片回调
    ///
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIDaduPhotoPicker
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYGAEventHelper postWithPath:@"/event" params:@{@"event":@"ddz_tjzp" ,@"action":@2} headers:nil completed:nil];
                                    IMYToolsDaduPhotoPickerViewController *vc = [IMYToolsDaduPhotoPickerViewController new];
                                    [vc imy_setPropertyWithDictionary:actionObject.uri.params];
                                    NSInteger maxCount = [[actionObject.uri.params objectForKey:@"maxCount"] integerValue];
                                    if (maxCount > 0) {
                                        vc.maxCount = maxCount;
                                    }
                                    [[actionObject getUsingViewController] imy_present:vc];
    }];
    
    /// @uri_start
    ///
    /// @name   tools/IMYToolsURIDaduAllPicture
    /// @brief  大肚照-全部照片页面
    ///
    /// 参数
    /// @param week  NSInteger#1# 必填，-2：历史孕照；-1不指定孕周，展示全部照片；0～41：展示对应孕周照片
    /// @param selectStyle  NSInteger#1# 1: 进入图片选取状态 0:图片浏览常态
    /// @param selectedArray  NSArray#数组# 已选中图片ID的集合  eg: 大肚照制作视频页跳转
    /// @param isShowPeriod  bool#yes# 是否显示孕周 默认YES 用于 编辑页
    /// @param selectedPhotosBlock  block#回调block# 是制作视频点击完成Block ;非制作视频进入无需添加
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIDaduAllPicture
                                        withActionBlock:^(IMYURIActionBlockObject *actionObject) {        
                                    IMYToolsDaduAllPhotosViewController *vc = [IMYToolsDaduAllPhotosViewController new];
                                    [vc imy_setPropertyWithDictionary:actionObject.uri.params];
                                    [[actionObject getUsingViewController] imy_push:vc];
    }];
        
    /// @uri_start
    ///
    /// @name   IMYToolsURIPostpartum (tools/postRecovery)
    /// @brief   产后恢复
    ///
    /// 参数
    /// @param index  int#1# 用于外部指定跳转天数
    ///
    /// @uri_end
    [[IMYURIManager shareURIManager] addForPath:IMYToolsURIPostpartum
                                        withActionBlock:^(IMYURIActionBlockObject *actionObject) {
                                    [IMYToolsEventHelper reqToolsEventTrack:actionObject];
                                    IMYToolsPostpartumVC *vc = [IMYToolsPostpartumVC new];
                                    [vc imy_setPropertyWithDictionary:actionObject.uri.params];
                                    vc.fromURI = actionObject.uri;
                                    [[actionObject getUsingViewController] imy_push:vc];
    }];
}


+ (void)toolEventWithWebId:(NSInteger)webId {
    NSString *event = @"";
    if (webId == 68) { // 胎动看男女
        event = @"snsn-tdk";
    } else if (webId == 53) { // 唐氏
        event = @"snsn-ts";
    } else if (webId == 67) { // 胎梦解析
        event = @"snsn-tm";
    } else if (webId == 61) { // 孕妈体重指南
        event = @"yqgj-ymtz";
    } else if (webId == 57) { // 宝宝血型计算器
        event = @"yqgj-bbxx";
    } else if (webId == 56) { // 宝宝体重
        event = @"yqgj-bbtz";
    } else if (webId == 62) { // 宝宝身高
        event = @"yqgj-bbsg";
    }
    [IMYEventHelper event:event];
}

@end
