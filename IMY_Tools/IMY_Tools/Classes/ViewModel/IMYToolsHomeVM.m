//
//  IMYToolsHomeVM.m
//  IMYDemo
//
//  Created by zcf on 1/4/16.
//  Copyright © 2016 MU. All rights reserved.
//

#import "IMYToolsHomeVM.h"
#import "IMYSimpleCellModel.h"
#import <IMYYQBasicServices/IMYToolsURIDefine.h>

static NSString *const kCellTitleVaccine = @"宝宝疫苗";               // IMYToolsVaccinumVC // @"xiyou://vaccinum"
static NSString *const kCellTitleGravidityCheck = @"产检报告";        // IMYToolsGravidityCheckDetailVC   // @"xiyou://chanjian"
static NSString *const kCellTitleOvulate = @"排卵试纸";               // IMYToolsOvulateVC // @"xiyou://Ovulate"
static NSString *const kCellTitleCanEat = @"能不能吃";                // SYCanEatVC   // @"xiyou://caneat"
static NSString *const kCellTitleCanDo = @"能不能做";                 // SYCanDoVC    // @"xiyou://cando"
static NSString *const kCellTitleFMCount = @"数胎动";                 // fetal movement   // IMYToolsMainFMCountingVC // @"xiyou://taidong"
static NSString *const kCellTitleUCCount = @"数宫缩";                 // uterine contraction  // IMYToolsContractionsCountingVC // @"xiyou://gongsuo"
static NSString *const kCellTitleBUltrasonicExplain = @"B超单解读";   // IMYToolsBUltrasonicExplainVC   // @"xiyou://bscan" // @"http://tools.seeyouyima.com/front/bscan/index.htm.php"
static NSString *const kCellTitleDueDateCalculate = @"计算预产期";    // IMYDueDateCaculateVC // @"xiyou://calculation"
static NSString *const kCellTitleBabayIsBoyOrGirlTools = @"生男生女"; // SYBoyOrGirlVC // xiyou://boyorgirl
static NSString *const kCellTitleWebToolsForPregnant = @"备孕工具";
static NSString *const kCellTitleWebToolsPregnancy = @"孕期工具";
static NSString *const kCellTitleWebToolsLama = @"育儿工具";
static NSString *const kCellTitleChildbirthBag = @"待产包"; // IMYToolsChildbirthBagContainerVC // xiyou://musthave
static NSString *const kCellTitleDoctor = @"春雨医生";      // IMYDoctorHistoryDiagnosticListVC // xiyou://chunyuDoc
static NSString *const kCellTitleEncyclopodia = @"百科小工具";
static NSString *const kCellTitleMenses = @"记经期";
static NSString *const kCellTitleSleepHelper = @"睡眠小工具";

typedef NS_ENUM(NSUInteger, IMYToolHomeVCCellType) {
    IMYToolHomeVCCellTypeVaccinue,
    IMYToolHomeVCCellTypeDoctor,
    IMYToolHomeVCCellTypeGravidity,
    IMYToolHomeVCCellTypeOvulate,
    IMYToolHomeVCCellTypeCanEat,
    IMYToolHomeVCCellTypeCanDo,
    IMYToolHomeVCCellTypeFMCount,
    IMYToolHomeVCCellTypeUCCount,
    IMYToolHomeVCCellTypeBUltrasonicExplain,
    IMYToolHomeVCCellTypeDueDateCalculate,
    IMYToolHomeVCCellTypeBabayIsBoyOrGirlTools,
    IMYToolHomeVCCellTypeWebToolsForPregnant,
    IMYToolHomeVCCellTypeWebToolsPregnancy,
    IMYToolHomeVCCellTypeWebToolsForLama,
    IMYToolHomeVCCellTypeChildbirthBag,
    IMYToolHomeVCCellTypeMenses,
    IMYToolHomeVCCellTypeEncyclopodia,
    IMYToolHomeVCCellTypeSleep,
};

@implementation IMYToolsHomeVM
- (instancetype)init {
    if (self = [super init]) {
        [self initData];
    }
    return self;
}

- (void)initData {
    self.dataArray = [NSMutableArray new];

    NSArray *titleArray = @[kCellTitleVaccine, kCellTitleDoctor, kCellTitleGravidityCheck, kCellTitleOvulate, kCellTitleCanEat, kCellTitleCanDo, kCellTitleFMCount, kCellTitleUCCount, kCellTitleBUltrasonicExplain, kCellTitleDueDateCalculate, kCellTitleBabayIsBoyOrGirlTools, kCellTitleWebToolsForPregnant, kCellTitleWebToolsPregnancy, kCellTitleWebToolsLama, kCellTitleChildbirthBag, kCellTitleMenses, kCellTitleEncyclopodia, kCellTitleSleepHelper];

    for (NSUInteger i = 0; i < titleArray.count; i++) {
        NSString *title = [titleArray objectAtIndex:i];
        IMYSimpleCellModel *model = [IMYSimpleCellModel modelWithTitle:title content:nil];
        model.type = IMYToolHomeVCCellTypeVaccinue + i;
        model.onCellDidPressed = ^(IMYSimpleCellModel *model, id cell) {
            NSString *URI;
            NSDictionary *param;
            NSDictionary *info;
            switch (model.type) {
                case IMYToolHomeVCCellTypeVaccinue: {
                    URI = IMYToolsURIVaccinum;
                } break;

                case IMYToolHomeVCCellTypeGravidity: {
                    URI = IMYToolsURIGravidityCheck;
                } break;

                case IMYToolHomeVCCellTypeOvulate: {
                    URI = IMYToolsURIOvulate;
                } break;

                case IMYToolHomeVCCellTypeCanEat: {
                    URI = IMYToolsURICanEat;
                } break;

                case IMYToolHomeVCCellTypeCanDo: {
                    URI = IMYToolsURICanDo;
                } break;

                case IMYToolHomeVCCellTypeFMCount: {
                    URI = IMYToolsURIFMCount;
                } break;

                case IMYToolHomeVCCellTypeUCCount: {
                    URI = IMYToolsURIUCCount;
                } break;

                case IMYToolHomeVCCellTypeBUltrasonicExplain: {
                    URI = IMYToolsURIBUExplain;
                } break;

                case IMYToolHomeVCCellTypeDueDateCalculate: {
                    URI = IMYToolsURIDueDateCalculate;
                } break;

                case IMYToolHomeVCCellTypeBabayIsBoyOrGirlTools: {
                    URI = IMYToolsURIBoyOrGirlTools;
                } break;
                case IMYToolHomeVCCellTypeWebToolsForPregnant: {
                    URI = IMYToolsURIWebToolForPregnant;
                } break;
                case IMYToolHomeVCCellTypeWebToolsPregnancy: {
                    URI = IMYToolsURIWebToolPregnancy;
                } break;
                case IMYToolHomeVCCellTypeWebToolsForLama: {
                    URI = IMYToolsURIWebToolLama;
                } break;
                case IMYToolHomeVCCellTypeChildbirthBag: {
                    URI = IMYToolsURIChildbirthBag;
                } break;
                case IMYToolHomeVCCellTypeDoctor: {
                    URI = IMYToolsURIDoctor;
                    //					URI = @"tools/doctor/chat";
                } break;
                case IMYToolHomeVCCellTypeMenses: {
                    URI = @"recordmenstrual";
                } break;
                case IMYToolHomeVCCellTypeEncyclopodia: {
                    URI = IMYToolsURIEncyclopedia;
                } break;
                case IMYToolHomeVCCellTypeSleep: {
                    URI = @"tools/sleep";
                } break;
            }
            [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:URI params:param info:info]];
        };
        [self.dataArray addObject:model];
    }
}
@end
