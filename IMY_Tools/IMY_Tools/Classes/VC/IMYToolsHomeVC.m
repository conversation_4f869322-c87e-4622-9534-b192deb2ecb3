//
//  IMYToolsHomeVC.h
//  IMYDemo
//
//  Created by zcf on 1/4/16.
//  Copyright © 2016 MU. All rights reserved.
//

#import "IMYToolsHomeVC.h"
#import "IMYSimpleTableView.h"
#import "IMYToolsHomeVM.h"
#import "IMYToolsVaccinumManager.h"
#import "SYGravidityCheckManager.h"
#import <IMYEZipArchive.h>

@interface IMYToolsHomeVC ()
@property (nonatomic, strong) IMYSimpleTableView *tableView;
@property (nonatomic, strong) IMYToolsHomeVM *vm;
@end

@implementation IMYToolsHomeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self setupTableView];
    
    [[IMYToolsVaccinumManager sharedManager] getVaccinumNotifyList];
//    [[SYGravidityCheckManager sharedManager] getGCNotifyList];
   
    [self.imy_topRightButton imy_setTitle:@"夜间主题" state:UIControlStateNormal];
    [[self.imy_topRightButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        
        NSBundle *bundle = [NSBundle mainBundle];
        NSString *path = [bundle pathForResource:@"nightheme" ofType:@"zip"];
        NSString *unzipPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        unzipPath = [unzipPath stringByAppendingPathComponent:@"themes"];
        
        NSFileManager *fileM = [NSFileManager defaultManager];
        
        if (![fileM fileExistsAtPath:unzipPath isDirectory:nil]) {
            if (path && path.length) {
                [IMYEZipArchive unzipFileAtPath:path toDestination:unzipPath overwrite:NO];
            }
        }
        unzipPath = [unzipPath stringByAppendingPathComponent:@"nightheme"];
        if ([[IMYThemeManager sharedIMYThemeManager].themePath isEqualToString:unzipPath]) {
            [IMYThemeManager sharedIMYThemeManager].themePath = nil;
        } else {
            [IMYThemeManager sharedIMYThemeManager].themePath = unzipPath;
        }
        
    }];
    
}

- (void)setupTableView{
    self.tableView = [[IMYSimpleTableView alloc] initWithFrame:self.view.bounds];
    [self.tableView imy_makeTransparent];
    self.tableView.backgroundColor = IMY_COLOR_KEY(kIMY_BG);
    [self.view addSubview:self.tableView];
    
    [self.tableView setSimpleDataSource:self.vm.dataArray];
}

#pragma mark - setter && getter
- (IMYToolsHomeVM *)vm{
    if (!_vm) {
        _vm = [IMYToolsHomeVM new];
    }
    return _vm;
}
@end
