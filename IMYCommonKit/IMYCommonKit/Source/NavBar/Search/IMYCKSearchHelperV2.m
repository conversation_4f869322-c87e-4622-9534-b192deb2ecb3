//
//  IMYCKSearchHelperV2.m
//  IMYCommonKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/31.
//

#import "IMYCKSearchHelperV2.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYCKDynamicSearchBar.h"

@interface IMYCKSearchHelperV2 ()

@property (nonatomic, assign) NSInteger show_style;//1.推荐词 2.固定文案
@property (nonatomic, assign) NSInteger recycleSecond;//推荐词轮询时间 default:8秒
@property (atomic, copy) NSDictionary *searchSuggestWords;//推荐词内容
@property (nonatomic, assign) BOOL isViewActived;//是否处于激活状态
@property (nonatomic, assign) BOOL shouldLoadWord;//是否可展示搜索推荐词
@property (nonatomic, assign) BOOL haveLoadSearchSuggest;//是否已加载了搜索推荐词(viewActive时)
@property (nonatomic, strong) NSTimer *searchTimer;

// 8.93.0版本：存储完整的head_search_words对象数组和当前轮播的对象
@property (nonatomic, copy) NSArray<NSDictionary *> *headSearchWordsArray; // 完整的对象数组
@property (nonatomic, copy) NSDictionary *currentSearchWordObject; // 当前轮播显示的完整对象

@end

@implementation IMYCKSearchHelperV2


- (void)releaseTimer {
    if (self.searchTimer) {
        [self.searchTimer invalidate];
        self.searchTimer = nil;
    }
}

- (instancetype)init {
    if (self = [super init]) {
        [self releaseTimer];
        self.posId = 10; //默认为首页
        self.recycleSecond = 8;
        self.shouldLoadWord = YES;
        self.searchViewVisiable = YES;
        [self addNotification];
    }
    return self;
}

- (instancetype)initWithSearchLabel:(UILabel *)searchLabel viewController:(nonnull UIViewController *)viewController fromType:(IMYCKSearchFromType)fromType {
    if (self = [self init]) {
        self.searchLabel = searchLabel;
        self.currentVC = viewController;
        self.fromType = fromType;
    }
    return self;
}

- (void)setFromType:(IMYCKSearchFromType const)fromType {
    if (fromType == _fromType) {
        return;
    }
    _fromType = fromType;
    if (fromType == IMYCKSearchFromTypeHome && [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        [self pregnancyDelayLoadSuggestSearchWord];
    }
    [self setDefaultSearchPlaceholderText];
}

- (void)addNotification {
    @weakify(self);
    ///增加激活监听，调切换搜索关键词和定时器
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:IMYPublicBaseViewController.IMYViewControllerDidActiveChangedNotification object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *_Nonnull note) {
        @strongify(self);
        id vc = note.object;
        if (self.currentVC != nil && vc == self.currentVC) {
            self.isViewActived = self.currentVC.isViewActived;
            if (self.searchTimer.isValid) { //有定时器 就停止轮询
                [self.searchTimer setFireDate:[NSDate distantFuture]];
            }
            if (self.isViewActived && self.show_style == 1) {
                imy_asyncMainBlock(0.5, ^{ //重新曝光搜索框时延迟0.5秒切换搜索框词
                    [self switchSearchText];
                });
                if (self.searchTimer != nil) {
                    if (self.searchTimer.isValid) { //如果有定时器，8秒后开始
                        [self.searchTimer setFireDate:[NSDate dateWithTimeIntervalSinceNow:self.recycleSecond]];
                    }
                } else {//没定时器，创建
                    [self handleSearchSuggestWords];
                }
            }
            if (self.isViewActived && !self.haveLoadSearchSuggest) { //启动时的初始化搜索接口放到viewActive在执行
                [self refreshSearchSuggestWords];
                self.haveLoadSearchSuggest = YES;
            }
        }
    }];
}

- (void)refreshSearchSuggestWords {
    @weakify(self);
    IMYURI *uri = [IMYURI uriWithPath:@"circles/updateSearchSuggestWords"
                               params:@{@"from": @(self.fromType)}
                                 info:nil];
    IMYURIActionBlockObject *actionObject = [IMYURIActionBlockObject actionBlockWithURI:uri];
    actionObject.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
        @strongify(self);
        NSLog(@"[search debug][nav search][请求接口]");
        @synchronized (self) {
            id oldObj = self.searchSuggestWords;
            self.searchSuggestWords = result;
            imy_asyncMainBlock(1, ^{
                [oldObj class];
            });
        }
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            @synchronized (self) {
                if (self.shouldLoadWord) {
                    [self handleSearchSuggestWords];
                }
            }
        });
    };
    [[IMYURIManager shareURIManager] runActionWithActionObject:actionObject completed:nil];
}

- (void)switchSearchText {
    if (!self.isViewActived || self.show_style != 1) {
        NSLog(@"[searchHelper2][switchSearchText][return]");
        return;
    }
    NSArray *keys = self.searchWords;
    if (!keys || keys.count == 0 || !self.headSearchWordsArray || self.headSearchWordsArray.count == 0) {
        [self setDefaultSearchPlaceholderText];
        return;
    }
    UILabel * const searchLabel = self.searchLabel;
    NSUInteger index = [keys indexOfObject:searchLabel.text ?: @""];
    if (index == NSNotFound) {
        index = 0;
    } else {
        index++;
    }
    if (index >= keys.count) {
        index = 0;
    }
    NSString *searchText = [keys imy_objectAtIndex:index];
    if (!imy_isEmptyString(searchText)) {
        // 8.93.0版本：同时更新当前轮播的完整对象
        if (index < self.headSearchWordsArray.count) {
            self.currentSearchWordObject = self.headSearchWordsArray[index];
        }

        [self gaPostSearchExposureWithSearchText:searchText];
        [self setText:searchText animation:YES];
    }
}

- (void)searchAction {
    [self searchAction:NO];
}

- (void)searchAction:(BOOL)isSearchBar {
    UILabel * const searchLabel = self.searchLabel;
    NSString *placeholder = searchLabel.text;
    
    // 底纹词点击上报
    if (self.show_style == 1 && !imy_isEmptyString(placeholder)) {
        [self gaPostSearchClickWithSearchText:placeholder];
    }
    
    if (isSearchBar) {
        // 8.93.0版本：检查当前轮播词是否有scheme_uri，如果有则直接跳转
        NSDictionary *currentSearchWordObject = self.currentSearchWordObject;
        NSString *schemeURI = nil;

        if ([currentSearchWordObject isKindOfClass:[NSDictionary class]]) {
            NSString *uri = currentSearchWordObject[@"scheme_uri"];
            if ([uri isKindOfClass:[NSString class]] && uri.length > 0) {
                schemeURI = uri;
            }
        }

        if (schemeURI) {
            // 有scheme_uri，在跳转前保存搜索历史记录
            [self saveSearchHistoryBeforeSchemeJump:currentSearchWordObject];

            // 执行跳转
            [[IMYURIManager shareURIManager] runActionWithString:schemeURI];
            return;
        }
    }

    NSMutableDictionary *dic = [@{@"from": @(self.fromType),
                                  @"pos_id": @(self.posId),
                                  @"shouldLoadCache": @(YES),
                                  } mutableCopy];
    BOOL isAdSearch = self.adSearchKeywords.count;
    if (!imy_isEmptyString(placeholder)) {
        if (isAdSearch) {
            dic[@"searchPlaceholder"] = [self adSearchKeyWithShowKey:placeholder];
            dic[@"keyword"] = dic[@"searchPlaceholder"];
        } else {
            // 8.93.0版本：传递完整的搜索词对象而非只是关键词
            if (self.currentSearchWordObject) {
                dic[@"searchPlaceholder"] = self.currentSearchWordObject;
            } else {
                dic[@"searchPlaceholder"] = placeholder;
            }
        }
    }
    // 首页和她她圈的搜索框推荐词，可以直接搜索
    // https://www.tapd.cn/34674824/prong/tasks/view/1134674824001100106
    if (self.show_style == 1) {
        [dic addEntriesFromDictionary:@{
            @"isRecommendWordAndSearch": @(YES),
            @"isRecommendWord": @(YES),
        }];
    }
    if (isAdSearch) {
        dic[@"showAssociateWithKey"] = @YES;
    }
    if (self.searchAppendParams) {
        NSDictionary *appendParams = self.searchAppendParams();
        [dic addEntriesFromDictionary:appendParams];
    }
    
    IMYCKBaseSearchBar *searchBar = [searchLabel imy_findParentViewWithClass:IMYCKBaseSearchBar.class];
    if (!searchBar.hidden && searchBar.window && searchBar.imy_width > 100 && searchBar.imy_height > 30) {
        CGRect searchBarFrame = [searchBar convertRect:searchBar.bounds toView:searchBar.window];
        dic[@"befromBarFrame"] = NSStringFromCGRect(searchBarFrame);
    }
    
    [[IMYURIManager shareURIManager] runActionWithPath:@"circles/search" params:dic info:nil];
    
    /// 点击之后就置空广告的
    self.adSearchKeywords = nil;
}

#pragma mark - PrivateMethod

- (void)handleSearchSuggestWords {
    self.show_style = [[self.searchSuggestWords valueForKey:@"show_style"] intValue];

    // 8.93.0版本：存储完整的head_search_words对象数组
    NSArray *headSearchWords = [self.searchSuggestWords valueForKey:@"head_search_words"];
    if ([headSearchWords isKindOfClass:[NSArray class]]) {
        self.headSearchWordsArray = headSearchWords;
    }

    // 下发推荐搜索词
    if (self.show_style == 1) {
        @weakify(self);
        if (self.isViewActived) {
            [self releaseTimer];
            self.searchTimer = [NSTimer bk_scheduledTimerWithTimeInterval:self.recycleSecond
                                                                    block:^(NSTimer *timer) {
                @strongify(self);
                NSLog(@"[searchHelper2][do timer]");
                [self switchSearchText];
            } repeats:YES];
        }
    }
    NSArray *keys = self.searchWords;
    NSString *searchText = [keys.firstObject copy];
    if (imy_isNotEmptyString(searchText)) {
        // 8.93.0版本：设置当前轮播的完整对象
        if (self.headSearchWordsArray.count > 0) {
            self.currentSearchWordObject = self.headSearchWordsArray.firstObject;
        }

        if (self.show_style == 1) {//推荐词才上报
            [self gaPostSearchExposureWithSearchText:searchText];
            [self setText:searchText animation:YES];
        } else {
            [self setText:searchText animation:NO];
        }
    } else {
        [self setDefaultSearchPlaceholderText];
    }
}

- (void)setDefaultSearchPlaceholderText {
    [self setText:self.defaultSearchPlaceholder animation:NO];
}

- (void)setText:(NSString *)searchText animation:(BOOL)animation {
    if (searchText && ![searchText isKindOfClass:NSString.class]) {
        NSAssert(NO, @"无效参数 %@", searchText);
        searchText = nil;
    }
    UILabel * const searchLabel = self.searchLabel;
    if (searchLabel && ![searchText isEqualToString:searchLabel.text]) {
        [searchLabel.layer removeAllAnimations];
        [searchLabel setText:searchText];
        if (searchLabel.window && animation) {
            CATransition *anima = [CATransition animation];
            anima.type = kCATransitionPush;
            anima.subtype = kCATransitionFromTop;
            anima.duration = .45f;
            [searchLabel.layer addAnimation:anima forKey:@"moveInAnimation"];
        }

        // 8.93.0版本：触发IMYCKDynamicSearchBar的图标更新
        [self triggerSearchBarIconUpdate];
    }
}

/// 曝光埋点
- (void)gaPostSearchExposureWithSearchText:(NSString *)searchText {
    BOOL isSearchOutOfWindow = NO;
    UILabel * const searchLabel = self.searchLabel;
    if (searchLabel && searchLabel.superview) {
        CGRect rect = [searchLabel convertRect:searchLabel.bounds toView:[UIApplication sharedApplication].keyWindow];
        isSearchOutOfWindow = rect.origin.y < SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    }
    
    if (!self.searchViewVisiable || isSearchOutOfWindow) {
        /// 在列表里，而非头部，目前就835版本，没有曝光时就不上报
        return;
    }
    if (![searchText isEqualToString:searchLabel.text]) { //避免重复上报
        // 增加曝光维度：底纹词序号与类型（人工/算法/兜底）
        // shading_index：取自当前展示词在 head_search_words 中的序号（1 开始）
        // shading_type：取自对应对象的 data_type 字段；若找不到则回退到 currentSearchWordObject.data_type；仍无则置为 0
        __block NSUInteger shadingIndex = 0;
        __block id shadingType = nil;

        // 优先使用已缓存的对象数组
        NSArray *headSearchWords = self.headSearchWordsArray;
        if (![headSearchWords isKindOfClass:[NSArray class]] || headSearchWords.count == 0) {
            // 回退到原始数据上的 head_search_words
            headSearchWords = [self.searchSuggestWords valueForKey:@"head_search_words"];
        }
        if ([headSearchWords isKindOfClass:[NSArray class]] && headSearchWords.count > 0) {
            [headSearchWords enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if ([obj isKindOfClass:[NSDictionary class]]) {
                    NSString *keyword = [(NSDictionary *)obj objectForKey:@"keyword"];
                    if ([keyword isKindOfClass:[NSString class]] && [keyword isEqualToString:searchText]) {
                        shadingIndex = idx + 1;
                        shadingType = [(NSDictionary *)obj objectForKey:@"data_type"];
                        *stop = YES;
                    }
                }
            }];
        }
        if (!shadingType && [self.currentSearchWordObject isKindOfClass:[NSDictionary class]]) {
            shadingType = [self.currentSearchWordObject objectForKey:@"data_type"];
        }

        NSDictionary *params = @{
            @"func": @(22),
            @"pos_id": @(self.posId),
            @"words": searchText,
            @"words_type": @(1),
            // 新增字段
            @"shading_location": @(1),
            @"shading_index": @(shadingIndex),
            @"shading_type": shadingType ?: @(0),
        };
        [self.class postSearchGAEventWithParam:params];
    }
}

+ (void)postSearchGAEventWithParam:(NSDictionary *)param {
    [IMYGAEventHelper postWithPath:@"search-static" params:param headers:nil completed:nil];
}

/// 底纹词点击上报
- (void)gaPostSearchClickWithSearchText:(NSString *)searchText {
    BOOL isSearchOutOfWindow = NO;
    UILabel * const searchLabel = self.searchLabel;
    if (searchLabel && searchLabel.superview) {
        CGRect rect = [searchLabel convertRect:searchLabel.bounds toView:[UIApplication sharedApplication].keyWindow];
        isSearchOutOfWindow = rect.origin.y < SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    }
    
    if (!self.searchViewVisiable || isSearchOutOfWindow) {
        /// 在列表里，而非头部，目前就835版本，没有曝光时就不上报
        return;
    }
    
    // shading_index：取自当前展示词在 head_search_words 中的序号（1 开始）
    // shading_type：取自对应对象的 data_type 字段；若找不到则回退到 currentSearchWordObject.data_type；仍无则置为 0
    __block NSUInteger shadingIndex = 0;
    __block id shadingType = nil;

    // 优先使用已缓存的对象数组
    NSArray *headSearchWords = self.headSearchWordsArray;
    if (![headSearchWords isKindOfClass:[NSArray class]] || headSearchWords.count == 0) {
        // 回退到原始数据上的 head_search_words
        headSearchWords = [self.searchSuggestWords valueForKey:@"head_search_words"];
    }
    if ([headSearchWords isKindOfClass:[NSArray class]] && headSearchWords.count > 0) {
        [headSearchWords enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[NSDictionary class]]) {
                NSString *keyword = [(NSDictionary *)obj objectForKey:@"keyword"];
                if ([keyword isKindOfClass:[NSString class]] && [keyword isEqualToString:searchText]) {
                    shadingIndex = idx + 1;
                    shadingType = [(NSDictionary *)obj objectForKey:@"data_type"];
                    *stop = YES;
                }
            }
        }];
    }
    if (!shadingType && [self.currentSearchWordObject isKindOfClass:[NSDictionary class]]) {
        shadingType = [self.currentSearchWordObject objectForKey:@"data_type"];
    }

    NSDictionary *params = @{
        @"func": @(23), // 点击事件
        @"pos_id": @(self.posId),
        @"words": searchText,
        @"words_type": @(1),
        // 新增字段
        @"shading_location": @(1),
        @"shading_index": @(shadingIndex),
        @"shading_type": shadingType ?: @(0),
    };
    [self.class postSearchGAEventWithParam:params];
}

/// 8.93.0版本：获取当前轮播显示的完整搜索词对象
- (NSDictionary *)getCurrentSearchWordObject {
    return self.currentSearchWordObject;
}


/// 8.93.0版本：在scheme_uri跳转前保存搜索历史记录
- (void)saveSearchHistoryBeforeSchemeJump:(NSDictionary *)searchWordObject {
    if (![searchWordObject isKindOfClass:[NSDictionary class]]) {
        return;
    }

    NSString *keyword = searchWordObject[@"keyword"];
    if (![keyword isKindOfClass:[NSString class]] || keyword.length == 0) {
        return;
    }

    // 直接保存IMYNASearchHistoryModel格式的历史记录
    [self saveSearchHistoryModelDirectly:keyword sourceModel:searchWordObject];
}

/// 直接保存IMYNASearchHistoryModel格式的历史记录到本地存储
- (void)saveSearchHistoryModelDirectly:(NSString *)keyword sourceModel:(NSDictionary *)sourceModel {
    // 存储键定义（与IMYNASearchHomeVC保持一致）
    static NSString * const kIMYKVSearchHistoryModelsKey = @"search.history.models";

    // 创建新的历史记录模型
    Class historyModelClass = NSClassFromString(@"IMYNASearchHistoryModel");
    if (!historyModelClass) {
        return;
    }

    id newHistoryModel = [historyModelClass performSelector:@selector(modelWithKeyword:sourceModel:)
                                                  withObject:keyword
                                                  withObject:sourceModel];
    if (!newHistoryModel) {
        return;
    }

    // 加载现有的历史记录模型
    NSMutableArray<id> *historyModels = [NSMutableArray array];
    NSArray *serializedArray = [[IMYKV defaultKV] arrayForKey:kIMYKVSearchHistoryModelsKey];

    if (serializedArray.count > 0) {
        // 反序列化现有模型
        for (id dict in serializedArray) {
            id model = [historyModelClass performSelector:@selector(yy_modelWithJSON:) withObject:dict];
            if (model) {
                [historyModels addObject:model];
            }
        }
    }

    // 移除相同关键词的旧记录
    NSMutableArray *filteredModels = [NSMutableArray array];
    for (id model in historyModels) {
        NSString *existingKeyword = [model valueForKey:@"keyword"];
        if (![existingKeyword isEqualToString:keyword]) {
            [filteredModels addObject:model];
        }
    }

    // 将新记录插入到第一位
    [filteredModels insertObject:newHistoryModel atIndex:0];

    // 限制历史记录数量（最多保留20条）
    if (filteredModels.count > 20) {
        [filteredModels removeObjectsInRange:NSMakeRange(20, filteredModels.count - 20)];
    }

    // 序列化并保存模型数组
    NSMutableArray *serializedModels = [NSMutableArray array];
    for (id model in filteredModels) {
        // 序列化模型，确保sourceModel信息完整保存
        id dict = [model performSelector:@selector(imy_jsonObject)];
        if (dict) {
            [serializedModels addObject:dict];
        }
    }

    // 只保存模型格式，不再保存字符串格式
    [[IMYKV defaultKV] setArray:serializedModels forKey:kIMYKVSearchHistoryModelsKey];

}

/// 8.93.0版本：触发IMYCKDynamicSearchBar的图标更新
- (void)triggerSearchBarIconUpdate {
    // 查找关联的IMYCKDynamicSearchBar
    IMYCKDynamicSearchBar *searchBar = (IMYCKDynamicSearchBar *)[self.searchLabel imy_findParentViewWithClass:[IMYCKDynamicSearchBar class]];
    if (searchBar && [searchBar respondsToSelector:@selector(updateIconTagFromHelper)]) {
        [searchBar updateIconTagFromHelper];
    }
}

/// 轮播用的推荐词
- (NSArray *)searchWords {
    if (self.adSearchKeywords.count) {
        return [self.adSearchKeywords map:^id _Nonnull(NSDictionary *element) {
            return element[@"showKey"];
        }];
    }

    // 获取head_search_words数据（8.93.0版本：只支持新的对象数组格式）
    NSArray *headSearchWords = [self.searchSuggestWords valueForKey:@"head_search_words"];
    if (![headSearchWords isKindOfClass:[NSArray class]] || headSearchWords.count == 0) {
        return nil;
    }

    // 从对象数组中提取keyword字段
    NSMutableArray *keywords = [NSMutableArray array];
    for (NSDictionary *item in headSearchWords) {
        if ([item isKindOfClass:[NSDictionary class]]) {
            NSString *keyword = item[@"keyword"];
            if ([keyword isKindOfClass:[NSString class]] && keyword.length > 0) {
                [keywords addObject:keyword];
            }
        }
    }

    return keywords.count > 0 ? [keywords copy] : nil;
}

- (NSString *)defaultSearchPlaceholder {
    if (self.fromType == IMYCKSearchFromTypeHome) {
        return IMYString(@"搜你想搜的");
    } else {
        return IMYString(@"搜索圈子、话题");
    }
}

#pragma mark - 孕期

/// 孕期首页搜索框推荐词增加3s文案提示AB实验
// https://www.tapd.cn/34674824/prong/stories/view/1134674824001041687
- (void)pregnancyDelayLoadSuggestSearchWord {
    IMYABTestExperiment *tmpExperiment = [[IMYABTestManager sharedInstance] experimentForKey:@"searchbar_gravida_Defaultcopy"];
    if (tmpExperiment && tmpExperiment.status == IMYABTestExpStatusTesting) {
        NSInteger delay = [tmpExperiment.vars integerForKey:@"searchbar_gravida_Defaultcopy"];
        if (delay == 1) {
            self.shouldLoadWord = NO;
            [self showWhenLaunchAdFinish];
        }
    }
}

//等开屏广告结束了，在进行3s延迟展示搜索推荐词
- (void)showWhenLaunchAdFinish {
    NSArray<__kindof UIWindow *> *windows = [UIApplication sharedApplication].windows;
    for (UIWindow *window in windows) {
        if ([window isKindOfClass:NSClassFromString(@"IMYAdLaunchWindow")]) {
            imy_asyncMainBlock(1, ^{
                [self showWhenLaunchAdFinish];
            });
            return;
        }
    }
    imy_asyncMainBlock(3, ^{
        self.shouldLoadWord = YES;
        [self pregnancyLoadSearchSuggestKeywords];
    });
}

- (void)pregnancyLoadSearchSuggestKeywords {
    if (![self.searchSuggestWords isKindOfClass:[NSDictionary class]] || self.searchSuggestWords.count == 0) {
        return;
    }
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"gravida_head_search"];
    if (exp.status != IMYABTestExpStatusGloballyIsolated && exp.status != IMYABTestExpStatusModuleIsolated) {//实验固化
        [self handleSearchSuggestWords];
    }
}

#pragma mark - 广告开屏搜索联动

- (void)setAdSearchKeywords:(NSArray<NSDictionary *> *)adSearchKeywords {
    if (!_adSearchKeywords && !adSearchKeywords) {
        return;
    }
    _adSearchKeywords = adSearchKeywords;
    /// 刷新
    [self handleSearchSuggestWords];
}

- (NSString *)adSearchKeyWithShowKey:(NSString *)showKey {
    for (NSDictionary *dic in self.adSearchKeywords) {
        if ([dic[@"showKey"] isEqualToString:showKey]) {
            return imy_isNotEmptyString(dic[@"searchKey"])?dic[@"searchKey"]:showKey;
        }
    }
    return showKey;
}

@end
