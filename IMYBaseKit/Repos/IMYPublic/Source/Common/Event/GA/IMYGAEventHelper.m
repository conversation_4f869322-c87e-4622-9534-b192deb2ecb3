//
//  IMYGAEventHelper.m
//  IMYPublic
//
//  Created by 施东苗 on 16/6/3.
//  Copyright © 2016年 meiyou. All rights reserved.
//

#import "IMYGAEventHelper.h"
#import "IMYGAEventHelper+Batch.h"
#import "IMYGAEventModel.h"
#import "IMYMeetyouHTTPHooks.h"
#import "IMYPublic.h"
#import <IOC-Protocols/IOCAppInfo.h>
#import <IOC-Protocols/IOCCAIDManager.h>

#pragma mark - 标准发送接口

// 当次活跃标识
static NSInteger GASendOrderIndex = 1;
static NSString *GASessionID = nil;

// 应用来源标识
static IMYGAFromType GAFromType = 0;
static NSDictionary *GAFromtTypeParams = nil;

// AB参数，相关参数 都是在 ga queue 队列中进行修改，无线程安全问题
static NSString *GAChannelABExpLog = nil;
static NSString *GAChannelABIsolLog = nil;

@implementation IMYGAEventHelper

IMY_KYLIN_FUNC_PREMAIN {
    dispatch_async(IMYGAEventHelper.gaQueue, ^{
        // AB实验开关
        IMYABTestManager *abManager = [IMYABTestManager sharedInstance];
        dispatch_block_t actionBlock = ^{
            // 先收集AB标准参数
            GAChannelABExpLog = abManager.exp_log;
            GAChannelABIsolLog = abManager.isol_log;
        };
        actionBlock();
        [abManager.loadedSignal subscribeNext:^(id  _Nullable x) {
            dispatch_async(IMYGAEventHelper.gaQueue, actionBlock);
        }];;
    });
}

+ (NSString *)sessionID {
    NSString *sessionID = GASessionID;
    if (!sessionID) {
        @synchronized(self) {
            if (!GASessionID) {
                GASessionID = [NSString stringWithFormat:@"%ld", (long)(IMYDateTimeIntervalSince1970() * 1000)];
            }
            sessionID = GASessionID;
        }
    }
    return sessionID;
}

+ (NSString *)sessionDidChangedNotification {
    return @"IMYGASessionDidChangedNotification";
}

+ (void)cleanSessionID {
    @synchronized(self) {
        NSString *sessionID = GASessionID;
        GASessionID = nil;
        imy_asyncMainBlock(1, ^{
            [sessionID class];
        });
    }
}

+ (void)postWithPath:(NSString *)path
              params:(NSDictionary *)params
             headers:(NSDictionary *)headers
           completed:(void (^)(NSData *, NSURLResponse *, NSError *))completedBlock {
    if (imy_load_current() == IMYLoadAtPreMain) {
        [NSObject imy_asyncBlock:^{
            dispatch_async(self.gaQueue, ^{
                [self asyncPostWithPath:path params:params headers:headers completed:completedBlock];
            });
        }];
    } else {
        dispatch_async(self.gaQueue, ^{
            [self asyncPostWithPath:path params:params headers:headers completed:completedBlock];
        });
    }
}

+ (IMYGAEventModel *)eventModelWithPath:(NSString *)path
                                 params:(NSDictionary *)params
                                headers:(NSDictionary *)headers {
    IMYGAEventModel *model = [IMYGAEventModel new];
    if (NO == [path hasPrefix:@"/"]) {
        path = [NSString stringWithFormat:@"/%@", path];
    }
    model.path = path;
    
    // in ms
    model.timestamp = (uint64_t)([[NSDate date] timeIntervalSince1970] * 1000);
    model.session_id = [self sessionID];
    model.source = [IMYMeetyouHTTPHooks currentPageSource];
    model.maintab = [IMYMeetyouHTTPHooks currentMainTab];
    model.history = [IMYMeetyouHTTPHooks currentPageHistory];
    
    // ab info
    model.exp = GAChannelABExpLog;
    model.isol = GAChannelABIsolLog;
    if (NO == [IMYPublicAppHelper shareAppHelper].isPersonalRecommand) {
        model.recomm = @(0).stringValue;
    }
    
    // user info
    if ([[IMYPublicAppHelper shareAppHelper].userid imy_isPositiveInt]) {
        model.uid = [IMYPublicAppHelper shareAppHelper].userid;
    } else {
        model.uid = @"0";
    }
    model.mode = [IMYPublicAppHelper shareAppHelper].userMode;
    
    // net info
    model.apn = [self currentAPNState];
    
    // page order info
    if (headers[@"-order"]) {
        model.order = [headers[@"-order"] integerValue];
    }
    
    // 第二身份相关
    IMYUserInfoModel *userSecondModel = [IMYPublicAppHelper shareAppHelper].userSecondModel;
    if (userSecondModel.userMode) {
        model.mode2 = @(userSecondModel.userMode).stringValue;
    }
    if (userSecondModel.baby_id) {
        model.bbid = @(userSecondModel.baby_id).stringValue;
    }
    if (imy_isNotBlankString(userSecondModel.birthday)) {
        model.bbday = userSecondModel.birthday;
    }
    
    // 保存body参数
    model.attributes = params;
    
    // 保存headers
    if (headers.count > 0 || model.uid.length > 1) {
        // 修正headers
        NSMutableDictionary *fixedHeaders = [NSMutableDictionary dictionary];
        // 追加auth
        NSDictionary *authMap = [IMYMeetyouHTTPHooks currentAuthorizationMap];
        [fixedHeaders addEntriesFromDictionary:authMap];
        // 修正外部传进来的headers
        [headers enumerateKeysAndObjectsUsingBlock:^(NSString *key, id obj, BOOL *stop) {
            if (![key isKindOfClass:NSString.class] ||
                ![obj isKindOfClass:NSString.class]) {
                NSAssert(NO, @"GA-%@, headers: %@ 必须都是string", path, headers);
                return;
            }
            // order 已经被独立保存
            if ([key isEqualToString:@"-order"]) {
                return;
            }
            // 不允许覆盖底层标准参数
            if ([key hasPrefix:@"-"]) {
                key = [key substringFromIndex:1];
            }
            fixedHeaders[key] = obj;
        }];
        model.headers = fixedHeaders;
    }
    
#ifdef DEBUG
    // 判断 params 是否无效
    if (params && ![NSJSONSerialization isValidJSONObject:params]) {
        NSString *message = [NSString stringWithFormat:@"invalid ga path: %@ params: %@", path, params.debugDescription];
        NSAssert(NO, message);
    }
#endif
    
    return model;
}


/**
 Door 接口配置结构
 {
   "type": "ga_switch_base",
   "status": true,
   "message": "",
   "data": {
     "paths": "bi_information,bi_tools,...", // path黑名单，优先级：3
     "simple_off": 0,  // 简化版本 一刀切，只上报 bi_active     优先级：2
     "force_off": 0  // 强化版本 一刀切。 不上报任何 GA 埋点数据， 优先级：1
   }
 }
 */
+ (BOOL)interceptPersonalRecommandWithPath:(NSString *)origPath {
    // GA上报数据，不再判断 个性化推荐 开关
    return NO;
}

+ (void)asyncPostWithPath:(NSString *)path
                   params:(NSDictionary *)params
                  headers:(NSDictionary *)headers
                completed:(void (^)(NSData *, NSURLResponse *, NSError *))completedBlock {
    if (imy_isEmptyString(path)) {
        NSAssert(NO, @"path is empty!");
        if (completedBlock) {
            completedBlock(nil, nil, [NSError errorWithDomain:@"path is empty!" code:-998 userInfo:nil]);
        }
        return;
    }
    
    id<IOCGAEventInterceptor> interceptor = IMYHIVE_BINDER(IOCGAEventInterceptor);
    if (interceptor) {
        // 该埋点被取消了
        BOOL cancelled = [interceptor cancelledPostWithPath:path];
        if (cancelled) {
            return;
        }
        
        // 修改埋点参数
        NSDictionary *newParams = [interceptor shouldChangeParamsForPath:path origParams:params];
        if (newParams.count > 0) {
            params = [newParams copy];
        }
    }
    
    // 如果 关闭了个性化推荐，根据 door 接口配置，来拦截数据上报
    if ([self interceptPersonalRecommandWithPath:path]) {
        return;
    }
    
    void (^jackBlock)(BOOL) = ^(BOOL success) {
        if (!completedBlock) {
            return;
        }
        if (success) {
            completedBlock(nil, nil, nil);
        } else {
            completedBlock(nil, nil, [NSError errorWithDomain:@"fails!" code:-1 userInfo:nil]);
        }
    };

    IMYGAEventModel *eventModel = [self eventModelWithPath:path params:params headers:headers];
#ifdef DEBUG
    /// 发到内网测试
    [self postGATestWithEventModel:eventModel];

    /// 全部走 http 实时接口
    if (!self.enabledBatchUpload) {
        [self real_postWithModel:eventModel isRetry:NO completed:jackBlock];
        return;
    }
#endif
    // 除了日活接口，其他埋点：无uid数据，都先存到数据库，等uid下发后再上传
    if ([eventModel.path isEqualToString:@"bi_active"] == NO &&
        [eventModel.uid isEqualToString:@"0"]) {
        [self batch_saveToDB:eventModel];
        return;
    }
    // 判断是否走批量接口
    IMYGAEventUploadType const type = [self getUploadTypeForPath:path];
    if (type == IMYGAEventUploadTypeReal) {
        // 走实时上传接口
        [self real_postWithModel:eventModel isRetry:NO completed:jackBlock];
    } else {
        // 走批量上传接口
        [self batch_postWithModel:eventModel isRetry:NO completed:jackBlock];
    }
}

+ (void)real_postWithModel:(IMYGAEventModel *)model
                   isRetry:(BOOL)isRetry
                 completed:(void (^)(BOOL success))completedBlock {
    if (![IMYPublicAppHelper shareAppHelper].hasAgreedPrivacy) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), self.gaQueue, ^{
            [self real_postWithModel:model isRetry:isRetry completed:completedBlock];
        });
        return;
    }
    // 获取请求域名
    NSString *host = [self requestHostWithABType:model.tcp];
    NSURL *url = [NSURL URLWithString:model.path relativeToURL:[NSURL URLWithString:host]];

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:30];
    [request setHTTPMethod:@"POST"];

    NSMutableDictionary *allHeaders = [NSMutableDictionary dictionary];
    ///设备参数
    NSDictionary *deviceInfo = [self getDeviceInfo];
    [allHeaders addEntriesFromDictionary:deviceInfo];

    /// 应用参数
    allHeaders[@"myclient"] = [IMYPublicAppHelper shareAppHelper].myclient;
    if (NSBundle.enableMYAppInfo) {
        allHeaders[@"myappinfo"] = [IMYPublicAppHelper shareAppHelper].myappinfo;
    }    
    //网络参数
    allHeaders[@"-apn"] = @(model.apn).stringValue;

    ///业务参数
    allHeaders[@"session-id"] = model.session_id;
    allHeaders[@"source"] = model.source;
    allHeaders[@"-uid"] = model.uid;
    allHeaders[@"maintab"] = model.maintab;
    allHeaders[@"history"] = model.history;
    if (model.exp) {
        allHeaders[@"exp"] = model.exp;
    }
    if (model.isol) {
        allHeaders[@"isol"] = model.isol;
    }
    if (model.recomm) {
        allHeaders[@"recomm"] = model.recomm;
    }
    
    /// 第一身份
    allHeaders[@"mode"] = @(model.mode).stringValue;
    
    /// 第二身份相关
    if (model.mode2) { // 第二身份
        allHeaders[@"mode2"] = model.mode2;
    }
    if (model.bbid) { // 选中的宝宝ID
        allHeaders[@"bbid"] = model.bbid;
    }
    if (model.bbday) { // 选中的宝宝生日
        allHeaders[@"bbday"] = model.bbday;
    }
    
    allHeaders[@"-order"] = @(model.order).stringValue;
    
    if (model.headers.count > 0) {
        [allHeaders addEntriesFromDictionary:model.headers];
    }
    
    allHeaders[@"Content-Type"] = @"application/json";
    allHeaders[@"Accept"] = @"*/*";

    NSData *bodyData = [model.attributes imy_jsonData];
    request.HTTPBody = bodyData;
    allHeaders[@"Content-Length"] = [NSString stringWithFormat:@"%lu", (unsigned long)bodyData.length];
    [request setAllHTTPHeaderFields:allHeaders];
    
    // 国际版可拦截
    [IMYHIVE_BINDER(IOCGAEventInterceptor) onRequestWillPosting:request];
    
    //发送请求
    [IMYPublicServerRequest dataTaskWithRequest:request
                              completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (!error) {
            if (completedBlock) {
                completedBlock(YES);
            }
        } else {
            NSLog(@"[ERROR] GA real post failed!");
            if (isRetry) {
                if (completedBlock) {
                    completedBlock(NO);
                }
                return;
            }
            /// 实时发送失败了，放到批量接口中，等待重新上传
            dispatch_async(self.gaQueue, ^{
                [self batch_postWithModel:model
                                  isRetry:YES
                                completed:completedBlock];
            });
        }
    }];
}

+ (void)batch_postWithModel:(IMYGAEventModel *)model
                    isRetry:(BOOL)isRetry
                  completed:(void (^)(BOOL success))completedBlock {
    BOOL inserted = [self batch_saveToDB:model];
    if (inserted) {
        if (completedBlock) {
            completedBlock(YES);
        }
    } else {
        NSLog(@"[ERROR] GA saveToDB failed!");
        if (isRetry) {
            if (completedBlock) {
                completedBlock(NO);
            }
            return;
        }
        /// 存储失败走实时发送
        dispatch_async(self.gaQueue, ^{
            [self real_postWithModel:model
                             isRetry:YES
                           completed:completedBlock];
        });
    }
}

+ (NSInteger)currentAPNState {
    return [IMYNetState apn].integerValue;
}

#ifdef DEBUG
// QA需求: https://www.tapd.cn/21039721/prong/stories/view/1121039721001131730
+ (void)postGATestWithEventModel:(IMYGAEventModel *)model {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"client_id"] = @(IMYPublicAppHelper.AppID);
    params[@"client_platform"] = @(2);
    params[@"client_version"] = APPBuildVersion;
    params[@"path"] = model.path;
    params[@"content"] = model.attributes;
    params[@"source"] = model.source;
    params[@"maintab"] = model.maintab;
    params[@"history"] = model.history;
    params[@"uid"] = model.uid;
    params[@"order"] = @(model.order);
    params[@"session-id"] = model.session_id;
    
    [[IMYPublicServerRequest postPath:@"http://*************:6666/burying_point_log" host:@"" querys:nil params:params headers:nil] subscribeNext:^(id  _Nullable x) {
        // ok
    } error:^(NSError * _Nullable error) {
        // fail
    }];
}
#endif

@end

#pragma mark - Private

@implementation IMYGAEventHelper (Private)

#ifdef DEBUG

static BOOL GADisabledBatchUpload = YES;

+ (BOOL)enabledBatchUpload {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        id number = [[NSUserDefaults standardUserDefaults] objectForKey:@"GADisabledBatchUpload"];
        if (number) {
            GADisabledBatchUpload = [number boolValue];
        }
    });
    return !GADisabledBatchUpload;
}

+ (void)setEnabledBatchUpload:(BOOL)enabledBatchUpload {
    GADisabledBatchUpload = !enabledBatchUpload;
    [[NSUserDefaults standardUserDefaults] setBool:GADisabledBatchUpload forKey:@"GADisabledBatchUpload"];
}

IMY_KYLIN_FUNC_IDLE_ASYNC {
    [[IMYServerRequest postPath:@"http://*************:8999/update/serial/version" host:@"" params:@{
        @"device_id" : UIDevice.imy_openUDID,
        @"os_version" : UIDevice.currentDevice.systemVersion ?: @"",
    } headers:nil] subscribeNext:^(id  _Nullable x) {
        NSLog(@"测试设备ID埋点上传成功");
    }];
}

#endif

+ (NSDictionary *)getDeviceInfo {
    NSMutableDictionary *info = [NSMutableDictionary dictionary];
    
    NSInteger screenWidth = SCREEN_WIDTH * SCREEN_SCALE;
    NSInteger screenHeight = SCREEN_HEIGHT * SCREEN_SCALE;
    info[@"-ua"] = [UIDevice imy_platform];
    if ([[UIDevice imy_machineModel] containsString:@"iPad"]) {
        info[@"-os"] = @"4";
    } else {
        info[@"-os"] = @"2";
    }
    info[@"-os-v"] = [IMYSystem stringVersion];
    info[@"-sw"] = [NSString stringWithFormat:@"%ld", (long)screenWidth];
    info[@"-sh"] = [NSString stringWithFormat:@"%ld", (long)screenHeight];
    info[@"-ot"] = [[UIDevice imy_carrierName] imy_URLEncodedString];
    info[@"-openudid"] = [UIDevice imy_openUDID];
    
    NSDictionary *statInfoDict = [self getStatInfoMap];
    NSDictionary *statInfoHeaders = [IMYMeetyouHTTPHooks statInfoEncryptHeadersForInfoDict:statInfoDict urlHost:@"ga.seeyouyima.com"];
    [info addEntriesFromDictionary:statInfoHeaders];
    
    return info;
}

+ (NSDictionary *)getStatInfoMap {
    NSDictionary *iocInfoMap = [IMYHIVE_BINDER(IOCGAEventInterceptor) getStatInfoMap];
    if (iocInfoMap.count > 0) {
        // 外部注入 statinfo map
        return iocInfoMap;
    }
    IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
    
    NSMutableDictionary *mutableDict = [NSMutableDictionary dictionary];
    
    // 设备ID
    mutableDict[@"idfa"] = [UIDevice imy_idfaString] ?: @"";
    mutableDict[@"mac"] = @"";
    mutableDict[@"dna"] = [UIDevice imy_DNA] ?: @"";
    mutableDict[@"device_id"] = [UIDevice imy_macaddress] ?: @"";
    mutableDict[@"caid"] = [IMYHIVE_BINDER(IOCCAIDManager) getCacheCAID] ?: @"";
    
    // 订阅权限信息
    const NSInteger currentRightsType = [IMYRightsSDK sharedInstance].currentRightsType;
    if (currentRightsType > 0) {
        mutableDict[@"vip_type"] = @(currentRightsType);
    }
    
    // 计算AppIcon类型
    NSInteger const appIconType = IMYHIVE_BINDER(IOCAppInfo).currentAppIconType;
    NSInteger biIconTag = 1; // 普通版
    if (appIconType == 1) {
        biIconTag = 3;  // 亲友版
    } else if (appIconType >= 2) {
        biIconTag = 2; // VIP
    }
    // 极简模式、青少年模式、会员状态、样式ID、AppIcon类型、AppIcon具体值、最新会员类型
    NSString *bi_model = [NSString stringWithFormat:@"%ld%ld%.2ld%ld%ld%.3ld%.2ld",
                          MAX(0, MIN(9, IMYHIVE_BINDER(IOCAppInfo).currentAppPattern == 2 ? 2 : 1)),
                          MAX(0, MIN(9, appHelper.useYoungMode ? 2 : 1)),
                          MAX(0, MIN(99, IMYRightsSDK.sharedInstance.currentSubscribeType)),
                          MAX(0, MIN(9, IMYHIVE_BINDER(IOCAppInfo).currentNewThemeID)),
                          MAX(0, MIN(9, biIconTag)),
                          MAX(0, MIN(999, appIconType)),
                          MAX(0, MIN(99, IMYRightsSDK.sharedInstance.detailModel.latest_type))];
    
    mutableDict[@"bi_model"] = bi_model;
    
    return [mutableDict copy];
}

+ (NSInteger)GASendOrderIndex {
    return GASendOrderIndex;
}

+ (void)incGASendOrderIndex {
    GASendOrderIndex++;
}

+ (void)resetGASendOrderIndex {
    GASendOrderIndex = 1;
}

+ (IMYGAFromType)GAFromType {
    return GAFromType;
}

+ (dispatch_queue_t)gaQueue {
    static dispatch_queue_t queue;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        queue = dispatch_queue_create("meiyou.ga.queue", NULL);
    });
    return queue;
}

@end

#pragma mark - FromType 启动来源

@implementation IMYGAEventHelper (FromType)

+ (void)setFromType:(IMYGAFromType)fromType {
    [self willChangeValueForKey:@"fromType"];
    GAFromType = fromType;
    [self didChangeValueForKey:@"fromType"];
}

+ (IMYGAFromType)fromType {
    return GAFromType;
}

+ (void)setFromTypeParams:(NSDictionary *)fromTypeParams {
    id temp = GAFromtTypeParams;
    GAFromtTypeParams = [fromTypeParams copy];
    imy_asyncMainBlock(^{
        id temp2 = temp;
        temp2 = nil;
    });
}

+ (NSDictionary *)fromTypeParams {
    return GAFromtTypeParams;
}

@end
