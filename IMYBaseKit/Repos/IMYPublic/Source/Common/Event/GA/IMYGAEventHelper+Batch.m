//
//  IMYGAEventHelper+Batch.m
//  IMYPublic
//
//  Created by mario on 2017/8/14.
//  Copyright © 2017年 meiyou. All rights reserved.
//

#import "IMYGAEventHelper+Batch.h"
#import "IMYGAEventModel.h"
#import "IMYPublic.h"

NSString *const IMYGABeginApplicationHoldRunning = @"IMYGABeginApplicationHoldRunning";
static NSString *const GABatchTimerKey = @"GABatchTimerKey";
static BOOL GABatchEnable = YES;

@implementation IMYGAEventHelper (Batch)

+ (NSMutableDictionary *)getBatchMap {
    static NSMutableDictionary *map;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = [NSMutableDictionary dictionaryWithCapacity:3];
    });
    return map;
}

/// 因为在初始化的时候 进行了配置设置，在运行时不会改动，所以可以不用加锁
+ (void)setUploadType:(IMYGAEventUploadType)type forPath:(NSString *)path {
    if (nil == path || imy_isBlankString(path)) {
        return;
    }
    NSMutableDictionary *map = [self getBatchMap];
    map[path] = @(type);
}

+ (IMYGAEventUploadType)getUploadTypeForPath:(NSString *)path {
    if (!GABatchEnable) {
        return IMYGAEventUploadTypeReal;
    }
    NSDictionary *map = [self getBatchMap];
    NSNumber *num = map[path];
    if (num) {
        return [num integerValue];
    }
    if ([path hasPrefix:@"/"]) {
        path = [path substringFromIndex:1];
        return [map[path] integerValue];
    } else {
        path = [NSString stringWithFormat:@"/%@", path];
        return [map[path] integerValue];
    }
}

+ (NSString *)requestHostWithABType:(NSInteger const)type {
    return ga_seeyouyima_com;
}

+ (NSMutableDictionary *)sharedUploadingMap {
    static NSMutableDictionary *map;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = [NSMutableDictionary dictionaryWithCapacity:3];
    });
    return map;
}

static NSInteger kCurrentBatchRowCount = 0;
static NSInteger const kMaxDBRowCount = 500;
static NSInteger const kMaxUploadCount = 50;

+ (BOOL)batch_saveToDB:(IMYGAEventModel *)model {
    BOOL success = [model saveToDB];
    if (!success) {
        return NO;
    }
    kCurrentBatchRowCount += 1;
    if (kCurrentBatchRowCount > kMaxDBRowCount - 10) {
        // 当前存储数量接近 max size，则直接开启新数据库进行存储
        // 已经在 gaQueue 线程内，无需再次切换线程
        [self handleBatchTimerExpired];
    }
    return YES;
}

+ (void)startBatchTimer {
    IMYSwitchModel *doorModel = [[IMYDoorManager sharedManager] switchForType:@"GABatch"];
    NSInteger repeat_time = [doorModel.dataDictionary[@"repeat_time"] integerValue];
    if (repeat_time <= 0) {
        repeat_time = 20;
    }
    [NSObject imy_asyncBlock:^{
        [self handleBatchTimerExpired];
        [self startBatchTimer];
    } onQueue:self.gaQueue afterSecond:repeat_time forKey:GABatchTimerKey];
}

+ (void)stopBatchTimer {
    [NSObject imy_cancelBlockForKey:GABatchTimerKey];
}

+ (void)sendModels:(NSArray<IMYGAEventModel *> *)models completion:(void (^)(BOOL success))completion {
    if (nil == models || models.count == 0) {
        if (completion) {
            completion(YES);
        }
        return;
    }

    NSString *path = @"/batch";
    NSString *host = [self requestHostWithABType:models.firstObject.tcp];
    NSURL *url = [NSURL URLWithString:path relativeToURL:[NSURL URLWithString:host]];

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url
                                                           cachePolicy:NSURLRequestReloadIgnoringCacheData
                                                       timeoutInterval:30];
    [request setHTTPMethod:@"POST"];

    NSMutableDictionary *allHeaders = [NSMutableDictionary dictionary];
    ///设备参数
    NSDictionary *deviceInfo = [self getDeviceInfo];
    [allHeaders addEntriesFromDictionary:deviceInfo];

    /// 应用参数
    allHeaders[@"myclient"] = [IMYPublicAppHelper shareAppHelper].myclient;
    if (NSBundle.enableMYAppInfo) {
        allHeaders[@"myappinfo"] = [IMYPublicAppHelper shareAppHelper].myappinfo;
    }
    // uid
    if ([[IMYPublicAppHelper shareAppHelper].userid imy_isPositiveInt]) {
        allHeaders[@"-uid"] = [IMYPublicAppHelper shareAppHelper].userid;
    } else {
        allHeaders[@"-uid"] = @"0";
    }

    // user mode
    IMYVKUserMode userMode = [IMYPublicAppHelper shareAppHelper].userMode;
    allHeaders[@"mode"] = @(userMode).stringValue;

    // 授权头
    NSDictionary *authMap = [IMYMeetyouHTTPHooks currentAuthorizationMap];
    [allHeaders addEntriesFromDictionary:authMap];
    
    // http content type
    allHeaders[@"Content-Type"] = @"application/json";
    allHeaders[@"Accept"] = @"*/*";

    models = [models sortedArrayUsingComparator:^NSComparisonResult(IMYGAEventModel *_Nonnull obj1, IMYGAEventModel *_Nonnull obj2) {
        NSComparisonResult sessionOrder = [obj1.session_id compare:obj2.session_id];
        if (sessionOrder != NSOrderedSame) {
            return sessionOrder;
        }
        if (obj1.order < obj2.order) {
            return NSOrderedAscending;
        }
        if (obj1.order == obj2.order) {
            return NSOrderedSame;
        }
        return NSOrderedDescending;
    }];
    //NOTE: imy_jsonObject 不能递归处理对象数组
    NSArray *array = [models map:^id _Nonnull(IMYGAEventModel *element) {
        NSMutableDictionary *modelJSON = [element yy_modelToJSONObject];
        if (element.headers.count > 0) {
            [modelJSON addEntriesFromDictionary:element.headers];
        }
        return modelJSON;
    }];
    NSDictionary *params = @{@"data": array};

    NSData *bodyData = [params imy_jsonData];
    request.HTTPBody = bodyData;
    allHeaders[@"Content-Length"] = [NSString stringWithFormat:@"%lu", (unsigned long)bodyData.length];
    [request setAllHTTPHeaderFields:allHeaders];

    // 国际版可拦截
    [IMYHIVE_BINDER(IOCGAEventInterceptor) onRequestWillPosting:request];
    
    //发送请求
    [IMYPublicServerRequest dataTaskWithRequest:request
                              completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        BOOL hasSuccess = (!error ? YES : NO);
        NSLog(@"GA Batch upload: %@", hasSuccess ? @"OK" : @"FAILED");
        if (completion) {
            dispatch_async(self.gaQueue, ^{
                completion(hasSuccess);
            });
        }
    }];
}

+ (void)sendAFileInOutbox:(NSString *)filePath {
    id uploading = self.sharedUploadingMap[filePath];
    if ([uploading boolValue]) {
        return;
    }
    self.sharedUploadingMap[filePath] = @YES;

    LKDBHelper *helper = [[LKDBHelper alloc] initWithDBPath:filePath];
    
    void(^onCompletionBlock)(BOOL) = ^(BOOL success) {
        // 关闭数据库连接
        [helper closeDB];
        // 成功，则直接删除数据库文件
        if (success) {
            [[NSFileManager defaultManager] removeItemAtPath:filePath error:nil];
        }
        self.sharedUploadingMap[filePath] = @NO;
    };
    [self sendModelsInDB:helper where:nil completion:onCompletionBlock];
}

+ (void)sendModelsInDB:(LKDBHelper *)helper where:(NSString *)where completion:(void (^)(BOOL success))completion {
    NSInteger const rowCount = [helper rowCount:IMYGAEventModel.class where:where];
    if (rowCount == 0) {
        if (completion) {
            completion(YES);
        }
        return;
    }
    NSString * const currentUid = [IMYPublicAppHelper shareAppHelper].userid;
    // 待上传的数量大于 阈值
    if (rowCount > kMaxUploadCount) {
        // 数量太大，进行分段上报
        __block NSInteger successCount = 0;
        __block NSInteger completedCount = 0;
        const NSInteger allStepCount = (rowCount / kMaxUploadCount) + 1;
        for (NSInteger stepIndex = 0; stepIndex < allStepCount; stepIndex ++) {
            NSMutableArray<IMYGAEventModel *> *models = [helper search:IMYGAEventModel.class where:where orderBy:@"rowid" offset:stepIndex * kMaxUploadCount count:kMaxUploadCount];
            NSMutableString *rowids = [NSMutableString string];
            for (IMYGAEventModel *event in models) {
                if (rowids.length == 0) {
                    [rowids appendFormat:@"%ld", event.rowid];
                } else {
                    [rowids appendFormat:@",%ld", event.rowid];
                }
                if ([event.uid isEqualToString:@"0"]) {
                    event.uid = currentUid;
                }
            }
            NSString *deleteWhere = [NSString stringWithFormat:@"rowid in (%@)", rowids];
            [self sendModels:models completion:^(BOOL success) {
                if (success) {
                    [helper deleteWithClass:IMYGAEventModel.class where:deleteWhere];
                    successCount += 1;
                }
                completedCount += 1;
                // 所有分段都完成了上传
                if (completedCount >= allStepCount) {
                    if (completion) {
                        // 判断是否全部成功
                        BOOL success = (successCount >= allStepCount);
                        completion(success);
                    }
                }
            }];
        }
    } else {
        NSMutableArray<IMYGAEventModel *> *models = [helper search:IMYGAEventModel.class where:where orderBy:@"rowid" offset:0 count:0];
        // 删除指定数据
        NSMutableString *rowids = [NSMutableString string];
        for (IMYGAEventModel *event in models) {
            if (rowids.length == 0) {
                [rowids appendFormat:@"%ld", event.rowid];
            } else {
                [rowids appendFormat:@",%ld", event.rowid];
            }
            if ([event.uid isEqualToString:@"0"]) {
                event.uid = currentUid;
            }
        }
        NSString *deleteWhere = [NSString stringWithFormat:@"rowid in (%@)", rowids];
        [self sendModels:models completion:^(BOOL success) {
            if (success) {
                [helper deleteWithClass:IMYGAEventModel.class where:deleteWhere];
            }
            if (completion) {
                completion(success);
            }
        }];
    }
}

+ (void)sendOutbox {
    // 无网络 则不进行发送
    if (NO == [IMYNetState networkEnable]) {
        return;
    }
    // 无uid 则不进行发送
    if ([[IMYPublicAppHelper shareAppHelper].userid imy_isPositiveInt] == NO) {
        return;
    }
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    NSError *error = nil;
    NSString *directory = [IMYGAEventModel outboxDirectory];
    NSArray *contents = [fileManager contentsOfDirectoryAtPath:directory error:&error];
    if (error) {
        NSLog(@"GA outbox empty.");
        return;
    }

    NSArray * const sorted = [contents sortedArrayUsingComparator:^NSComparisonResult(NSString *name1, NSString *name2) {
        // 按文件名排序，时间越近的排序在前（文件名会按顺序累加）
        BOOL notDB1 = (![name1 hasPrefix:IMYGAEventModel.dbFileName] || [name1 hasSuffix:@"-shm"] || [name1 hasSuffix:@"-wal"]);
        BOOL notDB2 = (![name2 hasPrefix:IMYGAEventModel.dbFileName] || [name2 hasSuffix:@"-shm"] || [name2 hasSuffix:@"-wal"]);
        if (notDB1 && !notDB2) {
            return NSOrderedDescending;
        }
        if (!notDB1 && notDB2) {
            return NSOrderedAscending;
        }
        return [name1 compare:name2 options:NSNumericSearch] * -1;
    }];

    for (NSString *name in sorted) {
        NSString * const path = [directory stringByAppendingPathComponent:name];
        if (![name hasPrefix:IMYGAEventModel.dbFileName] || [name hasSuffix:@"-shm"] || [name hasSuffix:@"-wal"]) {
            // 非数据库文件，直接删除
            [fileManager removeItemAtPath:path error:nil];
            continue;
        }
        [self sendAFileInOutbox:path];
    }
}

+ (void)handleBatchTimerExpired {
    if ([NSThread isMainThread]) {
        NSAssert(NO, @"不允许在主线程执行");
        return;
    }
    NSString *dbPath = [IMYGAEventModel cacheDbFilePath];
    if (NO == [[NSFileManager defaultManager] fileExistsAtPath:dbPath]) {
        return;
    }
    // move current to outbox
    NSString *targetPath = [dbPath imy_copyFileToDirectory:[IMYGAEventModel outboxDirectory] overwritten:NO];
    if (targetPath) {
        // 删除旧数据库
        [[IMYGAEventModel getUsingLKDBHelper] dropAllTable];
        // 重置当前DB数量
        kCurrentBatchRowCount = 0;
        // 发送待发送区内的数据
        [self sendOutbox];
    } else {
        // 无网络 则不进行发送
        if (NO == [IMYNetState networkEnable]) {
            return;
        }
        // 无uid 则不进行发送
        if ([[IMYPublicAppHelper shareAppHelper].userid imy_isPositiveInt] == NO) {
            return;
        }
        
        NSLog(@"[ERROR] can't copy file '%@' to outbox.", dbPath);
        id uploading = self.sharedUploadingMap[@"main"];
        if ([uploading boolValue]) {
            return;
        }
        self.sharedUploadingMap[@"main"] = @YES;
        void(^onCompletionBlock)(BOOL) = ^(BOOL success) {
            // 发送完毕
            kCurrentBatchRowCount = 0;
            self.sharedUploadingMap[@"main"] = @NO;
        };
        LKDBHelper *helper = [IMYGAEventModel getUsingLKDBHelper];
        [self sendModelsInDB:helper where:nil completion:onCompletionBlock];
    }
}

// 强制锁死主线程1秒，延长杀死应用后的存活时间
+ (void)ga_setupApplicationHoldRunning {
    [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationWillTerminateNotification
                                                      object:nil
                                                       queue:nil
                                                  usingBlock:^(NSNotification *_Nonnull note) {
                                                      [self ga_beginApplicationHoldRunning];
                                                  }];
}

+ (void)ga_beginApplicationHoldRunning {
    // 可能还在网络请求，强制保活1.2秒，之后才允许其他代码执行
    CGFloat delayTime = 1.2;
    if (@available(iOS 16.0, *)) {
        delayTime = 0.5;
    }
    __block BOOL isExecuted = NO;
    const CFAbsoluteTime beginTime = CFAbsoluteTimeGetCurrent();
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_HIGH, 0), ^{
        isExecuted = YES;
    });
    // 通知外部执行收尾工作
    [[NSNotificationCenter defaultCenter] postNotificationName:IMYGABeginApplicationHoldRunning object:nil];
    // 卡死主线程
    while (!isExecuted) {
        const CFAbsoluteTime endTime = CFAbsoluteTimeGetCurrent();
        if (endTime - beginTime > delayTime) {
            break;
        }
        [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate distantFuture]];
    }
    // OK
    NSLog(@"app did terminate");
}

// 执行GA数据发送
+ (void)ga_applicationDidEnterBackgroundNotification {
    // 申请下后台执行任务，主要是为了上报埋点数据
    UIBackgroundTaskIdentifier bgTaskIdentifier = [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:^{
        //... 超时后，也没东西可处理
    }];
    // 保证页面数据先存到数据库
    dispatch_async(self.gaQueue, ^{
        [self handleBatchTimerExpired];
        // 5-10秒之后，通知系统结束任务
        uint32_t delay = (arc4random() % 5) + 5;
        imy_asyncMainBlock(delay, ^{
            [[UIApplication sharedApplication] endBackgroundTask:bgTaskIdentifier];
        });
    });
}

+ (void)ga_applicationOnCrashNotification {
    // 保证页面数据先存到数据库
    dispatch_async(self.gaQueue, ^{
        [self handleBatchTimerExpired];
    });
}

IMY_KYLIN_FUNC_PREMAIN {
    
    // 注册实时接口
    dispatch_async(IMYGAEventHelper.gaQueue, ^{
        [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:@"bi_information"];
        [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:@"bi_active"];
        [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:@"page"];
        [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:@"bi_adevent"];//广告曝光/点击
        [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:@"bi_launchad"];//开屏广告埋点
    });
    
    /// 读取 door 接口配置，是否开启批量发送
    dispatch_block_t updateBatchBlock = ^{
        dispatch_async(IMYGAEventHelper.gaQueue, ^{
            // Door
            IMYSwitchModel *doorModel = [[IMYDoorManager sharedManager] switchForType:@"GABatch"];
            NSArray *realPaths = [doorModel.dataDictionary[@"realtime"] componentsSeparatedByString:@","];
            for (NSString *path in realPaths) {
                if (imy_isNotBlankString(path)) {
                    [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:path];
                }
            }
            
            // 配置中心
            NSString *ccbatch = [[IMYConfigsCenter sharedInstance] stringForKeyPath:@"apptech.api.gabatch"];
            realPaths = [ccbatch componentsSeparatedByString:@","];
            for (NSString *path in realPaths) {
                if (imy_isNotBlankString(path)) {
                    [IMYGAEventHelper setUploadType:IMYGAEventUploadTypeReal forPath:path];
                }
            }
            
            // 开启批量统计
            if (doorModel) {
                GABatchEnable = doorModel.status;
            } else {
                GABatchEnable = ccbatch.length > 0;
            }
        });
    };
    
    imy_asyncMainBlock(1, ^{
        /// 监听退到后台事件，并执行上报，尽量靠后执行
        [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidEnterBackgroundNotification
                                                          object:nil
                                                           queue:nil
                                                      usingBlock:^(NSNotification *_Nonnull note) {
                                                          IMYGetApplicationState();
                                                          [IMYGAEventHelper ga_applicationDidEnterBackgroundNotification];
                                                      }];
        /// 闪退监听
        [[NSNotificationCenter defaultCenter] addObserverForName:IMYApplicationOnCrashNotification
                                                          object:nil
                                                           queue:nil
                                                      usingBlock:^(NSNotification *_Nonnull note) {
                                                          [IMYGAEventHelper ga_applicationOnCrashNotification];
                                                      }];
        
        /// 监听配置更新
        RACSignal *signal1 = [IMYConfigsCenter sharedInstance].loadedSignal;
        RACSignal *signal2 = [IMYDoorManager sharedManager].dataLoadFinishedSignal;
        [[[RACSignal merge:@[signal1, signal2]] throttle:0.1] subscribeNext:^(id x) {
            updateBatchBlock();
        }];
    });

    /// 最后注册保活 willTerminate 通知
    imy_asyncMainBlock(5, ^{
        [IMYGAEventHelper ga_setupApplicationHoldRunning];
    });
    
    // 在异步中 初始化 door接口缓存
    imy_asyncBlock(updateBatchBlock);
}

IMY_KYLIN_FUNC_IDLE_ASYNC {
    [IMYGAEventHelper startBatchTimer];
    dispatch_async(IMYGAEventHelper.gaQueue, ^{
        [IMYGAEventHelper sendOutbox]; // app启动后，发送上次退出前发件箱中保存的数据
    });
}

@end
