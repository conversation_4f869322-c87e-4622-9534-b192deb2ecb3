//
//  IMYAppReport.m
//  IMYAppReportDemo
//
//  Created by ljh on 16/3/18.
//  Copyright © 2016年 IMY. All rights reserved.
//

#import "IMYAppReport.h"
#import "IMYBuglyException.h"
#import "IMYQATestEventPost.h"
#import "IMYUMengUDplus.h"
#import "IMYPublic.h"
#import "UIDevice+IMYViewKit.h"
#import "IMYAppPlater.h"
#import "IMYHTTPSessionManager.h"
#import <SDWebImage/SDWebImageDownloader.h>

#if __has_include("XM-MeetYou-App.h")

// 美柚App，只有Debug模式下，才接入Bugly
#ifdef DEBUG
#if __has_include("Bugly.h")
#import "Bugly.h"
#define __has_bugly_sdk 1
#endif
#endif

#else

// 非美柚App，直接判断头文件
#if __has_include("Bugly.h")
#import "Bugly.h"
#define __has_bugly_sdk 1
#endif

#endif

#if __has_include(<UMCommon/MobClick.h>)
#import <UMCommon/MobClick.h>
#import <UMCommon/UMConfigure.h>
#define __has_umeng_mobclick 1
#endif

#if __has_include(<UMAPM/UMAPMConfig.h>)
#import <UMAPM/UMAPMConfig.h>
#import <UMAPM/UMCrashConfigure.h>
#import <UMAPM/UMLaunch.h>
#define __has_umeng_apm 1
#endif


#pragma mark - AppReport

@interface IMYAppReport ()
#ifdef __has_bugly_sdk
    <BuglyDelegate>
#endif

@property (nonatomic, assign) NSInteger lastAppRuningTime;
@property (nonatomic, assign) CFAbsoluteTime appStartingTime;

@property (nonatomic, copy) NSString *umengAppKey;
@property (nonatomic, copy) NSString *buglyAppKey;

@property (nonatomic, strong) NSMutableSet<NSNumber *> *launchedTypes;
@property (nonatomic, strong) dispatch_queue_t ioQueue;

@end

@implementation IMYAppReport

#define IMYValidStartKey [NSString stringWithFormat:@"last_app_start_%@", APPVersion]
#define IMYLastRuningTimeKey @"IMYLastAppRuningTime"

IMY_KYLIN_FUNC_LAUNCHED_ASYNC {
    // 启动完成后，移除预埋的crash标识
    [[IMYAppReport shareInstance] removeValidStartKey];
#ifndef DEBUG
    imy_asyncMainBlock(5, ^{
        [[IMYAppReport shareInstance] checkUmengSDKRegisted];
    });
#endif
    // 启动后，加载友盟开关配置
    [[IMYConfigsCenter sharedInstance] fetchGroupForKeyPath:@"apptech.errors" completedBlock:^(IMYCConfigsGroup * _Nullable group) {
        //...
    }];
}

+ (instancetype)shareInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        IMYKV * const kv = [IMYKV defaultKV];
        // 判断是否存在Crash标识，有则当做App运行了1秒
        if ([kv boolForKey:IMYValidStartKey]) {
            self.lastAppRuningTime = 1;
        } else {
            // 无闪退，则使用旧埋点
            self.lastAppRuningTime = [kv integerForKey:IMYLastRuningTimeKey];
            [kv removeForKey:IMYLastRuningTimeKey];
        }
        // 设置启动标识，并在注册 bugly id 后移除
        [kv setBool:YES forKey:IMYValidStartKey];
        // 记录启动时间
        self.appStartingTime = CFAbsoluteTimeGetCurrent();
        // 独立事件操作队列
        self.ioQueue = dispatch_queue_create("IMYAppReport.queue", NULL);
        // 已经执行过的启动事件
        self.launchedTypes = [NSMutableSet set];
#ifdef DEBUG
        [self reportQAWithData:nil needCache:NO];
#endif
    }
    return self;
}

- (void)removeValidStartKey {
    IMYKV * const kv = [IMYKV defaultKV];
    [kv removeForKey:IMYValidStartKey];
}

- (void)saveLastAppRuningTime:(NSInteger)time {
    IMYKV * const kv = [IMYKV defaultKV];
    if (time < 1) {
        [kv removeForKey:IMYLastRuningTimeKey];
    } else {
        [kv setInteger:time forKey:IMYLastRuningTimeKey];
    }
}

- (void)checkUmengSDKRegisted {
    // 正式TF包 不进行检测
    if ([APPBuildVersion componentsSeparatedByString:@"."].count < 5) {
        return;
    }
    if (![IMYPublicAppHelper shareAppHelper].hasAgreedPrivacy) {
        // 还未同意隐私政策，不进行检测
        return;
    }
    // 5秒后进行检测，保证SDK注册代码已经跑过
    imy_asyncMainBlock(5, ^{
        if (self.umengAppKey.length == 0) {
            [UIAlertController imy_quickAlert:@"UmengSDK还未注册，请检查代码！"];
        }
    });
}

- (void)startWithUmengAppKey:(NSString *)umengKey buglyAppId:(NSString *)buglyAppId {
    if (self.umengAppKey.length > 0 || self.buglyAppKey.length > 0) {
        NSAssert(NO, @"已注册过UmengKey，无需再次注册");
    }
    // 必须要在主线程执行
    imy_asyncMainExecuteBlock(^{
        // 注入全局美柚UA
        [UIDevice imy_userAgent];
        // 注册 Bugly SDK
        [self installBuglyWithAppId:buglyAppId reportLevel:0];
        // 注册 Umeng SDK
        [self installUmengWithAppKey:umengKey];
    });
}

/// Umeng SDK 初始化
- (void)installUmengWithAppKey:(NSString *)umengKey {
    self.umengAppKey = umengKey;

#ifdef __has_umeng_mobclick
    
    // 强制取消Log
    [UMConfigure setLogEnabled:NO];
    
#ifdef __has_umeng_apm
    // 友盟APM配置
    UMAPMConfig *config = [UMAPMConfig defaultConfig];
    // 6年(iPhone8)之前的机型减少性能统计，系统本身就比较卡
    if ([IMYSystem iPhoneVersion] < 11) {
        // 低端机只保留闪退和卡顿统计(内存统计太耗资源)
        config.launchMonitorEnable = NO;
        config.memMonitorEnable = NO;
        config.oomMonitorEnable = NO;
        config.networkEnable = NO;
        config.pageMonitorEnable = NO;
    } else {
        // 提高卡顿检测标准，按3秒卡顿计算（美团、Bugly）
        config.sendBeatInterval = 1.5;
        config.checkBeatInterval = 1.5;
        config.toleranceBeatMissingCount = 2;
        
        // 远程控制友盟内存检测开关（默认关闭）
        BOOL const enableMemHook = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"apptech.errors.um_mem_hook"];
        config.memMonitorEnable = enableMemHook;
        
        // 远程控制友盟OOM检测开关（默认关闭）
        BOOL const enableOOMHook = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"apptech.errors.um_oom_hook"];
        config.oomMonitorEnable = enableOOMHook;
        
        // 只有 iOS15 以上才开启网络监控（默认开启）
        BOOL const enableNetHook = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"apptech.errors.um_net_hook"];
        if (@available(iOS 15.0, *)) {
            config.networkEnable = enableNetHook;
        } else {
            config.networkEnable = NO;
        }
    }
    // 不开启友盟的网络兼容
    [UMCrashConfigure enableNetworkForProtocol:NO];
    // 不接入友盟 JS SDK
    config.javaScriptBridgeEnable = NO;
#ifdef DEBUG
    // 友盟没法自动传 dSYM，关闭测试包的友盟收集
    config.crashAndBlockMonitorEnable = NO;
#else
    // 注册闪退回调
    [UMCrashConfigure setCrashCallBackBlock:^NSString *(UMCrashType const type) {
        // 获取扩展信息
        NSString * const appInfos = [self getReportAppInfos:type];
        // 闪退
        if (type == UMCrashTypeException) {
            [self onCrash:nil];
        }
        // 卡顿 (发到-ELK)
        if (type == UMCrashTypeBlock) {
            [IMYErrorTraces postWithType:IMYErrorTraceTypePageTimeout
                                pageName:IMYMeetyouHTTPHooks.currentPageName
                                category:IMYErrorTraceCategoryOthers
                                 message:@"um-block"
                                  detail:appInfos
                               needRetry:YES];
        }
        // 返回扩展信息
        return appInfos;
    }];
#endif
    
    // 初始化APM配置
    [UMCrashConfigure setAPMConfig:config];

#endif
    
    NSString *channelID = [IMYPublicAppHelper shareAppHelper].channelID;
    
    // 友盟SDK初始化
    [UMConfigure initWithAppkey:self.umengAppKey channel:channelID];
    
    // 1秒后检测，是否开启了网络注入
    imy_asyncMainBlock(1, ^{
        BOOL const isOpen = [UMCrashConfigure networkMonitorStatus];
        if (config.networkEnable && isOpen) {
            // 重新初始化 NSURLSession 提供给 友盟注入
            [IMYHTTPSessionManager resetInternalSessionManager];
            [[SDWebImageDownloader sharedDownloader] resetInternalURLSession];
        }
    });
    
    // 事件统计
    [IMYEventHelper addEventClientClass:[IMYUMengUDplus class]];
    
    // 增加友盟Debug协议
    [[IMYURIManager shareURIManager] addForPath:@"umeng/debug" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *urlString = actionObject.uri.params[@"url"];
        if (urlString.length > 0) {
            NSURL *debugURL = [NSURL URLWithString:urlString.imy_URLDecode];
            [MobClick handleUrl:debugURL];
#ifdef __has_umeng_apm
            [UMAPMConfig handleUrl:debugURL];
#endif
        }
    }];
    
#ifdef DEBUG
    // 转发友盟信息给 QA 抓包
    [IMYEventHelper addEventClientClass:[IMYQATestEventPost class]];
    imy_asyncBlock(5, ^{
        [IMYQATestEventPost postAppInfoWithUmengKey:self.umengAppKey];
    });
#endif
    
#endif
}

/// Bugly SDK 初始化
- (void)installBuglyWithAppId:(NSString *)buglyAppId reportLevel:(NSUInteger)level {
    
    self.buglyAppKey = buglyAppId;

#ifdef __has_bugly_sdk
    NSString *channelID = [IMYPublicAppHelper shareAppHelper].channelID;
    
    BuglyConfig *config = [[BuglyConfig alloc] init];
    config.channel = channelID;
    config.blockMonitorEnable = NO;
    config.symbolicateInProcessEnable = NO;
    config.consolelogEnable = NO;
    config.delegate = self;
    config.crashAbortTimeout = 0;
    config.debugMode = NO;
    config.reportLogLevel = level;

    // iPhone8、iPhoneX 之后的机型都开启卡顿检测
    if ([IMYSystem iPhoneVersion] >= 10) {
        // 参考美团标准，3秒阈值
        config.blockMonitorTimeout = 3.0;
        config.blockMonitorEnable = YES;
    }
    
    // 开启进程内还原堆栈
    config.symbolicateInProcessEnable = YES;
    // 模拟器 或者 调试状态 不上报crash日志
    if ([IMYAppPlater isDebugging] || IMYSystem.simulator) {
        config.blockMonitorEnable = NO;
        [Bugly closeCrashReport];
    }
    
    BOOL needBuglyStart = YES;
    
#ifdef DEBUG
    // Debug包都打开卡顿采集
    config.blockMonitorEnable = YES;
    
    // 调试状态不开启Bugly
    if (IMYAppPlater.isDebugging) {
        needBuglyStart = NO;
    }
#endif

    // Bugly SDK 初始化
    if (needBuglyStart) {
        [Bugly startWithAppId:buglyAppId config:config];
    }
    
    [[IMYPublicAppHelper shareAppHelper].useridChangedSignal subscribeNext:^(id x) {
        dispatch_async(self.ioQueue, ^{
            [Bugly setUserIdentifier:[IMYPublicAppHelper shareAppHelper].userid];
        });
    }];
#endif
}

#pragma mark - 冷启动耗时

- (void)setPredefineLaunchType:(IMYReportPredefineLaunchType)predefineLaunchType {
#ifdef __has_umeng_apm
    UMPredefineLaunchType umType = 0;
    switch (predefineLaunchType) {
        case IMYReportPredefineLaunchTypeLaunchingEnd:
            umType = UMPredefineLaunchType_DidFinishLaunchingEnd;
            break;
        case IMYReportPredefineLaunchTypeViewDidLoadEnd:
            umType = UMPredefineLaunchType_ViewDidLoadEnd;
            break;
        case IMYReportPredefineLaunchTypeViewDidAppearEnd:
            umType = UMPredefineLaunchType_ViewDidAppearEnd;
            break;
    }
    if (![_launchedTypes containsObject:@(umType)]) {
        [_launchedTypes addObject:@(umType)];
        [UMLaunch setPredefineLaunchType:umType];
    }
#endif
}

- (void)predefineBeginLaunch:(NSString *)methodName {
#ifdef __has_umeng_apm
    if (!methodName.length) {
        return;
    }
    [UMLaunch beginLaunch:methodName];
#endif
}

- (void)predefineEndLaunch:(NSString *)methodName {
#ifdef __has_umeng_apm
    if (!methodName.length) {
        return;
    }
    [UMLaunch endLaunch:methodName];
#endif
}

#pragma mark - 上报自定义错误

- (void)reportError:(NSError *)error {
    if (!error) {
        return;
    }
#ifdef __has_bugly_sdk
    dispatch_async(self.ioQueue, ^{
        [Bugly reportError:error];
    });
#endif
}

- (void)reportErrorWithCategory:(IMYReportErrorCategory const)category
                           name:(NSString *)aName
                         reason:(NSString *)aReason
                      callStack:(NSArray *)aStackArray
                      extraInfo:(NSDictionary *)aInfo {
#ifdef __has_bugly_sdk
    NSUInteger buglyCategory = 3;
    switch (category) {
        case IMYReportErrorCategoryCocoa:
            buglyCategory = 3;
            break;
        case IMYReportErrorCategoryDB:
            buglyCategory = 4;
            break;
        case IMYReportErrorCategoryJS:
            buglyCategory = 5;
            break;
        case IMYReportErrorCategoryOther:
            buglyCategory = 6;
            break;
    }
    if (!aStackArray) {
        aStackArray = [NSThread callStackSymbols];
    }
    dispatch_async(self.ioQueue, ^{
        [Bugly reportExceptionWithCategory:buglyCategory
                                      name:aName
                                    reason:aReason
                                 callStack:aStackArray
                                 extraInfo:aInfo
                              terminateApp:NO];
    });
#endif
}

# pragma mark - BuglyDelegate

- (NSString *)attachmentForException:(NSException *)exception {
    // 内部闪退回调
    [self onCrash:exception];
    // 获取闪退追加信息
    NSString *appInfos = [self getReportAppInfos:0];
#ifdef DEBUG
    // 上报到QA接口
    [self reportQAWithException:exception umType:0];
    [self sleepCurrentThreadTime:0.5];
#endif
    return appInfos;
}

# pragma mark - 发生闪退

- (void)onCrash:(NSException *)exception {
    // 存储App运行时间
    self.lastAppRuningTime = round(CFAbsoluteTimeGetCurrent() - self.appStartingTime);
    [self saveLastAppRuningTime:self.lastAppRuningTime];
    // 发送app闪退通知
    [[NSNotificationCenter defaultCenter] postNotificationName:IMYApplicationOnCrashNotification object:exception];
    // 运行时间小于10秒 则直接清空全部缓存
    if (self.lastAppRuningTime < 10) {
        [IMYCacheHelper clearCacheData];
    }
    // 防止假闪退，5秒后清空闪退记录
    imy_asyncMainBlock(5, ^{
        self.lastAppRuningTime = 0;
        [self saveLastAppRuningTime:self.lastAppRuningTime];
    });
}

- (NSTimeInterval)lastCrashAppRuningTime {
    return self.lastAppRuningTime;
}

- (void)sleepCurrentThreadTime:(double const)blockTime {
#ifdef DEBUG
    const CFAbsoluteTime beginTime = CFAbsoluteTimeGetCurrent();
    // 一直循环等待 openURL completionHandler 返回结果，通过真机实验，平均等待 ：(支付宝）0.08秒 ～（京东）2.3秒 左右
    while (YES) {
        const CFAbsoluteTime endTime = CFAbsoluteTimeGetCurrent();
        if (endTime - beginTime > blockTime) {
            // 等待时长超过5秒，则不继续等待，直接返回NO
            break;
        }
        [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate distantFuture]];
    }
#endif
}

- (NSString *)getReportAppInfos:(NSInteger)type {
    // 返回闪退后上报的信息
    NSMutableString *appInfos = [NSMutableString string];
    
    IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
    
    // 追加 编译版本、用户id、身份、皮肤包
    [appInfos appendFormat:@"%@-%@-%ld-%@-", APPBuildVersion, appHelper.userid, appHelper.userMode, appHelper.themeID];
    
    // App运行时间
    CFAbsoluteTime runTime = CFAbsoluteTimeGetCurrent() - self.appStartingTime;
    [appInfos appendFormat:@"%.2lf-%d=", runTime, type];
    
    // 追加页面路径
    [appInfos appendFormat:@"%@-", [IMYMeetyouHTTPHooks currentPageHistory]];
    
    // 追加最后一个页面信息
    IMYGAPageEventStore *lastEventStore = [IMYGAPageEventStore currentLastPageEventStore];
    [appInfos appendFormat:@"%@-%@=", lastEventStore.params.imy_jsonString, lastEventStore.headers.imy_jsonString];
    
    // 返回字符串
    return [appInfos copy];
}

#ifdef DEBUG

- (void)reportQAWithException:(NSException * const)buglyException umType:(NSInteger)umType {
    // 上传到我们自己的 QA 系统中，需求： https://www.tapd.cn/33735684/markdown_wikis/#1133735684001002813
    // 所有App 都会进行上报，由 QA 那边进行分配
    if (IMYSystem.simulator || IMYAppPlater.isDebugging) {
        // 过滤模拟器 和 Debug中的数据
        return;
    }
    
    static BOOL isCrashReportQA = NO;
    if (isCrashReportQA) {
        return;
    }
    isCrashReportQA = YES;
    
    NSString *exceptionName = nil;
    NSString *exceptionReason = nil;
    NSString *exceptionCallStack = nil;
    if (!buglyException) {
        NSString *dirPath = [[NSString imy_cachesDirectory] stringByAppendingPathComponent:@"WPKMobi/Seeyou/Reports"];
        NSArray *fileNames = [NSFileManager.defaultManager contentsOfDirectoryAtPath:dirPath error:nil];
        
        // 读取KSCrash写到磁盘的文件内容
        NSDictionary *crashInfo =  nil;
        for (NSString *name in fileNames) {
            if ([name hasSuffix:@".json"]) {
                NSString *crashJsonPath = [dirPath stringByAppendingPathComponent:name];
                NSData *jsonData = [NSData dataWithContentsOfFile:crashJsonPath];
                crashInfo = jsonData.imy_jsonObject[@"crash"];
                if (crashInfo.count > 0) {
                    break;
                }
            }
        }
        exceptionName = crashInfo[@"reason"] ?: crashInfo[@"error"][@"mach"][@"code_name"] ?: crashInfo[@"error"][@"signal"][@"name"] ?: @"未知：请到友盟查看";
        exceptionReason = crashInfo[@"diagnosis"] ?: exceptionName;
        
        NSArray *contents = crashInfo[@"threads"][@"backtrace"][@"contents"];
        exceptionCallStack = [[contents map:^id(NSDictionary *element) {
            return element[@"symbol_name"];
        }] componentsJoinedByString:@"\n"] ?: [crashInfo[@"error"] imy_jsonString] ?: @"未知：请到友盟查看";
    } else {
        exceptionName = buglyException.name;
        exceptionReason = buglyException.reason;
        exceptionCallStack = buglyException.userInfo[@"callStack"];
    }
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"pkg"] = APPBundleIdentifier;
    params[@"version"] = APPVersion;
    params[@"subversion"] = APPBuildVersion;
    params[@"crashtime"] = [NSDate date].imy_getDateTimeString;
    params[@"android_version"] = [IMYSystem stringVersion];
    params[@"phone_model"] = [UIDevice imy_platform];
    params[@"platform"] = @"iOS";
    params[@"idfa"] = [UIDevice imy_idfaString];
    params[@"topactivity"] = [(id)[UIViewController imy_currentViewControlloer] ga_pageName];
    
    NSMutableString *mutableString = [NSMutableString string];
    [mutableString appendFormat:@"name:%@\n", exceptionName];
    [mutableString appendFormat:@"reason:%@\n", exceptionReason];
    [mutableString appendFormat:@"uid:%@\n", [IMYPublicAppHelper shareAppHelper].userid];
    [mutableString appendFormat:@"umtype:%@\n", @(umType)];
    
    UIViewController *topVC = [UIViewController imy_currentTopViewController];
    UIViewController *lastVC = [topVC imy_navigationController].viewControllers.lastObject ?: topVC;
    
    [mutableString appendFormat:@"pageinfo:%@\n\n", [[(id)lastVC ga_appendParams] imy_jsonString]];
    [mutableString appendFormat:@"%@", exceptionCallStack];
    params[@"stack_info"] = mutableString.copy;

    NSData *data = [params imy_jsonData];
    // 缓存错误信息，等下次启动继续上报
    [self reportQAWithData:data needCache:YES];
    
    // 卡死当前线程0.5秒，等待QA上报完毕再继续
    [self sleepCurrentThreadTime:0.5];
}

- (void)reportQAWithData:(NSData *)data needCache:(BOOL const)needCache {
    NSString *crashCachePath = [NSString imy_pathForName:@"QACrashInfo.txt" atCachesDirs:@"QATest"];
    // 写入缓存
    if (data.length > 0 && needCache) {
        [data writeToFile:crashCachePath atomically:YES];
    }
    // 读取缓存
    if (!data.length && [NSFileManager.defaultManager fileExistsAtPath:crashCachePath]) {
        data = [NSData dataWithContentsOfFile:crashCachePath];
    }
    // 无数据
    if (!data.length) {
        return;
    }
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:@"http://39.107.74.137:6677/crashinfo"]];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setValue:[NSString stringWithFormat:@"%ld", (long)data.length] forHTTPHeaderField:@"Content-Length"];
    [request setValue:[IMYPublicAppHelper shareAppHelper].myclient forHTTPHeaderField:@"myclient"];
    if (NSBundle.enableMYAppInfo) {
        [request setValue:[IMYPublicAppHelper shareAppHelper].myappinfo forHTTPHeaderField:@"myappinfo"];
    }
    [request setHTTPBody:data];
    
    // 上报到QA
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        // 请求结束，删除缓存文件
        [[NSFileManager defaultManager] removeItemAtPath:crashCachePath error:nil];
    }];
    task.priority = NSURLSessionTaskPriorityHigh;
    [task resume];
}

#endif

@end

#ifdef __has_umeng_apm
#ifdef DEBUG

/// Debug包 都当做友盟测试机
@interface WPKConfigManager : NSObject
- (NSDictionary *)appConfigs;
@end

@implementation WPKConfigManager (IMYDebugForceOpen)

+ (void)load {
    [WPKConfigManager imy_swizzleMethod:@selector(appConfigs) withMethod:@selector(imyfix_appConfigs) error:nil];
}

- (BOOL)isHitWL {
    [self setValue:@1 forKey:@"_isHitWL"];
    return YES;
}

- (NSDictionary *)imyfix_appConfigs {
    NSMutableDictionary *xConfigs = [[self imyfix_appConfigs] mutableCopy];
    NSMutableArray *xActions = [xConfigs[@"actions"] mutableCopy];
    [xActions.copy enumerateObjectsUsingBlock:^(NSDictionary *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([@"apm_patrace_switch_rate" isEqualToString:obj[@"opt"]]) {
            [xActions removeObject:obj];
        }
    }];
    // 卡顿率 百分百采样
    [xActions addObject:@{
        @"opt" : @"apm_patrace_switch_rate",
        @"set" : @100,
    }];
    xConfigs[@"actions"] = [xActions copy];
    return [xConfigs copy];
}

@end

@interface WPK_AFURLSessionTaskSwizzling : NSObject

- (NSURLSessionTaskState)state;

@end

static NSString * const AFNSURLSessionTaskDidResumeNotification  = @"com.alamofire.networking.nsurlsessiontask.resume";
static NSString * const AFNSURLSessionTaskDidSuspendNotification = @"com.alamofire.networking.nsurlsessiontask.suspend";

static NSString * const AFNSURLSessionTaskWillResumeNotification  = @"com.alamofire.networking.nsurlsessiontask.will.resume";
static NSString * const AFNSURLSessionTaskWillSuspendNotification = @"com.alamofire.networking.nsurlsessiontask.will.suspend";

@implementation WPK_AFURLSessionTaskSwizzling (Hook)

- (void)af_resume {
    NSAssert([self respondsToSelector:@selector(state)], @"Does not respond to state");
    NSURLSessionTaskState state = [self state];
    
    if (state != NSURLSessionTaskStateRunning) {
        [[NSNotificationCenter defaultCenter] postNotificationName:AFNSURLSessionTaskWillResumeNotification object:self];
    }
    
    [self af_resume];
    
    if (state != NSURLSessionTaskStateRunning) {
        [[NSNotificationCenter defaultCenter] postNotificationName:AFNSURLSessionTaskDidResumeNotification object:self];
    }
}

- (void)af_suspend {
    NSAssert([self respondsToSelector:@selector(state)], @"Does not respond to state");
    NSURLSessionTaskState state = [self state];
    
    if (state != NSURLSessionTaskStateSuspended) {
        [[NSNotificationCenter defaultCenter] postNotificationName:AFNSURLSessionTaskWillSuspendNotification object:self];
    }
    
    [self af_suspend];
    
    if (state != NSURLSessionTaskStateSuspended) {
        [[NSNotificationCenter defaultCenter] postNotificationName:AFNSURLSessionTaskDidSuspendNotification object:self];
    }
}

@end

#endif
#endif
