//
//  IMYSystem.h
//  IMYFoundation
//
//  Created by mario on 15/12/4.
//  Copyright © 2015年 meiyou. All rights reserved.
//

#import <UIKit/UIKit.h>

/// 屏幕宽度
#define SCREEN_WIDTH IMYSystem.screenWidth
/// 屏幕高度
#define SCREEN_HEIGHT IMYSystem.screenHeight

#define SCREEN_STATUSBAR_HEIGHT IMYSystem.screenStatusBarHeight
#define SCREEN_NAVIGATIONBAR_HEIGHT IMYSystem.screenNavigationBarHeight
#define SCREEN_TABBAR_HEIGHT IMYSystem.screenTabBarHeight
#define SCREEN_TABBAR_SAFEBOTTOM_MARGIN IMYSystem.screenTabBarSafeBottomMargin
#define SCREEN_LAND_TABBAR_SAFEBOTTOM_MARGIN IMYSystem.screenLandscapeTabBarSafeBottomMargin
#define SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT IMYSystem.screenStatusBarAndNavigationBarHeight

/// 系统 scale
#define SCREEN_SCALE IMYSystem.screenScale
///系统屏幕宽度和320的比例
#define SCREEN_RATIO IMYSystem.screen320Ratio
///系统屏幕宽度和375的比例
#define SCREEN_RATIO_375 IMYSystem.screen375Ratio
///按320的设计，进行尺寸转换
#define SCREEN_By320(x) ceil((x) * SCREEN_RATIO)
///按375的设计，进行尺寸转换
#define SCREEN_By375(x) ceil((x) * SCREEN_RATIO_375)

#define IOS_V($x) @available(iOS $x, *)

#define IOS5 IOS_V(5)
#define IOS6 IOS_V(6)
#define IOS7 IOS_V(7)
#define IOS8 IOS_V(8)
#define IOS9 IOS_V(9)
#define IOS10 IOS_V(10)
#define IOS11 IOS_V(11)
#define IOS12 IOS_V(12)
#define IOS13 IOS_V(13)

#define iPhone5 IMYSystem.iPhone5Screen
#define iPhone6 IMYSystem.iPhone6Screen
#define iPhone6p IMYSystem.iPhone6pScreen
#define iPhoneX IMYSystem.iPhoneXScreen

///是否模拟器
#ifndef isSimulator
#define isSimulator IMYSystem.simulator
#endif

// 从375的设计尺寸 转为 当前分辨率的 frame
extern inline CGRect IMYFrameBy375Design(CGFloat x, CGFloat y, CGFloat width, CGFloat height);
extern inline CGRect IMYFrameBy375DesignRect(CGRect designFrame);

extern inline NSInteger IMYIntegerBy375Design(CGFloat x);

extern inline CGPoint IMYPointBy375Design(CGFloat x, CGFloat y);
extern inline CGPoint IMYPointBy375DesignPoint(CGPoint designPoint);

extern inline CGSize IMYSizeBy375Design(CGFloat width, CGFloat height);
extern inline CGSize IMYSizeBy375DesignSize(CGSize designSize);

NS_ASSUME_NONNULL_BEGIN

@interface IMYSystem : NSObject

#pragma mark - 系统信息
/// 系统版本
+ (NSString *)stringVersion;
+ (NSInteger)intVersion;
+ (float)floatVersion;

+ (BOOL)iOS5 __deprecated_msg("不支持的系统版本");

+ (BOOL)iOS6 __deprecated_msg("不支持的系统版本");

+ (BOOL)iOS7 __deprecated_msg("不支持的系统版本");

+ (BOOL)iOS8 __deprecated_msg("不支持的系统版本");

+ (BOOL)iOS9 __deprecated_msg("不支持的系统版本");

+ (BOOL)iOS10 __deprecated_msg("不支持的系统版本");

+ (BOOL)iOS11;

+ (BOOL)iOS12;

+ (BOOL)iOS13;

#pragma mark - 机型信息
/// 同个年份的版本号一致： (6,6p)：7, (6s,6sp)：8, (7,7p)：9, (8,8p,x)：10
+ (NSInteger)iPhoneVersion;

+ (BOOL)iPhone5Screen;

+ (BOOL)iPhone6Screen;

+ (BOOL)iPhone6pScreen;

+ (BOOL)iPhoneXScreen;

+ (BOOL)simulator;

+ (BOOL)isIpad;

+ (CGFloat)screenWidth;

+ (CGFloat)screenHeight;

+ (CGFloat)screenScale;

+ (CGFloat)screenStatusBarHeight;

+ (CGFloat)screenNavigationBarHeight;

+ (CGFloat)screenTabBarHeight;

+ (CGFloat)screenTabBarSafeBottomMargin;

+ (CGFloat)screenLandscapeTabBarSafeBottomMargin;

+ (CGFloat)screenStatusBarAndNavigationBarHeight;

+ (CGFloat)screen320Ratio;

+ (CGFloat)screen375Ratio;

+ (CGFloat)screen414Ratio;

// 上个应用版本，有值：从该版本升级上来的， nil：还未升级过
+ (NSString *)lastAppVersion;

// 历史版本记录，包含当前版本
+ (NSArray<NSString *> *)historyAppVersions;

// 重新计算 UI变量
+ (void)setupUIValues;

/// 跳转到系统push设置页
+ (void)gotoPushSettings;

/// 当前系统正在显示键盘
+ (BOOL)isShowingKeyboardWindow;

/// 获取粘贴板内容，会自动使用 iOS14 detectPatternsForPatterns API 进行判断
/// 有粘贴板有值才会真正读取（系统会弹窗）, 并且会暂停bugly的卡顿检测
+ (void)getUIPasteboardStringWithCompleted:(void(^)(NSString * _Nullable keyword))completedBlock;

@end

NS_ASSUME_NONNULL_END
