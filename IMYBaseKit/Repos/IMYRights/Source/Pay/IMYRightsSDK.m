//
//  IMYRightsSDK.m
//  IMYBaseKit
//
//  Created by ljh on 2024/1/25.
//

#import "IMYRightsSDK.h"
#import "IMYBaseKit.h"
#import "IMYRightsPaySDK.h"
#import "IMYHTTPSignatures.h"
#import <IOC-Protocols/IOCAppInfo.h>

/// 刷新会员中心页面
NSNotificationName const IMYMRHomeRefreshDataNotification = @"IMYMRHomeRefreshDataNotification";

/// 会员权益过期type
static NSInteger const kIMYRightsSubTypeExpired = 6;

/// 返回过期的权益对象
@interface IMYRightsModel (Expired)
+ (instancetype)modelWithExpired:(BOOL)isExpired latestType:(NSInteger)latestType;
@end

#pragma mark - 权益SDK

@interface IMYRightsSDK ()

/// 接口成功回调
@property (nonatomic, strong) RACSignal *loadedSignal;
@property (nonatomic, assign) BOOL inABTest;
@property (nonatomic, assign) IMYRightsType lastUnusedRightsType;
@property (nonatomic, assign) BOOL isNotNotify;

/// 简略版的权益信息
@property (nonatomic, assign) IMYRightsType mRightsType;
@property (nonatomic, assign) NSInteger mSubType;
@property (atomic, copy) NSString *mUserId;

/// 详细权益信息
@property (atomic, strong) IMYRightsModel *detailModel;

/// 避免接口爆炸，采用递增重试
@property (nonatomic, assign) NSInteger retryDelay;
@property (nonatomic, assign) NSInteger retryCount;

/// 是否支付校验通过，如果通过，服务器没有返回权益，则进行递增重试
@property (nonatomic, assign) BOOL isPayVerifyed;

/// 请求 my_rights 接口的错误信息
@property (atomic, copy) NSDictionary *lastGetRightsErrors;

/// AB入组信息
@property (atomic, copy) NSString *remoteABExpValue;

/// get items hash key
@property (atomic, copy) NSString *lastItemsHashKey;

@end

@implementation IMYRightsSDK

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    [IMYRightsSDK sharedInstance];
}

+ (instancetype)sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.loadedSignal = [RACSubject subject];
        self.mUserId = [IMYPublicAppHelper shareAppHelper].userid;
        
        [self loadCache];
        [self addNotifications];
        
        // 初始化IAP监听
        [IMYRightsPaySDK sharedInstance];
        
        // 从 879 之前版本升级上来： 当前有开启会员 & 用户处于青少年模式，则自动帮用户退出请少年模式, 只修复一次
        const BOOL isFixed = [[NSUserDefaults standardUserDefaults] boolForKey:@"IMYRightsFixedYoungMode"];
        if (!isFixed) {
            if (self.currentRightsType != 0 && IMYPublicAppHelper.shareAppHelper.useYoungMode) {
                // 强制关闭青少年模式
                [IMYHIVE_BINDER(IOCAppInfo) forceCloseYoungMode];
            }
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"IMYRightsFixedYoungMode"];
        }
    }
    return self;
}

- (void)addNotifications {
    // 存储当前的权限状态
    self.lastUnusedRightsType = self.currentRightsType;
    
    // uid 变化
    [[IMYPublicAppHelper shareAppHelper].useridChangedSignal subscribeNext:^(NSString *newUserId) {
        // 直接重置权益信息，等待AB回调后，重新请求接口
        if ([newUserId isEqualToString:self.mUserId]) {
            return;
        }
        self.mRightsType = 0;
        self.mSubType = 0;
        self.mUserId = newUserId;
        self.detailModel = nil;
        
        // 权限发生了变化，需要发通知
        if (self.lastUnusedRightsType != self.currentRightsType) {
            [self postNotification];
        }
    }];
    
    // 监听AB接口 (二次启动有AB接口)
    [[IMYABTestManager sharedInstance].loadedSignal subscribeNext:^(id  _Nullable x) {
        // 刷新AB入组状态
        [self refreshABTestStatus];
        // 启动就需要，刷新权益接口
        self.retryDelay = 0;
        self.retryCount = 0;
        [self refreshData];
    }];
    
    // 监听配置中心变化
    [[IMYConfigsCenter sharedInstance].loadedSignal subscribeNext:^(id  _Nullable x) {
        // 刷新AB入组状态
        const BOOL oldAB = self.inABTest;
        [self refreshABTestStatus];
        // 配置不相等，刷新权益接口
        if (oldAB != self.inABTest) {
            [self refreshData];
        }
    }];
    
    // 支付完成的回调
    [[NSNotificationCenter defaultCenter] addObserverForName:IMYRightsPaySDK.payVerifyCompletedNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        // 支付完成，刷新权益接口
        self.isPayVerifyed = YES;
        [self refreshData];
        // 检测权益是否下发成功
        [self delayCheckRightsGetSuccess];
    }];
    
    // 二次启动，刷新权益接口
    [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationWillEnterForegroundNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        // 刷新权益接口
        [self refreshData];
    }];
}

- (void)refreshABTestStatus {
    // 刷新会员AB入组信息
    self.remoteABExpValue = nil;
    // 经期实验
    IMYABTestExperiment *jingqiExp = [[IMYABTestManager sharedInstance] experimentForKey:@"viptest"];
    const BOOL isJingqiAB = ([jingqiExp.vars integerForKey:@"is_show"] == 1);
    if (isJingqiAB && !self.remoteABExpValue) {
        self.remoteABExpValue = jingqiExp.exp_value;
    }
    // 孕期实验
    IMYABTestExperiment *yunqiExp = [[IMYABTestManager sharedInstance] experimentForKey:@"viptest_pregnancy"];
    const BOOL isYunqiExpAB = ([yunqiExp.vars integerForKey:@"is_show"] == 1);
    if (isYunqiExpAB && !self.remoteABExpValue) {
        self.remoteABExpValue = yunqiExp.exp_value;
    }
    // 辣妈实验
    IMYABTestExperiment *lamaExp = [[IMYABTestManager sharedInstance] experimentForKey:@"viptest_baby"];
    const BOOL isLamaAB = ([lamaExp.vars integerForKey:@"is_show"] == 1);
    if (isLamaAB && !self.remoteABExpValue) {
        self.remoteABExpValue = lamaExp.exp_value;
    }
    
    // 本地强制入组
    NSString * const forceABKey = [NSString stringWithFormat:@"my-rights-enable-%@", self.mUserId];
    const BOOL isForceAB = ([[IMYKV defaultKV] integerForKey:forceABKey] == 2);
    
    // 入组会员实验
    const BOOL openABTest = (isJingqiAB || isYunqiExpAB || isLamaAB || isForceAB || IMYRightsSDK.isSubAuditReview);
    self.inABTest = openABTest;
}

+ (BOOL)isSubAuditReview {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.sub_audit"];
    if (!group && IMYNetState.networkEnable && ![[NSLocale currentLocale].countryCode isEqualToString:@"CN"]) {
        // 配置接口加载失败，如果处于非中国区，也当做命中配置
        return YES;
    }
    if ([group boolForKey:@"apple_switch"]) {
        NSString *appVersion = [group stringForKey:@"apple_version"];
        if ([APPVersion isNewerOrEqualVersionThan:appVersion]) {
            return YES;
        }
    }
    return NO;
}

#pragma mark - 我的权益

- (IMYRightsType)currentRightsType {
    // 每次调用 都会实时判断有效期
    return _mRightsType;
}

- (NSInteger)currentSubscribeType {
    return _mSubType;
}

- (BOOL)hasRightsItemForKey:(NSString *)itemKey {
    return [self getRightsItemModelForKey:itemKey] != nil;
}

- (IMYRightsItemModel *)getRightsItemModelForKey:(NSString *)itemKey {
    if (!itemKey.length) {
        return nil;
    }
    IMYRightsModel *model = self.detailModel;
    for (IMYRightsItemModel *item in model.myRights) {
        if ([item.rights_item_key isEqualToString:itemKey]) {
            return item;
        }
    }
    return nil;
}

- (void)refreshData {
    // 重置通知标志位
    self.isNotNotify = NO;
    // 100ms限流
    NSString *queueKey = NSStringFromClass(self.class);
    NSString *retryKey = [queueKey stringByAppendingString:@"RetryX"];
    [NSObject imy_cancelBlockForKey:retryKey];
    [NSObject imy_asyncBlock:^{
        [self requestReal];
    } onQueue:dispatch_get_global_queue(0, 0) afterSecond:0.1 forKey:retryKey];
}

- (void)refreshDataNotNotify {
    // 先执行刷新，再修改 通知标志位
    [self refreshData];
    self.isNotNotify = YES;
}

- (void)requestReal {
    if (!IMYPublicAppHelper.isJingqi) {
        // 只有经期App才请求
        return;
    }
    // 获取历史是否有权益
    BOOL const hasVIP = (self.currentRightsType != 0);
    // 返回值是 v2 类型
    [[IMYPublicServerRequest getPath:@"v3/sub/my_rights" host:sub_seeyouyima_com params:nil headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        // 刷新缓存
        NSDictionary *rootJSON = x.responseObject;
        if (hasVIP && !rootJSON.count) {
            // 异常数据，3秒后重新请求一次
            imy_asyncBlock(3, ^{
                [self refreshData];
            });
        }
        BOOL isRightsExpired = NO;
        NSInteger latestType = 0;
        if (rootJSON.count > 5 && [self validResponseRootJSON:rootJSON]) {
            self.detailModel = [rootJSON toModel:IMYRightsModel.class];
        } else {
            isRightsExpired = [rootJSON[@"is_expired"] boolValue];
            latestType = [rootJSON[@"latest_type"] integerValue];
            rootJSON = nil;
            self.detailModel = nil;
        }
        // 存储权益信息
        [self saveCacheWithRootJSON:rootJSON isExpired:isRightsExpired latestType:latestType];
        // 上报权益项
        [self delayPostRightsItemsWithRootJSON:rootJSON hasVIP:hasVIP];
        
        // 支付成功，但是服务端没返回权益，则每隔5秒重试一次，最多3次
        if (self.isPayVerifyed && self.currentRightsType == IMYRightsTypeNone) {
            self.isPayVerifyed = NO;
            imy_asyncMainBlockInRunLoopModeRepeat(nil, 3, 5, ^BOOL(NSInteger index) {
                if (self.currentRightsType == IMYRightsTypeNone) {
                    // 如果还是无权益，继续请求一次权益接口，并继续循环
                    [self refreshData];
                    return NO;
                }
                return YES;
            });
        } else {
            // 修正重试延迟
            self.retryDelay = 0;
            self.retryCount = 0;
            // 发送 loaded 通知
            if (!self.isNotNotify || self.lastUnusedRightsType != self.currentRightsType) {
                [self postNotification];
            }
        }
    } error:^(NSError *error) {
        // 每次增加2秒，最多10秒 + 最多20次
        self.retryDelay = MIN(10, self.retryDelay + 2);
        self.retryCount += 1;
        if (self.retryCount <= 20) {
            NSString *queueKey = NSStringFromClass(self.class);
            NSString *retryKey = [queueKey stringByAppendingString:@"RetryX"];
            [NSObject imy_asyncBlock:^{
                if (self.isNotNotify) {
                    [self refreshDataNotNotify];
                } else {
                    [self refreshData];
                }
            } onQueue:dispatch_get_global_queue(0, 0) afterSecond:self.retryDelay forKey:retryKey];
        }
        // 存储最后一次的接口错误信息
        if (!self.lastGetRightsErrors || error.af_responseData.length > 0) {
            self.lastGetRightsErrors = @{
                @"code" : @(error.code),
                @"reason" : error.localizedFailureReason ?: error.domain ?: @"",
                @"body" : [error.af_responseData imy_jsonObject] ?: @{},
            };
        }
    }];
}

/// 校验服务端签名
- (BOOL)validResponseRootJSON:(NSDictionary *)rootJSON {
    BOOL isValid = [self validResponseRootJSON:rootJSON userId:[IMYPublicAppHelper shareAppHelper].userid];
    if (!isValid) {
        // 无效，尝试用临时工ID进行验证
        NSString *tempId = [IMYHIVE_BINDER(IOCAppInfo) getAccountModelUserID];
        if (tempId.length > 0) {
            isValid = [self validResponseRootJSON:rootJSON userId:tempId];
        }
    }
    return isValid;
}

- (BOOL)validResponseRootJSON:(NSDictionary * const)rootJSON userId:(NSString * const)userId {
    if (!rootJSON.count) {
        return NO;
    }
    NSMutableString *allSignString = [NSMutableString string];
    [allSignString appendFormat:@"%@", userId];
    [allSignString appendFormat:@"%@", rootJSON[@"transaction_id"]];
    [allSignString appendFormat:@"%@", rootJSON[@"type"]];
    [allSignString appendFormat:@"%@", rootJSON[@"sub_type"]];
    [allSignString appendFormat:@"%@", rootJSON[@"title"]];
    [allSignString appendFormat:@"%@", rootJSON[@"start_time"]];
    [allSignString appendFormat:@"%@", rootJSON[@"expires_at"]];
    
    NSArray *my_rights = rootJSON[@"my_rights"];
    if (my_rights.count > 0) {
        NSMutableArray *keys = [NSMutableArray array];
        for (NSDictionary *info in my_rights) {
            [keys addObject:info[@"rights_item_key"] ?: @""];
        }
        [keys sortUsingComparator:^NSComparisonResult(NSString *obj1, NSString *obj2) {
            return [obj1 compare:obj2];
        }];
        [allSignString appendFormat:@"%@", [keys componentsJoinedByString:@""]];
    }
    NSString *clientSign = [IMYHTTPSignatures base64HmacSha1WithContent:allSignString secretKey:@"b7f92b2117590d80622d632934c54443"];
    NSString *serverSign = rootJSON[@"sign"];
    return [clientSign isEqualToString:serverSign];
}

- (void)postNotification {
    imy_asyncMainBlock(^{
        self.lastUnusedRightsType = self.currentRightsType;
        [((RACSubject *)self.loadedSignal) sendNext:self];
    });
}

- (void)loadCache {
    // 刷新AB入组信息
    [self refreshABTestStatus];
    
    // 先加载简略权限信息
    NSString *simpleData = [[IMYUserDefaults standardUserDefaults] stringForKey:@"my-rights-simple-data"];
    NSArray *components = [simpleData componentsSeparatedByString:@"-"];
    if (components.count < 3) {
        // 数据格式不对
        return;
    }
    
    // 存储的uid不一致，不加载缓存
    if (![self.mUserId isEqualToString:components[0]]) {
        return;
    }
    
    // 具体的权益信息
    self.mRightsType = [components[1] integerValue];
    self.mSubType = [components[2] integerValue];
    
    // 异步读取完整缓存
    imy_asyncBlock(^{
        NSString *filePath = [NSString imy_pathForName:@"my-rights.json" atDocumentsDirs:@"my-base/cache"];
        NSData *fileData = [NSData dataWithContentsOfFile:filePath];
        NSDictionary *rootJSON = nil;
        if (fileData.length > 0) {
            rootJSON = [fileData imy_jsonObject];
        }
        if (rootJSON.count > 0 && [self validResponseRootJSON:rootJSON]) {
            IMYRightsModel *detailModel = [rootJSON toModel:IMYRightsModel.class];
            if (!self.detailModel) {
                self.detailModel = detailModel;
            }
        } else {
            // 用户权益过期
            if (!self.detailModel && self.currentSubscribeType == kIMYRightsSubTypeExpired) {
                self.detailModel = [IMYRightsModel modelWithExpired:YES latestType:self.mRightsType];
            }
        }
    });
}

- (void)saveCacheWithRootJSON:(NSDictionary *)rootJSON isExpired:(BOOL const)isExpired latestType:(NSInteger const)latestType {
    // 存储简略权益信息
    IMYRightsModel *model = self.detailModel;
    if (!model) {
        model = [IMYRightsModel modelWithExpired:isExpired latestType:latestType];
        self.detailModel = model;
    }
    self.mRightsType = model.type;
    self.mSubType = model.sub_type;
    
    // 存储简略权益信息
    NSString *simpleData = [NSString stringWithFormat:@"%@-%ld-%ld-%ld", self.mUserId, self.mRightsType, self.mSubType, 0];
    [[IMYUserDefaults standardUserDefaults] setObject:simpleData forKey:@"my-rights-simple-data"];
    
    // 处于订阅中，强制命中AB
    if (self.currentRightsType != IMYRightsTypeNone || self.mSubType != IMYRightsTypeNone) {
        if (!self.inABTest) {
            NSString * const forceABKey = [NSString stringWithFormat:@"my-rights-enable-%@", self.mUserId];
            [[IMYKV defaultKV] setInteger:2 forKey:forceABKey];
            self.inABTest = YES;
        }
    }
    
    // 异步存储JSON文件
    imy_asyncBlock(^{
        NSString *filePath = [NSString imy_pathForName:@"my-rights.json" atDocumentsDirs:@"my-base/cache"];
        if (rootJSON.count > 0) {
            NSData *writeData = [rootJSON imy_jsonData];
            if (writeData.length > 0) {
                [writeData writeToFile:filePath atomically:YES];
            }
        } else {
            [[NSFileManager defaultManager] removeItemAtPath:filePath error:nil];
        }
    });
}

/// 监测权益是否发放成功 : http://wiki.meiyou.com/pages/viewpage.action?pageId=195566546
- (void)delayCheckRightsGetSuccess {
    if (self.currentRightsType != IMYRightsTypeNone && self.detailModel.myRights.count > 0) {
        return;
    }
    imy_asyncBlock(10, ^{
        if (self.currentRightsType != IMYRightsTypeNone && self.detailModel.myRights.count > 0) {
            return;
        }
        // 上报权益下发错误埋点
        NSMutableDictionary *detailMap = [NSMutableDictionary dictionaryWithDictionary:[IMYRightsPaySDK sharedInstance].lastVerifyInfos];
        detailMap[@"error"] = self.lastGetRightsErrors ?: @{};
        [IMYErrorTraces postWithType:IMYErrorTraceTypeNoneUI
                            pageName:IMYMeetyouHTTPHooks.currentPageName
                            category:IMYErrorTraceCategoryRights
                             message:@"rights-get-fails"
                              detail:detailMap];
    });
}

/// 上报权益下发项 : http://wiki.meiyou.com/pages/viewpage.action?pageId=195566546
- (void)delayPostRightsItemsWithRootJSON:(NSDictionary *)rootJSON hasVIP:(BOOL)hasVIP {
    // 加个2秒滤重
    [NSObject imy_asyncBlock:^{
        if (!hasVIP && rootJSON.count < 5 && !self.detailModel) {
            // 用户无权益，不用上报
            return;
        }
        // 生成埋点参数
        NSString *itemsHashKey = nil;
        NSMutableDictionary *detailMap = [NSMutableDictionary dictionary];
        NSUInteger errorType = IMYErrorTraceTypeOthers;
        NSString *errorMsg = @"rights-get-items";
        if (!self.detailModel) {
            // 本地校验失败，则上报整个Body
            errorType = IMYErrorTraceTypeNoneUI;
            errorMsg = @"rights-get-nosign";
            detailMap[@"root"] = rootJSON;
            // hash key
            itemsHashKey = @"rights-get-nosign";
        } else {
            // 获取权限聚合
            detailMap[@"type"] = @(self.currentRightsType);
            detailMap[@"sub_type"] = @(self.currentSubscribeType);
            NSArray *itemKeys = [self.detailModel.myRights map:^id (IMYRightsItemModel *element) {
                return element.rights_item_key ?: element.rights_item_id ?: element.rights_title ?: @"";
            }];
            detailMap[@"items"] = itemKeys;
            // hash key
            itemsHashKey = [itemKeys componentsJoinedByString:@","];
        }
        if ([self.lastItemsHashKey isEqualToString:itemsHashKey]) {
            return;
        }
        self.lastItemsHashKey = itemsHashKey;
        // 参数不一致才需要上报
        [IMYErrorTraces postWithType:errorType
                            pageName:IMYMeetyouHTTPHooks.currentPageName
                            category:IMYErrorTraceCategoryRights
                             message:errorMsg
                              detail:detailMap];
    } onQueue:dispatch_get_global_queue(0, 0) afterSecond:2 forKey:@"kDelayPostRightsItems"];
}

#pragma mark - 购买权益

- (void)payWithPlanId:(NSString *)planId 
              priceId:(NSString *)priceId
         appendParams:(NSDictionary *)appendParams
        progressBlock:(void (^)(NSDictionary *, NSInteger))progressBlock
       completedBlock:(void (^)(NSDictionary *, NSError *))completedBlock {
    // 调用支付SDK
    [[IMYRightsPaySDK sharedInstance] payWithSubPlanId:planId 
                                               priceId:priceId
                                             renewalId:nil
                                          appendParams:appendParams
                                         progressBlock:progressBlock
                                        completedBlock:^(NSDictionary *result, NSError *error) {
        // 购买成功，刷新权益接口 (已经到达receipt验证流程)
        if (!error || error.code == -201) {
            self.retryDelay = 0;
            self.isPayVerifyed = YES;
            [self refreshData];
            // 检测权益是否下发成功
            if (!error) {
                [self delayCheckRightsGetSuccess];
            }
        }
        // 回调外部
        if (completedBlock) {
            completedBlock(result, error);
        }
    }];
}

- (void)restoreWithAppendParams:(NSDictionary *)appendParams
                  progressBlock:(void (^)(NSDictionary *, NSInteger))progressBlock
                 completedBlock:(void (^)(NSDictionary *, NSError *))completedBlock {
    // 调用支付SDK
    [[IMYRightsPaySDK sharedInstance] restoreWithAppendParams:appendParams
                                                progressBlock:progressBlock
                                               completedBlock:^(NSDictionary *result, NSError *error) {
        // 购买成功，刷新权益接口 (已经到达receipt验证流程)
        if (!error || error.code == -201) {
            self.retryDelay = 0;
            self.isPayVerifyed = YES;
            [self refreshData];
            // 检测权益是否下发成功
            if (!error) {
                [self delayCheckRightsGetSuccess];
            }
        }
        // 回调外部
        if (completedBlock) {
            completedBlock(result, error);
        }
    }];
}

- (NSString *)abExpValue {
    if (!self.inABTest) {
        // 未命中实验，直接返回空
        return nil;
    }
    // 如果无服务端值，则手动赋值
    NSString *expValue = self.remoteABExpValue;
    if (!expValue.length) {
        if (IMYURLEnvironmentManager.currentType == IMYURLEnviromentTypeTest) {
            expValue = @"1838-6361";
        } else {
            expValue = @"1407-7342";
        }
    }
    return expValue;
}

@end

#pragma mark - 会员权益详情

@interface IMYRightsModel ()
@property (nonatomic, assign) IMYRightsType type;
@property (nonatomic, assign) NSInteger sub_type;
@property (nonatomic, assign) NSInteger latest_type;
@property (nonatomic, copy) NSString *transaction_id;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *desc;
@property (nonatomic, copy) NSString *images;
@property (nonatomic, copy) NSString *icon;
@property (nonatomic, assign) NSInteger start_time;
@property (nonatomic, assign) NSInteger expires_at;
@property (nonatomic, copy) NSArray<IMYRightsItemModel *> *myRights;
@end

@implementation IMYRightsModel

+ (instancetype)modelWithExpired:(BOOL const)isExpired latestType:(NSInteger const)latestType {
    IMYRightsModel *model = [self new];
    model.sub_type = (isExpired ? kIMYRightsSubTypeExpired : 0);
    model.latest_type = latestType;
    return model;
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{ 
        @"desc" : @"description",
        @"myRights": @"my_rights"
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"myRights": IMYRightsItemModel.class,
    };
}

@end



@interface IMYRightsItemModel ()
@property (nonatomic, copy) NSString *rights_item_id;
@property (nonatomic, copy) NSString *rights_item_key;
@property (nonatomic, copy) NSString *rights_title;
@property (nonatomic, copy) NSString *rights_description;
@property (nonatomic, assign) NSInteger status;
@property (nonatomic, assign) NSInteger start_time;
@property (nonatomic, assign) NSInteger expires_at;
@property (nonatomic, assign) NSInteger free_trial;
@property (nonatomic, assign) NSInteger worth;
@property (nonatomic, assign) NSInteger remain_num;
@property (nonatomic, assign) NSInteger item_num;
@property (nonatomic, assign) NSInteger discount_amount;
@end


@implementation IMYRightsItemModel

@end
