//
//  IMYRightsSDK.h
//  IMYBaseKit
//
//  Created by ljh on 2024/1/25.
//

#import <Foundation/Foundation.h>

@class RACSignal, IMYRightsModel, IMYRightsItemModel;

typedef NS_ENUM(NSInteger, IMYRightsType) {
    /// 未开通
    IMYRightsTypeNone = 0,
    /// 付费会员
    IMYRightsTypeVIP = 1,
    /// 试用会员
    IMYRightsTypeProbationVIP = 2,
    /// 预埋 SVIP
    IMYRightsTypeSuperVIP = 3,
};

NS_ASSUME_NONNULL_BEGIN

/// 通知会员tab刷新数据
UIKIT_EXTERN NSNotificationName const IMYMRHomeRefreshDataNotification;

@interface IMYRightsSDK : NSObject

+ (instancetype)sharedInstance;

/// 权益相关信息修改通知
@property (nonatomic, readonly) RACSignal *loadedSignal;

/// 命中 ABTest
@property (nonatomic, readonly) BOOL inABTest;

/// 命中 ABTest 的情况下，返回对应value 比如: @"1407-7342"
@property (nonatomic, readonly, nullable) NSString *abExpValue;

#pragma mark - 我的权益

/// 简化版本：是否有对应权益Type，0：无，1：普通VIP，2：试用VIP，3：超级VIP
- (IMYRightsType)currentRightsType;

/// 订阅类型：0：无订阅 1：订阅-月卡 2：订阅-季卡 3：连续包月 4：连续包季 5：连续包年 6：过期 7：订阅-年卡
- (NSInteger)currentSubscribeType;

/// 是否有该权益项
- (BOOL)hasRightsItemForKey:(NSString *)itemKey;

/// 获取权益项详细信息，会判断有效期（有效期之外的会直接返回:nil）
- (IMYRightsItemModel *)getRightsItemModelForKey:(NSString *)itemKey;

/// 完整的权益信息
@property (atomic, readonly) IMYRightsModel *detailModel;

/// 刷新用户权益信息
- (void)refreshData;

/// 权益无变化则不发通知
- (void)refreshDataNotNotify;

/// 审核状态
+ (BOOL)isSubAuditReview;

#pragma mark - 购买权益

/// 购买对应权益，权益包id + 定价id
- (void)payWithPlanId:(NSString *)planId
              priceId:(NSString *)priceId
         appendParams:(NSDictionary *)appendParams
        progressBlock:(void(^)(NSDictionary *result, NSInteger state))progressBlock
       completedBlock:(void(^)(NSDictionary *result, NSError *error))completedBlock;

/// 恢复购买
- (void)restoreWithAppendParams:(NSDictionary *)appendParams
                  progressBlock:(void (^)(NSDictionary *result, NSInteger state))progressBlock
                 completedBlock:(void (^)(NSDictionary *result, NSError *error))completedBlock;

@end

#pragma mark - 权益详情

@interface IMYRightsModel : NSObject

/// 权益类型，0：无，1：普通VIP，2：试用VIP，3：超级VIP
@property (nonatomic, assign, readonly) IMYRightsType type;

/// 订阅类型：0：无订阅 1：订阅-月卡 2：订阅-季卡 3：连续包月 4：连续包季 5：连续包年 6：过期 7：订阅-年卡
@property (nonatomic, assign, readonly) NSInteger sub_type;

/// 最新会员类型：0非 1普通 2试用
@property (nonatomic, assign, readonly) NSInteger latest_type;

/// 订单id
@property (nonatomic, copy, readonly) NSString *transaction_id;
/// 名称
@property (nonatomic, copy, readonly) NSString *title;
/// 描述
@property (nonatomic, copy, readonly) NSString *desc;
/// 图
@property (nonatomic, copy, readonly) NSString *images;
/// icon
@property (nonatomic, copy, readonly) NSString *icon;

/// 开始生效时间
@property (nonatomic, assign, readonly) NSInteger start_time;
/// 过期时间
@property (nonatomic, assign, readonly) NSInteger expires_at;

/// 权益项
@property (nonatomic, copy, readonly) NSArray<IMYRightsItemModel *> *myRights;

@end



@interface IMYRightsItemModel : NSObject

/// 权益项id
@property (nonatomic, copy, readonly) NSString *rights_item_id;
/// 权益项key
@property (nonatomic, copy, readonly) NSString *rights_item_key;

/// 名称
@property (nonatomic, copy, readonly) NSString *rights_title;
/// 描述
@property (nonatomic, copy, readonly) NSString *rights_description;

/// 状态：1-已发放，2-待使用，3-已使用，4-已过期, 5-已回收
@property (nonatomic, assign, readonly) NSInteger status;
/// 开始生效时间
@property (nonatomic, assign, readonly) NSInteger start_time;
/// 过期时间
@property (nonatomic, assign, readonly) NSInteger expires_at;

/// 试用，0-否，1-是
@property (nonatomic, assign, readonly) NSInteger free_trial;
/// 面值
@property (nonatomic, assign, readonly) NSInteger worth;
/// 剩余数量
@property (nonatomic, assign, readonly) NSInteger remain_num;
/// 总数量
@property (nonatomic, assign, readonly) NSInteger item_num;
/// 预估优惠金额
@property (nonatomic, assign, readonly) NSInteger discount_amount;

@end




NS_ASSUME_NONNULL_END
