# MARK: converted automatically by spec.py. @hgy

Pod::Spec.new do |s|
	s.name = 'IMYBaseKit'
	s.version = '8.95.012'
	s.description = 'IMYBaseKit Description'
	s.license = 'MIT'
	s.summary = 'IMYBaseKit'
	s.homepage = 'https://github.com/meiyoudev/IMYBaseKit'
	s.authors = { 'xyz' => '<EMAIL>' }
	s.source = { :git => '*********************:iOS/IMYBaseKit.git', :branch => 'release-8.95.0' }
	s.requires_arc = true
	s.ios.deployment_target = '12.0'

	s.default_subspec  = [
    'IMYFoundation',
    'IMYVendor',
    'IMYPublic',
    'IMYViewKit',
    'IMYUserTracer',
    'IMYRights'
  ]
	
	s.source_files = 'Repos/IMYBaseKit.h'
	
	# 资源需要在根节点也配置一份（二进制缓存使用）
	s.resources = 'Repos/Resources/Resources/**/*.{json,png,jpg,apng,gif,js,xib,eot,svg,ttf,db,sqlite,mp3,pag}',
								'Repos/Resources/Bundles/*.{bundle,xcassets}'

	# IMYFoundation
	s.subspec "IMYFoundation" do |ss|
		ss.source_files = 'Repos/IMYFoundation/Source/**/*.{h,m,c,cpp,cc,mm,hpp,S}',
											'Repos/IMYFoundation/Subpods/MLNKV/iOS/*.{h,m,mm}',
											'Repos/IMYFoundation/Subpods/MLNKV/cpp/*.{h,hpp,cpp}'
	end

	# IMYVendor
	s.subspec "IMYVendor" do |ss|
		ss.source_files = 'Repos/IMYVendor/OldSource/**/*.{h,m,c}',
											'Repos/IMYVendor/Source/IMYShareSDK/**/*.{h,m,c}',
											'Repos/IMYVendor/Source/IMYTheme/**/*.{h,m}',
											'Repos/IMYVendor/Source/IMYNetworking/**/*.{h,m}',
											'Repos/IMYVendor/Source/IMYPageViewController/**/*.{h,m}',
											'Repos/IMYVendor/Source/FKReachability/**/*.{h,m}',
											'Repos/IMYVendor/Source/LDEventSource/**/*.{h,m,mm}'
	end

	# IMYPublic
	s.subspec "IMYPublic" do |ss|
		ss.source_files = 'Repos/IMYPublic/Source/**/*.{h,m,c}'
	end

	# IMYViewKit
	s.subspec "IMYViewKit" do |ss|
		ss.source_files = 'Repos/IMYViewKit/Source/**/*.{h,m}'
	end

	# IMYUserTracer
	s.subspec "IMYUserTracer" do |ss|
		ss.source_files = 'Repos/IMYUserTracer/Source/**/*.{h,m,c}'
	end

	# IMYRights
	s.subspec "IMYRights" do |ss|
		ss.source_files = 'Repos/IMYRights/Source/**/*.{h,m,c}'
	end


	s.frameworks =  'UIKit', 
									'Foundation', 
									'QuartzCore', 
									'CoreGraphics', 
									'WebKit', 
									'ImageIO', 
									'SystemConfiguration', 
									'CoreTelephony', 
									'AdSupport', 
									'MobileCoreServices', 
									'MediaPlayer', 
									'CoreMedia', 
									'MessageUI',
									'EventKitUI', 
									'EventKit', 
									'AudioToolbox', 
									'AVFoundation', 
									'StoreKit', 
									'CoreMotion', 
									'MapKit', 
									'CoreLocation', 
									'Security', 
									'Photos',
									'PhotosUI',
									'SafariServices'

	s.weak_frameworks = 'AppTrackingTransparency', 
											'AdServices'

	s.libraries = 'xml2.2', 
							  'z', 
							  'sqlite3', 
							  'stdc++', 
							  'c++',
							  'resolv'

	s.pod_target_xcconfig = {
	    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++11',
	    'CLANG_CXX_LIBRARY' => 'libc++'
	}

	s.dependency 'MBProgressHUD','1.1.0'
	s.dependency 'BlocksKit','~> 2.2.5'
	s.dependency 'ReactiveCocoa','~> 2.5'
	s.dependency 'CocoaSecurity','~> 1.2.4'
	s.dependency 'ZipArchive','1.4.0'
	s.dependency 'YYModel','~> 1.0.4'
	s.dependency 'CommonCrypto', '1.1'
	s.dependency 'YYCache','~> 1.0.4'
	s.dependency 'LKDBHelper'
	s.dependency 'FMDB'
	s.dependency 'pop','1.0.12'
	s.dependency 'Masonry','1.1.0'
	s.dependency 'ObjcAssociatedObjectHelpers','2.0.1'
	s.dependency 'AFNetworking','~> 3.2.1'
	s.dependency 'Reachability','3.2'
	s.dependency 'SDWebImage', '~> 3.8.2'
	s.dependency 'FLAnimatedImage','~> 1.0.16'
	s.dependency 'YYImage','~> 1.0.4'
	s.dependency 'UICKeyChainStore','2.1.2'
	s.dependency 'THProgressView','1.0'
	s.dependency 'GCDObjC','0.3.0'
	s.dependency 'MJRefresh','~> 3.1.16'
	s.dependency 'TPKeyboardAvoiding','1.3.2'
	s.dependency 'CHTCollectionViewWaterfallLayout','0.9.7'
	s.dependency 'HPGrowingTextView','~> 1.1'
	s.dependency 'StyledPageControl','1.0'
	s.dependency 'XMLDictionary','1.4.1'
	s.dependency 'SZTextView','1.3.0'
	s.dependency 'KKJSBridge/AjaxProtocolHook','1.3.9'
	s.dependency 'lottie-ios','2.5.3'

	s.dependency 'IMYDynamicFrameworks'
	s.dependency 'IOC-Protocols'
	
end
