//
//  IMYMR2ndMainView.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/8.
//

#import "IMYMR2ndMainView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYMR2ndCardView.h"

@interface IMYMR2ndMainView ()

@property (nonatomic, strong) UIView *topBar;
@property (nonatomic, strong) IMYBadgeView *badgeView;

@property (nonatomic, strong) UIScrollView *contentView;

@property (nonatomic, copy) NSDictionary *rawData;

@end

@implementation IMYMR2ndMainView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 顶部区域
    {
        self.topBar = [UIView new];
        self.topBar.frame = CGRectMake(0, 0, self.imy_width, 38);
        [self addSubview:self.topBar];
        
        @weakify(self);
        [self.topBar bk_whenTapped:^{
            @strongify(self);
            [self onTopBarClickAction];
        }];
        
        UILabel *titleLabel = [UILabel new];
        titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        titleLabel.textColor = UIColor.whiteColor;
        titleLabel.text = @"会员专区";
        [titleLabel imy_sizeToFit];
        titleLabel.imy_left = 24;
        titleLabel.imy_centerY = self.topBar.imy_height / 2;
        [self.topBar addSubview:titleLabel];
        
        UIImageView *vipIcon = [UIImageView new];
        vipIcon.imy_size = CGSizeMake(34, 16);
        vipIcon.image = [UIImage imageNamed:@"vip_simplified_goto_icon"];
        vipIcon.imy_centerY = titleLabel.imy_centerY;
        vipIcon.imy_left = titleLabel.imy_right + 4;
        [self.topBar addSubview:vipIcon];
        
        UIImageView *arrowIcon = [UIImageView new];
        arrowIcon.imy_size = CGSizeMake(14, 14);
        arrowIcon.image = [UIImage imageNamed:@"all_list_arrow_right_m"];
        arrowIcon.imy_right = self.topBar.imy_width - 24;
        arrowIcon.imy_centerY = titleLabel.imy_centerY;
        [self.topBar addSubview:arrowIcon];
        
        UILabel *rightLabel = [UILabel new];
        rightLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        rightLabel.textColor = IMY_COLOR_KEY(@"#999999");
        rightLabel.text = @"会员中心";
        [rightLabel imy_sizeToFit];
        rightLabel.imy_right = arrowIcon.imy_left - 4;
        rightLabel.imy_centerY = arrowIcon.imy_centerY;
        [self.topBar addSubview:rightLabel];
        
        self.badgeView = [IMYBadgeView new];
        self.badgeView.center = CGPointMake(rightLabel.imy_right + 1, rightLabel.imy_top);
        self.badgeView.edgeValue = 1;
        [self.topBar addSubview:self.badgeView];
    }
    
    self.contentView = [UIScrollView new];
    self.contentView.frame = CGRectMake(0, self.topBar.imy_bottom, self.imy_width, 110);
    self.contentView.showsHorizontalScrollIndicator = NO;
    [self addSubview:self.contentView];
}

- (void)onTopBarClickAction {
    NSString *jump_uri = self.rawData[@"jump_uri"];
    if (jump_uri.length > 0) {
        self.userInteractionEnabled = NO;
        [[IMYURIManager sharedInstance] runActionWithString:jump_uri];
        @weakify(self);
        imy_asyncMainBlock(0.5, ^{
            @strongify(self);
            self.userInteractionEnabled = YES;
            [[NSNotificationCenter defaultCenter] postNotificationName:@"secondFloor/quitSilence" object:nil];
        });
    }
    
    // 点击埋点
    [self biReportWithAction:2];
    
    // 点击后，才更新红点值
    if (!self.badgeView.hidden) {
        self.badgeView.text = nil;
        NSString * const newDotValue = self.rawData[@"red_dot_str"];
        [[IMYKV defaultKV] setString:newDotValue forKey:@"vip_2nd_red_dot_key"];
    }
}

- (void)setupWithData:(NSDictionary *)rawData {
    self.rawData = rawData;
    
    // 判断是否显示红点
    NSString * const newDotValue = self.rawData[@"red_dot_str"];
    NSString * const lastDotValue = [[IMYKV defaultKV] stringForKey:@"vip_2nd_red_dot_key"];
    if (newDotValue.length > 0 && ![lastDotValue isEqualToString:newDotValue]) {
        // 跟上次的，不一致要显示红点，点击后再刷新缓存值
        self.badgeView.text = kIMYBadgeViewDotText;
    } else {
        self.badgeView.text = nil;
    }
    
    // 清理卡片区
    [self.contentView imy_removeAllSubviews];
    
    // 数据源
    NSArray<NSDictionary *> * const cardList = self.rawData[@"list"];
    // 已注册的Adapters
    NSArray<Class> * const registers = IMYHIVE_REGISTERS(IOCMR2ndCardViewProcotol);
    
    CGFloat leftOffsetX = 32;
    NSInteger leftIndex = 1;
    // 遍历数据,生成对应卡片
    for (NSDictionary *cardData in cardList) {
        for (Class<IOCMR2ndCardViewProcotol> clazz in registers) {
            if ([clazz canHandleCardData:cardData]) {
                IMYMR2ndCardView *cardView = [(Class)clazz new];
                [cardView setupWithData:cardData];
                cardView.imy_left = leftOffsetX;
                cardView.imy_top = 8;
                cardView.cardIndex = leftIndex;
                [self.contentView addSubview:cardView];
                
                leftIndex += 1;
                leftOffsetX = cardView.imy_right + 8;
                
                break;
            }
        }
    }
    
    // 设置可滚动区
    self.contentView.contentSize = CGSizeMake(leftOffsetX + 24, 0);
    self.contentView.contentOffset = CGPointZero;
    
    // 曝光埋点，延迟1秒(避免缓存)
    @weakify(self);
    imy_asyncMainBlock(1, ^{
        @strongify(self);
        self.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%p", NSStringFromClass(self.class), self];
        self.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self biReportWithAction:1];
        };
    });
}

- (void)biReportWithAction:(NSInteger const)action {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"action"] = @(action);
    gaParams[@"position"] = @(197);
    gaParams[@"info_tag"] = @"会员中心";
    gaParams[@"public_type"] = @(self.badgeView.isHidden ? 0 : 1);
    gaParams[@"index"] = @0;
    
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:gaParams headers:nil completed:nil];
}

@end

#pragma mark - 外部获取参数，IOC实例

@interface IMYMR2ndAdapter : NSObject

@end

IMYHIVE_BIND_CLASS(IMYMR2ndAdapter, IOCMemberRights2ndFloorAdapter, YES);

@implementation IMYMR2ndAdapter

- (UIView<IOCMemberRights2ndFloorMainView> *)mainView {
    CGRect const frame = {CGPointZero, self.mainViewSize};
    return [[IMYMR2ndMainView alloc] initWithFrame:frame];
}

- (CGSize)mainViewSize {
    return CGSizeMake(SCREEN_WIDTH, 148);
}

- (NSDictionary *)mainViewRequestParams {
    NSDictionary * const allParams = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"secondFloor/get/user_health_info" params:nil];
    return allParams;
}

- (BOOL)validWithData:(NSDictionary * const)rawData {
    NSArray *list = rawData[@"list"];
    return list.count > 0;
}

@end
