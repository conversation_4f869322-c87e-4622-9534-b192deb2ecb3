//
//  IMYMR2ndCardViewYuerNurse.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/11.
//

#import "IMYMR2ndCardViewYuerNurse.h"

@implementation IMYMR2ndCardViewYuerNurse

IMYHIVE_REGIST_CLASS(IMYMR2ndCardViewYuerNurse, IOCMR2ndCardViewProcotol);

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    // 卡片分类：1经期健康度2黄金受孕期3好孕计划4产检单解读5喂奶6睡眠7发育测评
    NSInteger const type = [cardData[@"type"] integerValue];
    return type == 5;
}

- (void)onViewDidLoad {
    // 设置标题icon
    UIImage *icon = [UIImage imageNamed:@"vip_2nd_icon_yuer_nurse"];
    [self setupWithIcon:icon andTitle:@"喂奶分析"];
    
    // 直接全部重建
    [self.contentView imy_removeAllSubviews];
    
    UIImageView *scoreImageView = [UIImageView new];
    scoreImageView.frame = CGRectMake(12, 0, 76, 76);
    [self.contentView addSubview:scoreImageView];
    
    UILabel *scoreLabel = [UILabel new];
    [self.contentView addSubview:scoreLabel];
    
    UILabel *bottomLabel = [UILabel new];
    bottomLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    bottomLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    bottomLabel.text = @"今日喂奶";
    [bottomLabel imy_sizeToFit];
    bottomLabel.imy_height = 16;
    bottomLabel.imy_centerX = scoreImageView.imy_centerX;
    bottomLabel.imy_bottom = self.contentView.imy_height - 4;
    [self.contentView addSubview:bottomLabel];
    
    // 内容数据
    NSDictionary * const contentData = self.rawData[@"data"];
    
    NSInteger const total_task_num = [contentData[@"today"][@"reference_count"] integerValue];
    NSInteger const done_task_num = [contentData[@"today"][@"count"] integerValue];
    CGFloat const progress = done_task_num / (double)(MAX(1, total_task_num));
    
    // 显示喂奶次数
    NSString * const allText = [NSString stringWithFormat:@"%ld次", done_task_num];
    NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:allText];
    [attrs addAttributes:@{
        NSFontAttributeName : [UIFont systemFontOfSize:11 weight:UIFontWeightMedium]
    } range:NSMakeRange(allText.length - 1, 1)];
    
    scoreLabel.font = [UIFont systemFontOfSize:19 weight:UIFontWeightMedium];
    scoreLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
    scoreLabel.attributedText = attrs;
    [scoreLabel imy_sizeToFit];
    scoreLabel.center = scoreImageView.center;
    
    // 进度图片
    NSString *barImageKey = nil;
    if (progress > 0.81) {
        barImageKey = @"mr_2nd_wnfx_5";
    } else if (progress > 0.61) {
        barImageKey = @"mr_2nd_wnfx_4";
    } else if (progress > 0.41) {
        barImageKey = @"mr_2nd_wnfx_3";
    } else if (progress > 0.21) {
        barImageKey = @"mr_2nd_wnfx_2";
    } else if (progress > 0.01) {
        barImageKey = @"mr_2nd_wnfx_1";
    } else {
        barImageKey = @"mr_2nd_hyjh_0";
    }
    scoreImageView.image = [UIImage imageNamed:barImageKey];
    
    // 获取标签最多两项
    NSArray * const metric_labels = contentData[@"weekly"][@"metric_labels"];
    for (NSInteger index = 0; index < metric_labels.count && index < 2; index ++) {
        NSDictionary * const item = metric_labels[index];
        
        NSString * const text = item[@"name"];
        NSInteger const status = [item[@"status"] integerValue];
        NSString *label = nil;
        NSString *colorKey = nil;
        switch (status) {
            case 1:
                label = @"未记录";
                colorKey = @"#999999";
                break;
            case 2:
                label = @"符合参考值";
                colorKey = @"#29CC5F";
                break;
            case 3:
                label = @"建议关注";
                colorKey = @"#FF8833";
                break;
            case 4:
                label = @"建议调整";
                colorKey = @"#FF8833";
                break;
            case 5:
                label = @"明天更新";
                colorKey = @"#4DA6FF";
                break;
            default:
                label = @"未知";
                colorKey = @"#999999";
                break;
        }
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = UIColor.whiteColor;
        textLabel.text = text.length > 0 ? text : @"";
        textLabel.frame = CGRectMake(110, index == 0 ? 18 : 40, 0, 18);
        [textLabel imy_sizeToFitWidth];
        [self.contentView addSubview:textLabel];
        
        UILabel *tagLabel = [UILabel new];
        tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        tagLabel.text = label.length > 0 ? label : @"";
        tagLabel.backgroundColor = [UIColor colorWithWhite:0 alpha:0.15];
        tagLabel.textAlignment = NSTextAlignmentCenter;
        [tagLabel imy_drawAllCornerRadius:4];
        tagLabel.frame = CGRectMake(0, 0, 0, 16);
        [tagLabel imy_sizeToFitWidth];
        tagLabel.imy_width += 8;
        tagLabel.imy_left = textLabel.imy_right + 2;
        tagLabel.imy_centerY = textLabel.imy_centerY;
        if (!label.length) {
            tagLabel.hidden = YES;
        }
        tagLabel.textColor = IMY_COLOR_KEY(colorKey);
        [self.contentView addSubview:tagLabel];
    }
}

- (void)onViewDidClick {
    NSString *jump_uri = self.rawData[@"data"][@"uri"];
    if (jump_uri.length > 0) {
        [[IMYURIManager sharedInstance] runActionWithString:jump_uri];
    } else {
        [[IMYURIManager sharedInstance] runActionWithPath:@"secondFloorClick"
                                                   params:@{ @"id" : @4003 }
                                                     info:nil];
    }
}

- (NSDictionary *)onReportParams {
    NSDictionary * const contentData = self.rawData[@"data"];
    NSInteger const done_task_num = [contentData[@"today"][@"count"] integerValue];
    
    NSString *public_type = nil;
    if (done_task_num > 0) {
        public_type = @"已记录";
    } else {
        public_type = @"未记录";
    }
    
    NSString *baby_id = self.rawData[@"baby_id"];
    return @{
        @"public_type" : public_type ?: @"",
        @"baby_id" : baby_id ?: @"",
    };
}

@end
