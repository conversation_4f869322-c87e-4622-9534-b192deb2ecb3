//
//  IMYMR2ndCardView.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/8.
//

#import "IMYMR2ndCardView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYMR2ndCardView ()

@property (nonatomic, strong) UIImageView *bgView;
@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UIView *contentView;

@property (nonatomic, strong) UIImageView *topIconView;
@property (nonatomic, strong) UILabel *topTitleLabel;

@property (nonatomic, copy) NSDictionary *rawData;

@end

@implementation IMYMR2ndCardView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupViewInit];
    }
    return self;
}

- (void)setupViewInit {
    self.imy_size = CGSizeMake(260, 102);
    
    self.bgView = [UIImageView new];
    self.bgView.frame = self.bounds;
    self.bgView.image = [UIImage imageNamed:@"vip_2nd_card_bg"];
    [self addSubview:self.bgView];
    
    // 顶部区
    {
        self.topView = [UIView new];
        self.topView.frame = CGRectMake(0, 0, self.imy_width, 26);
        [self addSubview:self.topView];
        
        self.topIconView = [UIImageView new];
        self.topIconView.frame = CGRectMake(8, 8, 16, 16);
        [self.topView addSubview:self.topIconView];
        
        CGFloat const leftOffset = self.topIconView.imy_right + 4;
        self.topTitleLabel = [UILabel new];
        self.topTitleLabel.frame = CGRectMake(leftOffset, 8, self.topView.imy_width - leftOffset - 8, 18);
        self.topTitleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        self.topTitleLabel.textColor = [UIColor whiteColor];
        self.topTitleLabel.numberOfLines = 1;
        [self.topView addSubview:self.topTitleLabel];
    }
    
    // 内容区
    {
        self.contentView = [UIView new];
        self.contentView.clipsToBounds = YES;
        self.contentView.frame = CGRectMake(0, self.topView.imy_bottom, self.imy_width, 76);
        [self addSubview:self.contentView];
    }
    
    // 增加卡片点击事件
    @weakify(self);
    [self bk_whenTapped:^{
        @strongify(self);
        self.userInteractionEnabled = NO;
        [self biReportWithAction:2];
        [self onViewDidClick];
        imy_asyncMainBlock(0.5, ^{
            @strongify(self);
            self.userInteractionEnabled = YES;
            [[NSNotificationCenter defaultCenter] postNotificationName:@"secondFloor/quitSilence" object:nil];
        });
    }];
}

- (void)setupWithIcon:(UIImage *)icon andTitle:(NSString *)title {
    self.topIconView.image = icon;
    self.topTitleLabel.text = title;
}

- (void)setupWithData:(NSDictionary *)rawData {
    self.rawData = rawData;
    [self onViewDidLoad];
    
    // 曝光埋点，延迟1秒(避免缓存)
    @weakify(self);
    imy_asyncMainBlock(1, ^{
        @strongify(self);
        self.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%p", NSStringFromClass(self.class), self];
        self.imyut_eventInfo.showRadius = 0.5;
        self.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self biReportWithAction:1];
        };
    });
}

- (void)biReportWithAction:(NSInteger const)action {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"action"] = @(action);
    gaParams[@"position"] = @(197);
    gaParams[@"info_tag"] = self.topTitleLabel.text;
    gaParams[@"index"] = @(self.cardIndex);
    
    NSDictionary *info = [self onReportParams];
    if (info.count > 0) {
        [gaParams addEntriesFromDictionary:info];
    }
    
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:gaParams headers:nil completed:nil];
}

#pragma mark - 子类重载

- (void)onViewDidLoad {
    
}

- (void)onViewDidClick {
    
}

- (NSDictionary *)onReportParams {
    return nil;
}

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    return NO;
}

@end
