//
//  IMYMR2ndCardViewHuaiyunGCExplain.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/11.
//

#import "IMYMR2ndCardViewHuaiyunGCExplain.h"

@implementation IMYMR2ndCardViewHuaiyunGCExplain

IMYHIVE_REGIST_CLASS(IMYMR2ndCardViewHuaiyunGCExplain, IOCMR2ndCardViewProcotol);

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    // 卡片分类：1经期健康度2黄金受孕期3好孕计划4产检单解读5喂奶6睡眠7发育测评
    NSInteger const type = [cardData[@"type"] integerValue];
    return type == 4;
}

- (void)onViewDidLoad {
    // 设置标题icon
    UIImage *icon = [UIImage imageNamed:@"vip_2nd_icon_huaiyun_gcexplain"];
    [self setupWithIcon:icon andTitle:@"产检单智能解读"];
    
    // 直接全部重建
    [self.contentView imy_removeAllSubviews];
    
    UIImageView *scoreImageView = [UIImageView new];
    scoreImageView.frame = CGRectMake(12, 0, 76, 76);
    [self.contentView addSubview:scoreImageView];
    
    UILabel *tipLabel = [UILabel new];
    tipLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    tipLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    tipLabel.frame = CGRectMake(110, 19, 138, 16);
    [self.contentView addSubview:tipLabel];
    
    // 内容数据
    NSDictionary * const contentData = self.rawData[@"data"];
    
    // 报告数量
    NSInteger const report_num = [contentData[@"report_num"] integerValue];
    
    // 加载报告图片
    NSString * const img_path = contentData[@"img_path"];
    if (img_path.length > 0 && report_num > 0) {
        // 云端图片， 需要设置圆角
        scoreImageView.frame = CGRectMake(18, 6, 64, 64);
        [scoreImageView imy_drawAllCornerRadius:8];
        [scoreImageView imy_setImageURL:img_path];
    } else {
        scoreImageView.image = [UIImage imageNamed:@"vip_2nd_empty_huaiyun_gcexplain"];
    }
    
    // 当前孕周
    NSInteger const preg_week = [contentData[@"preg_week"] integerValue];
    tipLabel.text = [NSString stringWithFormat:@"孕%ld周产检", preg_week];
    
    // 有报告单
    if (report_num > 0) {
        NSInteger const report_status = [contentData[@"report_status"] integerValue];
        
        NSString * const numText = [NSString stringWithFormat:@"%ld张", report_num];
        NSString * const allText = [NSString stringWithFormat:@"%@产检单%@", numText, (report_status == 1 ? @"待解读" : @"已解读")];
        NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:allText];
        if (report_status == 1) {
            [attrs addAttributes:@{
                NSForegroundColorAttributeName : IMY_COLOR_KEY(@"#FF4D88"),
            } range:[allText rangeOfString:numText]];
        }
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = [UIColor colorWithWhite:1 alpha:1];
        textLabel.attributedText = attrs;
        textLabel.frame = CGRectMake(110, tipLabel.imy_bottom + 4, 138, 18);
        [self.contentView addSubview:textLabel];
        
    } else {
        // 无报告
        tipLabel.imy_top = 5;
        
        // 提升上传产检单
        NSString *report_text = contentData[@"importants"];
        if (!report_text.length) {
            report_text = @"产检单待解读";
        }
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = [UIColor colorWithWhite:1 alpha:1];
        textLabel.text = report_text;
        textLabel.frame = CGRectMake(110, tipLabel.imy_bottom + 4, 138, 18);
        [self.contentView addSubview:textLabel];
        
        NSString *button_name = contentData[@"button_name"];
        if (!button_name.length) {
            button_name = @"去上传产检单";
        }
        UILabel *gotoLabel = [UILabel new];
        gotoLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        gotoLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        gotoLabel.textAlignment = NSTextAlignmentCenter;
        gotoLabel.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        gotoLabel.layer.borderWidth = 1 / SCREEN_SCALE;
        gotoLabel.layer.cornerRadius = 12;
        gotoLabel.frame = CGRectMake(110, textLabel.imy_bottom + 4, 57, 24);
        gotoLabel.text = button_name;
        [gotoLabel imy_sizeToFitWidth];
        gotoLabel.imy_width += 24;
        [self.contentView addSubview:gotoLabel];
    }
}

- (void)onViewDidClick {
    NSString *jump_uri = self.rawData[@"data"][@"jump_uri"];
    if (jump_uri.length > 0) {
        [[IMYURIManager sharedInstance] runActionWithString:jump_uri];
    } else {
        [[IMYURIManager sharedInstance] runActionWithPath:@"secondFloorClick"
                                                   params:@{ @"id" : @3008 }
                                                     info:nil];
    }
}

- (NSDictionary *)onReportParams {
    NSDictionary * const contentData = self.rawData[@"data"];
    NSInteger const report_num = [contentData[@"report_num"] integerValue];
    
    NSString *public_type = nil;
    if (report_num > 0) {
        public_type = @"已上传";
    } else {
        public_type = @"未上传";
    }
    return @{
        @"public_type" : public_type ?: @"",
    };
}

@end
