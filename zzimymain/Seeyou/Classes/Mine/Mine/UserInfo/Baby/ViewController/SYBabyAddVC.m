//
//  SYBabyAddVC.m
//  Seeyou
//
//  Created by zlj on 2019/12/20.
//  Copyright © 2019 linggan. All rights reserved.
//

#import "SYBabyAddVC.h"
#import "SYBabyListVC.h"
#import <IMYBaseKit.h>
#import <IMYViewKit.h>
#import "SYGlobalMacros.h"
#import <IMYYQHome/IMYYQHomeABTestManager.h>
#import <IMYRecord/IMYRecordABTestManager.h>
#import "IMYUserModeBabyAlert.h"
#import "SYBabyReserveSheetView.h"
#import <IMYMSG/IMYMsgNotifyManager.h>
#import <IMYMe/IMYMe.h>
#import "SYChildPrivacyRulesView.h"
#import <IMYCommonKit/IMYCKABTestManager.h>
#import "BBJBabyCacheManager.h"
#import "NSDate+IMYDateFormat.h"
#import "IMYRecordPregnancyBabyManager.h"

@interface SYBabyAddVC () <UITextFieldDelegate>
@property (nonatomic, strong) SYChildPrivacyRulesView *checkPrivateView;
@property (nonatomic, strong) UIView *footerView;
@property (nonatomic, strong) NSMutableArray *deleteBabyList;
@property (nonatomic, assign, getter=isChangeModeToLama) BOOL changeModeToLama;
@property (nonatomic, strong) UIButton *rightclickButton_iOS12;
@end

@implementation SYBabyAddVC

- (instancetype)init {
    self = [super init];
    if (self) {
        self.location_id = 0;
    }
    return self;
}
- (void)viewDidLoad {
    self.isAddBaby = YES;
    [super viewDidLoad];
    
    [self setupData];
    [self setupSubviews];
    [self updateView];
    
    
    [self.view addSubview:self.checkPrivateView];
    [self.checkPrivateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.tableView.mas_bottom).offset(-46-SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
        make.centerX.equalTo(self.view);
    }];
}

- (void)setupData {
    self.title = IMYString(@"添加宝宝");
    self.saveImmediately = NO;
    self.saveBabyBGImmediately = NO;
    self.babyModel = [[IMYRecordBabyModel alloc] init];
    self.babyModel.nickname = @"";    
    @weakify(self);
    self.nicknameCellModel.cellType = SYBabyInfoNikname;
    self.nicknameCellModel.configureHandler = ^(SYTextTableViewCell *cell) {
        @strongify(self);
        cell.showFlagView = NO;
        cell.titleLabel.text = IMYString(@"宝宝小名");
        cell.textField.text = self.babyModel.nickname;
        cell.textField.userInteractionEnabled = YES;
        cell.textField.placeholder = IMYString(@"请输入宝宝小名");
        if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou) {
            cell.titleLabel.text = IMYString(@"宝宝小名");
            cell.textField.placeholder = IMYString(@"请输入宝宝小名");
        }
        [cell.textField imy_setPlaceholderColorForKey:kCK_Black_D];
        cell.textField.delegate = self;
        [[[cell.textField rac_textSignal] takeUntil:self.rac_willDeallocSignal]subscribeNext:^(NSString *text) {
            @strongify(self);
            if (text.imy_charCount > 20) {
                [UIWindow imy_showTextHUD:IMYString(@"输入已达上限")];
                text = [text imy_substringToCharCount:20];
                cell.textField.text = text;
            }
            self.babyModel.nickname = text;
            [self updateView];
        }];
    };
    self.nicknameCellModel.selectHandler = nil;
}

- (void)setupSubviews {
    [self.imy_topLeftButton imy_setImage:nil];
    [self.imy_topLeftButton imy_setTitle:IMYString(@"取消")];
    [self.imy_topLeftButton imy_setTitleColor:kCK_Black_A state:UIControlStateNormal];
    [self.imy_topLeftButton imy_setTitleColor:kCK_Black_A state:UIControlStateHighlighted];
    self.imy_topLeftButton.titleLabel.font = [UIFont systemFontOfSize:15];
    [self.imy_topLeftButton addTarget:self action:@selector(dismissAction) forControlEvents:UIControlEventTouchUpInside];
    
    UIView *righNavView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 60, 44)];
    self.rightNavButton = [[IMYCKLoadingButton alloc] initWithFrame:CGRectMake(0, (righNavView.imy_height - 26)/2, 62, 32)];
    [self.rightNavButton setTitle:IMYString(@"完成") buttonType:IMYCKLoadingButtonRedType];
    if(!IOS13 ) {//ios 12以下需要适配
        self.rightNavButton.imy_left = -62;
        self.rightNavButton.imy_top = - 16;
    }
    self.rightNavButton.layer.cornerRadius = 16;
    self.rightNavButton.layer.masksToBounds = YES;
    self.rightNavButton.titleLabel.font = [UIFont systemFontOfSize:14];
    [self.rightNavButton imy_setTitleColor:kCK_Black_B state:UIControlStateDisabled];
    [self.rightNavButton imy_setTitleColor:kCK_White_A state:UIControlStateNormal];
    
    [self.rightNavButton imy_addThemeChangedBlock:^(IMYCKLoadingButton *weakObject) {
        [weakObject imy_setBackgroundImage:[UIImage imageWithColor:IMY_COLOR_KEY(kCK_Red_B)] state:UIControlStateNormal stretch:YES];
        [weakObject imy_setBackgroundImage:[UIImage imageWithColor:IMY_COLOR_KEY(kCK_Black_E)] state:UIControlStateDisabled stretch:YES];
    }];
    
    [self.rightNavButton addTarget:self action:@selector(doneAction) forControlEvents:UIControlEventTouchUpInside];
    [righNavView addSubview:self.rightNavButton];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:righNavView];
    
    if(!IOS13) {//ios 12以下需要适配
        self.rightNavButton.imy_left = -62;
        self.rightNavButton.imy_top = - 16;
        UIButton *clickBtn = [[UIButton alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - self.rightNavButton.imy_width - 16, 0, 62, 44)];
        [clickBtn addTarget:self action:@selector(doneAction) forControlEvents:UIControlEventTouchUpInside];
        [self.navigationController.navigationBar addSubview:clickBtn];
        clickBtn.enabled = NO;
        self.rightclickButton_iOS12 = clickBtn;
    }

}

#pragma mark - Override
- (void)modelFinishSelect:(SYTextTableViewCellModel *)model {
    [super modelFinishSelect:model];
    if (self.babyModel.gender == IMYRecordBabyGenderNone) {
        self.genderCellModel.selectHandler();
    } else if (imy_isBlankString(self.babyModel.birthday)) {
        self.birthdayCellModel.selectHandler();
    }
    [self updateView];
}

#pragma mark - Private
- (void)updateView {
    
    self.rightNavButton.enabled = (imy_isNotBlankString(self.babyModel.nickname) &&
                                   imy_isNotBlankString(self.babyModel.birthday) &&
                                   self.babyModel.gender != IMYRecordBabyGenderNone);
    self.rightclickButton_iOS12.enabled = self.rightNavButton.enabled;
    if (self.avatar) {
        [self.avatarImageView imy_setImage:self.avatar];
        self.iconCameraFlag.hidden = NO;
    } else {
        [self.avatarImageView imy_setImage:[UIImage imy_imageForKey:@"ertai_pic_photo"]];
        self.iconCameraFlag.hidden = YES;
    }
}

#pragma mark - Action
- (void)dismissAction {
    if (imy_isNotBlankString(self.babyModel.nickname) ||
        imy_isNotBlankString(self.babyModel.birthday) ||
        self.babyModel.gender != IMYRecordBabyGenderNone ||
        self.avatar) {
        @weakify(self);
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"继续编辑")
                            destructiveTitle:IMYString(@"放弃操作")
                                 otherTitles:nil
                                     summary:IMYString(@"已有编辑内容，放弃操作将不保存记录")
                                  showInView:nil action:^(NSInteger index) {
            @strongify(self);
            if (index) {
                [self dismissViewControllerAnimated:YES completion:nil];
            }
        }];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)doneAction {
    
    if (!self.checkPrivateView.checkSeleted) {
        [self imy_showTextHUD:IMYString(@"请勾选同意美柚使用您的宝宝信息")];
        [self.checkPrivateView shake];
        return ;
    }
    
    if ([self babyCountOverLimit]) {
        return;
    }
    
    self.changeModeToLama = [self isNeedChangeUserMode];
    IMYRecordBabyModel *baby = [IMYRecordBabyManager.sharedInstance myBabySearchWithBirthday:self.babyModel.birthday];

    if (baby && [IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModePregnancy) {
        @weakify(self);
        [IMYUserModeBabyAlert showWithBaby:baby compeltion:^(NSInteger index) {
            @strongify(self);
            if (index == 2) {
                return;
            }
            if (index == 0) {//同一个宝宝
                [self dismissViewControllerAnimated:YES completion:^{
                    if (self.addBabyFinish) {
                        self.addBabyFinish(self.babyModel.birthday.imy_getDateZero);
                    }
                }];
                return;
            }
            [self addBaby];
        }];
    } else if (baby && [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        //怀孕身份且相同生日宝宝
        //切不切身份，以落在孕期内为主
        [self showReserveSheetView:YES];
    } else if ([self pregnancyAnd140]) {
        //弹窗提示
    } else if([SYBabyReserveSheetView hasSimilarBabyBetween0And4:self.babyModel.birthday]){
        //已有宝宝生日间隔在1-4天内
        [SYBabyReserveSheetView showSimilarBabyAlertWithBirthday:self.babyModel.birthday completion:^(BOOL addNew) {
            if (!addNew) {
                return ;
            }
            [self addBaby];
        }];
    } else if ([SYBabyReserveSheetView containBabyBetween4And154:self.babyModel.birthday]) {
        //已有宝宝生日间隔在4-154天内
        [self showReserveSheetView:NO];
    } else {
        [self addBaby];
    }
}

#pragma mark
- (void)showReserveSheetView:(BOOL)isSameBaby {
    //退出编辑状态
    [self.view endEditing:YES];
    //弹窗
    BOOL changeModel = self.changeModeToLama && !self.manualChangeMode;
    [SYBabyReserveSheetView showSheetViewWithBabyModel:self.babyModel avatar:self.avatar sameBaby:isSameBaby changeMode:changeModel completeBlock:^(BOOL needAdd, NSArray * _Nonnull babyList) {
        self.deleteBabyList = [NSMutableArray arrayWithArray:babyList];
        if (needAdd) {
            NSInteger gestation_id = 0;
            IMYPregnancyFlowModel *model = [IMYPregnancyFlowModel getCurrentPregnancing];
            if (model.gestation_id > 0) {
                gestation_id = model.gestation_id;
            } else {
                gestation_id = [IMYRecordPregnancyBabyManager sharedInstance].gestation_id;
            }
            self.babyModel.gestation_id = gestation_id;
            [self addBaby];
        } else {
            //出生日不同的宝宝且怀孕身份，不主动切换身份
            if (!isSameBaby && changeModel) {
                self.manualChangeMode = YES;
            }
            [self sync_deleteBabyListForServer];

        }
    }];
}

#pragma mark 宝宝个数超过上限
- (BOOL)babyCountOverLimit {
    if ([[IMYRecordBabyManager sharedInstance] babyList].count >= 5) {
        [IMYActionMessageBox showBoxWithTitle:IMYString(@"宝宝已到达上限")
                                      message:IMYString(@"添加新的宝宝，请至少删除一个宝宝")
                                        style:IMYMessageBoxStyleFlat
                            isShowCloseButton:NO
                                textAlignment:NSTextAlignmentCenter
                            cancelButtonTitle:IMYString(@"取消")
                             otherButtonTitle:IMYString(@"去删除")
                                       action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            if (sender == messageBox.rightButton) {
                IMYURI *uri = [IMYURI uriWithPath:@"user/baby/list" params:@{@"hideAdd":@(YES)} info:nil];
                [IMYURIManager.shareURIManager runActionWithURI:uri];
            }
            if (sender) {
                [messageBox dismiss];
            }
        }];
        return YES;
    }
    return NO;
}

#pragma mark 是否需要切换身份
- (BOOL)isNeedChangeUserMode {
    //如果有问题，打开开关，
    if (![[IMYConfigsCenter sharedInstance] boolForKeyPath:@"womens_health2.tech_optimize_set_config.disable_ios_opt_fetal_895"]) {
        return NO;
    }

    //怀孕身份且 宝宝出生日 - 怀孕开始日天数 >= 140天
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        //怀孕开始日
        NSDate *dueDate = [[IMYPublicAppHelper shareAppHelper].pregnancy imy_getOnlyDate];
        NSDate *pregnancyDate = [dueDate dateBySubtractingDays:280];
        NSDate *babyBirthday = [self.babyModel.birthday imy_getOnlyDate];
        NSInteger babyDay = [[NSDate imy_getCNCalendar] components:NSCalendarUnitDay
                                                          fromDate:pregnancyDate
                                                            toDate:babyBirthday
                                                           options:0].day;
        if (babyDay >= 140) {
            return YES;
        }
        return NO;
    }
    return NO;
}

#pragma mark 出生日期异常弹窗
- (BOOL)pregnancyAnd140 {
    //怀孕身份且宝宝出生日期在当前孕期内 -14 < 宝宝出生日 - 怀孕开始日天数 < 140天
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        //怀孕开始日
        NSDate *dueDate = [[IMYPublicAppHelper shareAppHelper].pregnancy imy_getOnlyDate];
        NSDate *pregnancyDate = [dueDate dateBySubtractingDays:280];
        NSDate *babyBirthday = [self.babyModel.birthday imy_getOnlyDate];
        NSInteger babyDay = [[NSDate imy_getCNCalendar] components:NSCalendarUnitDay
                                                          fromDate:pregnancyDate
                                                            toDate:babyBirthday
                                                           options:0].day;
        BOOL contain = NO;
        if (-14 < babyDay && babyDay < 140) {
            NSString *message = IMYString(@"宝宝出生日距怀孕开始日不足140天，请重新设置出生日期或预产期");
            if (babyDay < 0) {
                message = IMYString(@"宝宝出生日距怀孕开始日不足14天，请重新设置出生日期或预产期");
            }
            //出生日期异常弹窗
            [IMYActionMessageBox showBoxWithTitle:IMYString(@"出生日期异常")
                                          message:message
                                            style:IMYMessageBoxStyleFlat
                                isShowCloseButton:NO
                                    textAlignment:NSTextAlignmentCenter
                                cancelButtonTitle:nil
                                 otherButtonTitle:IMYString(@"我知道了")
                                           action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                [messageBox dismiss];
            }];
            return YES;
        }
        return NO;
    }
    return NO;
}

#pragma mark 添加宝宝，名称校验
- (void)addBaby {
    [self sync_addBaby];
}
/// 同步，857新方案
- (void)sync_addBaby {
    @weakify(self);
    NSString *nickname = self.babyModel.nickname.imy_trimString;
    [UIWindow imy_showTextHUDWithoutUI];
    [self.rightNavButton showLoading:YES];
    [[[IMYServerRequest getPath:@"v3/checkBabyName" host:gravidity_seeyouyima_com params:@{@"nickname" : nickname} headers:nil] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        [self uploadAvatar:^(BOOL success, NSString *url, NSString *errorMessage) {
            @strongify(self);
            if (success) {
                self.babyModel.nickname = nickname;
                self.babyModel.avatar = url;
                NSString *scence = [NSString stringWithFormat:@"delConut=%ld",self.deleteBabyList.count];
                NSMutableArray *serverArray = [[NSMutableArray alloc] init];
                [serverArray addObject:self.babyModel];
                if (self.gestation_id > 0) {
                    self.babyModel.gestation_id = self.gestation_id;
                }
                BOOL is_checked = NO;
                for (IMYRecordBabyModel *babyModel in self.deleteBabyList) {
                    babyModel.is_deleted = YES;
                    if (babyModel.is_checked) {
                        babyModel.is_checked = NO;
                        is_checked = YES;
                        scence = [NSString stringWithFormat:@"delConut=%ldck=YES",self.deleteBabyList.count];
                    }
                    [serverArray addObject:babyModel];
                }
                //需要反选选中宝宝
                if (self.needUnCheckedBaby) {
                    //新增宝宝，选中最新宝宝
                    self.babyModel.is_checked = NO;
                    if (!is_checked) {
                        IMYRecordBabyModel *checkBabyModel = [IMYRecordBabyManager sharedInstance].currentBaby;
                        checkBabyModel.is_checked = NO;
                        [serverArray addObject:checkBabyModel];
                    }
                } else {
                    //新增宝宝，选中最新宝宝
                    self.babyModel.is_checked = YES;
                }

                [IMYRecordBabyManager syncBabyInfoToServerWithBabyModel:[serverArray copy]
                                                               position:10 + self.location_id
                                                                  scene:scence
                                                          andCompletion:^(BOOL success, NSString *errorMessage) {
                    @strongify(self);
                    [UIWindow imy_hideHUD];
                    [self.rightNavButton showLoading:NO];
                    if (success) {
                        [self sync_deleteBabyListAndDismiss];
                        [[IMYRecordBabyManager sharedInstance] selectBaby:self.babyModel.baby_id];
                        // 824 孕育|首页产品结构调整  新增baby后。默认选中
                        if ([IMYCKABTestManager isYunyuHomeContainStyle]) {
                            if (self.addBabyFinishForPregnancy) {
                                self.addBabyFinishForPregnancy(self.babyModel);
                            }
                        }
                    } else {
                        if (![IMYNetState networkEnable]) {
                            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                        } else {
                            [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                        }
                    }
                   
                }];
          
            } else {
                [self.rightNavButton showLoading:NO];
                [UIWindow imy_hideHUD];
                [UIWindow imy_showTextHUD:errorMessage];
            }
        }];
    } error:^(NSError *error) {
        [self.rightNavButton showLoading:NO];
        NSString *message;
        if (![IMYNetState networkEnable]) {
            message = MT_Request_NoNetToast;
        } else if (error.code > 0) {
            message = IMYString(@"宝宝昵称不可用，请换一个");
        } else {
            message = IMYString(@"上传失败，请再试一次");
        }
        [UIWindow imy_showTextHUD:message];
    }];
}


/// 857 同步机制
- (void)sync_deleteBabyListForServer {
    BOOL isChecked = NO;
    NSMutableArray *serverArray = [[NSMutableArray alloc] init];
    for (IMYRecordBabyModel *babyModel in self.deleteBabyList) {
        babyModel.is_deleted = YES;
        if (babyModel.is_checked) {
            isChecked = YES;
        }
        [serverArray addObject:babyModel];
    }
    //删除了选中的数据，就需要找到最小宝宝设置选中
    if (isChecked) {
        NSMutableArray *babyList = [[IMYRecordBabyManager sharedInstance] babyList];
        NSPredicate * filterPredicate = [NSPredicate predicateWithFormat:@"NOT (SELF IN %@)",serverArray];
        NSArray * filterArray = [babyList filteredArrayUsingPredicate:filterPredicate];
        IMYRecordBabyModel *lastModel = filterArray.firstObject;
        for (IMYRecordBabyModel *babyModel in filterArray) {
            NSDate *lastModel_babyBirthDay = [lastModel.birthday imy_getOnlyDate];
            NSDate *current_babyBirthDay = [babyModel.birthday imy_getOnlyDate];
            if ([current_babyBirthDay compare:lastModel_babyBirthDay] == NSOrderedDescending &&
                babyModel.baby_id != lastModel.baby_id) {
                lastModel = babyModel;
            }
        }
        //取最小宝宝，作为选中宝宝
        if (lastModel) {
            [serverArray addObject:lastModel];
        }
    }
    NSString *scene = [NSString stringWithFormat:@"delCount=%ld ck=%@",self.deleteBabyList.count, isChecked?@"YES" : @"NO"];
    [UIWindow imy_showTextHUDWithoutUI];
    [self.rightNavButton showLoading:YES];
    @weakify(self);
    if (serverArray.count == 0 && [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        [IMYRecordBabyManager syncBabyInfoToServerWithNONewBaby:^(BOOL success, NSString *errorMessage) {
            @strongify(self);
            [self.rightNavButton showLoading:NO];
            [UIWindow imy_hideHUD];
            if (success) {
                [self sync_deleteBabyListAndDismiss];
                [IMYDayRecordModel deleteCurrentPregnancy];///不需要新增宝宝,需要删除当前孕期
            } else {
                if (![IMYNetState networkEnable]) {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                } else {
                    [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                }
            }
        }];
    } else {
        [IMYRecordBabyManager syncBabyInfoToServerWithBabyModel:[serverArray copy]
                                                       position:17
                                                          scene:scene
                                                  andCompletion:^(BOOL success, NSString *errorMessage) {
            @strongify(self);
            [self.rightNavButton showLoading:NO];
            [UIWindow imy_hideHUD];
            if (success) {
                [self sync_deleteBabyListAndDismiss];
                [IMYDayRecordModel deleteCurrentPregnancy];///不需要新增宝宝,需要删除当前孕期
            } else {
                if (![IMYNetState networkEnable]) {
                    [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
                } else {
                    [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
                }
            }
        }];
    }

}

/// 857 同步机制
- (void)sync_deleteBabyListAndDismiss {
    if (self.changeModeToLama && !self.manualChangeMode) {
        //切换身份
        [self babyBornAndChangeUserModeToLama];
        //等待身份切换成功，否则会闪现怀孕首页
        imy_asyncMainBlock(0.6, ^{
            //跳转到首页
            [self dismissViewControllerAnimated:NO completion:^{
                [self positionToFirstHomeTab:YES];
                imy_asyncMainBlock(0.3, ^{
                    NSString *modeStr = [IMYPublicAppHelper shareAppHelper].userModeName;
                    [MBProgressHUD imy_showTextHUD:[NSString stringWithFormat:IMYString(@"已切换至%@模式"), modeStr]];
                });
            }];
        });
    } else {
        if (self.needRouteToListVC) {
            UIViewController *vc = [[SYBabyListVC alloc] init];;
            [self.presentingViewController imy_push:vc animated:NO];
        }
        [self dismissViewControllerAnimated:YES completion:^{
            if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou) {
                IMYRecordBabyModel *babyModel = [[IMYRecordBabyManager sharedInstance] getBabyWithId:self.babyModel.baby_id];
                [BBJBabyCacheManager shareInstance].currentBabyId = babyModel.bbj_baby_id;
            }
            if (self.addBabyFinish) {
                self.addBabyFinish(self.babyModel.birthday.imy_getDateZero);
            }
        }];
    }
    
    if (self.location_id > 0) {
        NSDictionary *params = @{ @"event": @"add_baby",
                                  @"location_id": @(self.location_id),
                                  @"action": @(2)};
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    } else {
        [IMYGAEventHelper postWithPath:@"event" params:@{ @"event": @"add_baby", @"action": @(3)} headers:nil completed:nil];
    }
    
    if (self.deleteBabyList.count > 0) {
        [self sync_deleteBabyListAction];
    } else {
        self.hasSyncBabyInfo = YES;
    }
}
/// 857 同步机制
- (void)sync_deleteBabyListAction {
    // 进入宝宝记融合实验，在删除最小(选中)宝宝前，把选中宝宝设置成年龄第二小的宝宝
    BOOL isChecked = NO;
    IMYRecordBabyModel *preSelectModel = [[IMYRecordBabyManager sharedInstance] currentBaby];
    for (IMYRecordBabyModel *babyModel in self.deleteBabyList) {
        if (babyModel.is_checked) {
            isChecked = YES;
        }
        ///通知宝宝记模块,悬浮窗需要监听数据
        [NSNotificationCenter.defaultCenter postNotificationName:@"BBJNotification_removeFloating" object:@(babyModel.baby_id)];
    }
    
    if (isChecked) {
        self.hasSyncBabyInfo = NO;
        IMYRecordBabyModel *lastModel = [[IMYRecordBabyManager sharedInstance] lastBirthdayBaby];
        //选中最小宝宝
        if (!lastModel.is_checked) {
            [[IMYRecordBabyManager sharedInstance] selectBaby:lastModel.baby_id];
        }
         if ([preSelectModel.birthday isEqualToString:[lastModel birthday]] &&
            preSelectModel.gender == lastModel.gender &&
            preSelectModel.baby_id != lastModel.baby_id) {
            [[NSNotificationCenter defaultCenter] postNotificationName:@"kSYUserBabyInfoChangedNotification" object:[NSDictionary dictionary]];
        }
    }
    self.hasSyncBabyInfo = NO;
    self.deleteBabyList = nil;
}

#pragma mark 宝宝出生了，身份切换为辣妈
- (void)babyBornAndChangeUserModeToLama {
    //结束妊娠
    NSDate *birthdayDate = [self.babyModel.birthday imy_getOnlyDate];
    [IMYDayRecordModel birthCurrentPregnancy:birthdayDate];
    NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:birthdayDate];
    if (manualOvlution) {
        [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
    }
    //切换身份为辣妈
    SYUserModeViewModel *viewModel = [SYUserModeViewModel new];
    [viewModel changeToMode:IMYVKUserModeLama];
}

#pragma mark 跳转到首页 当前页面也跳转到rootVC
- (void)positionToFirstHomeTab:(BOOL)shouldPopToRoot {
    id appDelegate = [[UIApplication sharedApplication] delegate];
    UIWindow *window = [appDelegate valueForKey:@"window"];
    UITabBarController *tabbarController = (UITabBarController *)window.rootViewController;
    UIViewController *currentSelectVC = nil;
    if ([tabbarController isKindOfClass:UITabBarController.class]) {
        NSInteger circleIndex = 0;
        if (circleIndex >= 0 && tabbarController.selectedIndex != circleIndex) {
            currentSelectVC = tabbarController.selectedViewController;
#if __has_include("SYBaseTabBarController.h")
            [SYBaseTabBarController shareTabbarController].selectedTabIndexType = SYTabBarIndexTypeHome;
#endif
        }
    }
    
    if (shouldPopToRoot) {
        [[currentSelectVC imy_currentShowViewController].navigationController popToRootViewControllerAnimated:NO];
        [[UIViewController imy_currentViewControlloer].navigationController popToRootViewControllerAnimated:NO];
    }
}

#pragma mark - 重写父类 
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

#pragma mark - UITextFieldDelegate
- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    BOOL shouldReturn = imy_isNotBlankString(textField.text);
    if (shouldReturn) {
        [self modelFinishSelect:self.nicknameCellModel];
    }
    return shouldReturn;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self.view endEditing:YES];
    [self.checkPrivateView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.tableView.mas_bottom).offset(-56-SCREEN_TABBAR_SAFEBOTTOM_MARGIN - scrollView.contentOffset.y);
    }];
}


//MARK: - getter
- (SYChildPrivacyRulesView *)checkPrivateView{
    if (!_checkPrivateView) {
        _checkPrivateView = [SYChildPrivacyRulesView new];
        _checkPrivateView.checkSeleted = NO;
    }
    return _checkPrivateView;
}
@end
